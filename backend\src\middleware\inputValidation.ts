import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

/**
 * Middleware para sanitização e validação robusta de entrada
 */

// Regex mais restritivo para telefones brasileiros
const BRAZILIAN_PHONE_REGEX = /^(\+55\s?)?(\(?\d{2}\)?\s?)?(9\s?)?\d{4}-?\d{4}$/;

// Caracteres perigosos que devem ser removidos
const DANGEROUS_CHARS = /<script|javascript:|data:|vbscript:|onload|onerror|onclick|eval\(|expression\(/gi;

/**
 * Sanitiza string removendo caracteres perigosos
 */
const sanitizeString = (str: string): string => {
  if (typeof str !== 'string') return '';
  
  return str
    .replace(DANGEROUS_CHARS, '')
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove caracteres de controle
    .trim()
    .substring(0, 1000); // <PERSON>ita tamanho
};

/**
 * Validação robusta para contatos
 */
export const validateContact = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres')
    .matches(/^[a-zA-ZÀ-ÿ\s\-'\.]+$/)
    .withMessage('Nome contém caracteres inválidos')
    .customSanitizer(sanitizeString),
    
  body('phone')
    .trim()
    .matches(BRAZILIAN_PHONE_REGEX)
    .withMessage('Formato de telefone brasileiro inválido')
    .customSanitizer((value) => {
      // Normaliza telefone brasileiro
      const cleaned = value.replace(/\D/g, '');
      if (cleaned.length === 10) {
        return `(${cleaned.substring(0, 2)}) 9${cleaned.substring(2, 6)}-${cleaned.substring(6)}`;
      } else if (cleaned.length === 11) {
        return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
      }
      return value;
    }),
    
  body('baby_gender')
    .optional()
    .isIn(['male', 'female', 'unknown'])
    .withMessage('Gênero do bebê deve ser: male, female ou unknown'),
    
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido')
    .customSanitizer(sanitizeString),
    
  body('observations')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Observações devem ter no máximo 500 caracteres')
    .customSanitizer(sanitizeString)
];

/**
 * Validação para mensagens
 */
export const validateMessage = [
  body('phoneNumber')
    .trim()
    .matches(BRAZILIAN_PHONE_REGEX)
    .withMessage('Formato de telefone brasileiro inválido'),
    
  body('message')
    .trim()
    .isLength({ min: 1, max: 4096 })
    .withMessage('Mensagem deve ter entre 1 e 4096 caracteres')
    .customSanitizer(sanitizeString)
    .custom((value) => {
      // Verificar se não contém apenas espaços ou caracteres especiais
      if (!/[a-zA-ZÀ-ÿ0-9]/.test(value)) {
        throw new Error('Mensagem deve conter pelo menos um caractere alfanumérico');
      }
      return true;
    })
];

/**
 * Validação para autenticação
 */
export const validateAuth = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido')
    .customSanitizer(sanitizeString),
    
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Senha deve ter entre 8 e 128 caracteres')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Senha deve conter: 1 minúscula, 1 maiúscula, 1 número e 1 caractere especial')
];

/**
 * Middleware para verificar erros de validação
 */
export const checkValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined
    }));

    console.warn('❌ Erro de validação:', {
      path: req.path,
      method: req.method,
      errors: formattedErrors,
      ip: req.ip
    });

    return res.status(400).json({
      success: false,
      error: 'Dados inválidos',
      code: 'VALIDATION_ERROR',
      details: formattedErrors
    });
  }
  
  next();
};

/**
 * Middleware para sanitização geral do body
 */
export const sanitizeBody = (req: Request, res: Response, next: NextFunction) => {
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = sanitizeString(req.body[key]);
      }
    }
  }
  next();
};

/**
 * Middleware para limitar tamanho do payload
 */
export const limitPayloadSize = (maxSizeKB: number = 100) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('content-length') || '0');
    const maxSizeBytes = maxSizeKB * 1024;
    
    if (contentLength > maxSizeBytes) {
      return res.status(413).json({
        success: false,
        error: `Payload muito grande. Máximo permitido: ${maxSizeKB}KB`,
        code: 'PAYLOAD_TOO_LARGE'
      });
    }
    
    next();
  };
};

/**
 * Middleware para rate limiting por IP
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export const rateLimitByIP = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    
    const record = requestCounts.get(ip);
    
    if (!record || now > record.resetTime) {
      // Novo período ou primeiro acesso
      requestCounts.set(ip, { count: 1, resetTime: now + windowMs });
      return next();
    }
    
    if (record.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: 'Muitas requisições. Tente novamente mais tarde.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((record.resetTime - now) / 1000)
      });
    }
    
    record.count++;
    next();
  };
};

/**
 * Limpeza periódica do rate limiting
 */
setInterval(() => {
  const now = Date.now();
  for (const [ip, record] of requestCounts.entries()) {
    if (now > record.resetTime) {
      requestCounts.delete(ip);
    }
  }
}, 5 * 60 * 1000); // Limpa a cada 5 minutos
