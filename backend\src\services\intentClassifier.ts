/**
 * Serviço de Classificação de Intenções
 * Classifica automaticamente as consultas dos cidadãos por categoria e tipo
 */

import { geminiService } from './gemini';

export interface Intent {
  id: string;
  name: string;
  category: 'health' | 'education' | 'infrastructure' | 'transparency' | 'legislation' | 'other';
  confidence: number;
  keywords: string[];
  examples: string[];
}

export interface ClassificationResult {
  intent: Intent;
  confidence: number;
  reasoning: string;
  suggestedActions: string[];
  requiresHuman: boolean;
}

export interface ClassificationOptions {
  threshold?: number;
  includeReasoning?: boolean;
  suggestActions?: boolean;
}

export class IntentClassifier {
  private geminiService = geminiService;
  private predefinedIntents: Intent[] = [];

  constructor() {
    // Usar instância singleton
    this.initializePredefinedIntents();
  }

  /**
   * Inicializar intenções predefinidas
   */
  private initializePredefinedIntents(): void {
    this.predefinedIntents = [
      {
        id: 'health_service_request',
        name: 'Solicitação de Serviço de Saúde',
        category: 'health',
        confidence: 0,
        keywords: ['saúde', 'hospital', 'médico', 'consulta', 'exame', 'posto', 'ubs', 'sus'],
        examples: [
          'Como marcar consulta no posto de saúde?',
          'Onde fica o hospital mais próximo?',
          'Como solicitar exames pelo SUS?'
        ]
      },
      {
        id: 'education_inquiry',
        name: 'Consulta sobre Educação',
        category: 'education',
        confidence: 0,
        keywords: ['escola', 'educação', 'matrícula', 'creche', 'ensino', 'professor', 'aluno'],
        examples: [
          'Como fazer matrícula na escola?',
          'Quando abrem as inscrições para creche?',
          'Qual o calendário escolar?'
        ]
      },
      {
        id: 'infrastructure_complaint',
        name: 'Reclamação de Infraestrutura',
        category: 'infrastructure',
        confidence: 0,
        keywords: ['buraco', 'asfalto', 'rua', 'calçada', 'iluminação', 'poda', 'árvore', 'lixo'],
        examples: [
          'Tem um buraco na minha rua',
          'A iluminação está queimada',
          'Como solicitar poda de árvore?'
        ]
      },
      {
        id: 'legislation_inquiry',
        name: 'Consulta sobre Legislação',
        category: 'legislation',
        confidence: 0,
        keywords: ['lei', 'projeto', 'vereadora', 'câmara', 'votação', 'proposta'],
        examples: [
          'Quais projetos a vereadora apresentou?',
          'Como funciona a lei de acessibilidade?',
          'Quando será votado o projeto?'
        ]
      },
      {
        id: 'transparency_request',
        name: 'Solicitação de Transparência',
        category: 'transparency',
        confidence: 0,
        keywords: ['orçamento', 'gasto', 'verba', 'transparência', 'prestação', 'conta', 'audiência'],
        examples: [
          'Qual o orçamento da saúde?',
          'Como são gastos os recursos?',
          'Quando será a audiência pública?'
        ]
      },
      {
        id: 'general_information',
        name: 'Informação Geral',
        category: 'other',
        confidence: 0,
        keywords: ['informação', 'contato', 'telefone', 'endereço', 'horário', 'funcionamento'],
        examples: [
          'Qual o telefone do gabinete?',
          'Qual o horário de atendimento?',
          'Onde fica o gabinete?'
        ]
      }
    ];
  }

  /**
   * Classificar intenção de uma consulta
   */
  async classifyIntent(
    query: string,
    options: ClassificationOptions = {}
  ): Promise<ClassificationResult> {
    try {
      const {
        threshold = 0.7,
        includeReasoning = true,
        suggestActions = true
      } = options;

      console.log(`🎯 Classificando intenção: "${query}"`);

      // 1. Classificação baseada em keywords
      const keywordClassification = this.classifyByKeywords(query);

      // 2. Classificação com IA
      const aiClassification = await this.classifyWithAI(query, includeReasoning);

      // 3. Combinar resultados
      const finalClassification = this.combineClassifications(
        keywordClassification,
        aiClassification,
        threshold
      );

      // 4. Gerar ações sugeridas
      const suggestedActions = suggestActions 
        ? this.generateSuggestedActions(finalClassification.intent, query)
        : [];

      // 5. Determinar se requer intervenção humana
      const requiresHuman = this.shouldRequireHuman(finalClassification, query);

      console.log(`✅ Intenção classificada: ${finalClassification.intent.name} (${(finalClassification.confidence * 100).toFixed(1)}%)`);

      return {
        intent: finalClassification.intent,
        confidence: finalClassification.confidence,
        reasoning: aiClassification.reasoning || 'Classificação baseada em palavras-chave',
        suggestedActions,
        requiresHuman
      };

    } catch (error) {
      console.error('❌ Erro na classificação de intenção:', error);
      
      // Fallback para intenção geral
      return {
        intent: this.predefinedIntents.find(i => i.id === 'general_information')!,
        confidence: 0.3,
        reasoning: 'Erro na classificação - usando fallback',
        suggestedActions: ['Encaminhar para atendimento humano'],
        requiresHuman: true
      };
    }
  }

  /**
   * Classificação baseada em palavras-chave
   */
  private classifyByKeywords(query: string): { intent: Intent; confidence: number } {
    const lowerQuery = query.toLowerCase();
    let bestMatch = this.predefinedIntents[this.predefinedIntents.length - 1]; // default: general_information
    let bestScore = 0;

    for (const intent of this.predefinedIntents) {
      let score = 0;
      let matchedKeywords = 0;

      for (const keyword of intent.keywords) {
        if (lowerQuery.includes(keyword.toLowerCase())) {
          score += 1;
          matchedKeywords++;
        }
      }

      // Normalizar score
      const normalizedScore = matchedKeywords > 0 ? score / intent.keywords.length : 0;

      if (normalizedScore > bestScore) {
        bestScore = normalizedScore;
        bestMatch = intent;
      }
    }

    return {
      intent: { ...bestMatch, confidence: bestScore },
      confidence: bestScore
    };
  }

  /**
   * Classificação com IA
   */
  private async classifyWithAI(
    query: string,
    includeReasoning: boolean
  ): Promise<{ intent: Intent; confidence: number; reasoning?: string }> {
    try {
      const prompt = `Analise a seguinte consulta de um cidadão e classifique a intenção:

CONSULTA: "${query}"

CATEGORIAS DISPONÍVEIS:
1. health - Saúde (hospitais, consultas, exames, SUS)
2. education - Educação (escolas, matrículas, creches)
3. infrastructure - Infraestrutura (ruas, buracos, iluminação, poda)
4. legislation - Legislação (leis, projetos, vereadora)
5. transparency - Transparência (orçamento, gastos, audiências)
6. other - Outros (informações gerais, contatos)

Responda APENAS no formato JSON:
{
  "category": "categoria_identificada",
  "confidence": 0.85,
  "reasoning": "explicação_da_classificação"
}`;

      const response = await this.geminiService.generateRAGResponse(prompt);
      
      // Tentar extrair JSON da resposta
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        
        // Encontrar intenção correspondente
        const matchedIntent = this.predefinedIntents.find(
          intent => intent.category === result.category
        ) || this.predefinedIntents[this.predefinedIntents.length - 1];

        return {
          intent: { ...matchedIntent, confidence: result.confidence },
          confidence: result.confidence,
          reasoning: includeReasoning ? result.reasoning : undefined
        };
      }

      throw new Error('Resposta da IA não está no formato esperado');

    } catch (error) {
      console.error('❌ Erro na classificação com IA:', error);
      
      // Fallback
      return {
        intent: this.predefinedIntents[this.predefinedIntents.length - 1],
        confidence: 0.3
      };
    }
  }

  /**
   * Combinar classificações
   */
  private combineClassifications(
    keywordResult: { intent: Intent; confidence: number },
    aiResult: { intent: Intent; confidence: number },
    threshold: number
  ): { intent: Intent; confidence: number } {
    // Se ambas concordam na categoria, usar a maior confiança
    if (keywordResult.intent.category === aiResult.intent.category) {
      const maxConfidence = Math.max(keywordResult.confidence, aiResult.confidence);
      return {
        intent: keywordResult.confidence > aiResult.confidence ? keywordResult.intent : aiResult.intent,
        confidence: maxConfidence
      };
    }

    // Se discordam, usar a com maior confiança se acima do threshold
    if (aiResult.confidence >= threshold) {
      return aiResult;
    }

    if (keywordResult.confidence >= threshold) {
      return keywordResult;
    }

    // Se ambas estão abaixo do threshold, usar IA com confiança reduzida
    return {
      intent: aiResult.intent,
      confidence: Math.min(aiResult.confidence, 0.5)
    };
  }

  /**
   * Gerar ações sugeridas
   */
  private generateSuggestedActions(intent: Intent, query: string): string[] {
    const actions: Record<string, string[]> = {
      health: [
        'Buscar informações sobre serviços de saúde',
        'Fornecer contatos de UBS e hospitais',
        'Orientar sobre agendamento no SUS'
      ],
      education: [
        'Consultar calendário escolar',
        'Fornecer informações sobre matrículas',
        'Orientar sobre programas educacionais'
      ],
      infrastructure: [
        'Registrar solicitação de reparo',
        'Fornecer protocolo de acompanhamento',
        'Orientar sobre prazos de atendimento'
      ],
      legislation: [
        'Buscar projetos da vereadora',
        'Explicar processo legislativo',
        'Fornecer agenda da Câmara'
      ],
      transparency: [
        'Consultar portal da transparência',
        'Agendar audiência pública',
        'Fornecer relatórios de gastos'
      ],
      other: [
        'Fornecer informações de contato',
        'Orientar sobre horários de atendimento',
        'Encaminhar para setor apropriado'
      ]
    };

    return actions[intent.category] || actions.other;
  }

  /**
   * Determinar se requer intervenção humana
   */
  private shouldRequireHuman(
    classification: { intent: Intent; confidence: number },
    query: string
  ): boolean {
    // Baixa confiança
    if (classification.confidence < 0.6) {
      return true;
    }

    // Consultas complexas ou sensíveis
    const sensitiveKeywords = [
      'urgente', 'emergência', 'reclamação', 'denúncia', 
      'problema grave', 'não funciona', 'péssimo'
    ];

    const lowerQuery = query.toLowerCase();
    const hasSensitiveContent = sensitiveKeywords.some(keyword => 
      lowerQuery.includes(keyword)
    );

    return hasSensitiveContent;
  }

  /**
   * Obter estatísticas de classificação
   */
  getIntentStatistics(): {
    totalIntents: number;
    intentsByCategory: Record<string, number>;
    availableIntents: Intent[];
  } {
    const intentsByCategory: Record<string, number> = {};
    
    this.predefinedIntents.forEach(intent => {
      intentsByCategory[intent.category] = (intentsByCategory[intent.category] || 0) + 1;
    });

    return {
      totalIntents: this.predefinedIntents.length,
      intentsByCategory,
      availableIntents: this.predefinedIntents
    };
  }
}

// Instância singleton
export const intentClassifier = new IntentClassifier();
