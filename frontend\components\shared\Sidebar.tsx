
import React from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { APP_NAME, ICONS } from '../../constants';
import { authService } from '../../services/authService';

interface SidebarProps {
  isOpen: boolean;
  isCollapsed?: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isMobile: boolean;
}

const navItems = [
  { name: 'Dashboard', path: '/dashboard', icon: ICONS.dashboard },
  { name: 'Gestantes', path: '/gestantes', icon: ICONS.pregnant },
  { name: 'WhatsApp & IA', path: '/whatsapp', icon: ICONS.whatsapp },
  { name: 'WhatsApp Auto', path: '/whatsapp-auto', icon: ICONS.microphone },
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, isCollapsed = false, setIsOpen, isMobile }) => {
  const navigate = useNavigate();
  const activeClassName = "bg-primary text-white shadow-lg";
  const inactiveClassName = "text-gray-300 hover:bg-neutral-dark hover:text-white";

  // Determinar se deve mostrar expandido
  const isExpanded = isMobile ? isOpen : !isCollapsed;

  const handleLogout = () => {
    try {
      authService.logout();
      toast.success('Logout realizado com sucesso!');
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Erro no logout:', error);
      toast.error('Erro ao fazer logout');
    }
  };

  const handleNavClick = () => {
    if (isMobile) {
      setIsOpen(false);
    }
  };

  return (
    <div
      data-testid="sidebar"
      className={`
        bg-neutral-dark text-white transition-all duration-300 ease-in-out flex flex-col shadow-xl
        ${isExpanded ? 'w-64' : 'w-20'}
        ${isMobile ? 'fixed' : 'fixed lg:relative'} z-30 h-full
        ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
        ${!isMobile && isCollapsed ? 'hover:w-64 hover:shadow-2xl' : ''}
      `}
    >
      {/* Header com Logo */}
      <div className={`flex flex-col items-center p-4 border-b border-neutral-light border-opacity-20 transition-all duration-300 ${
        isExpanded ? 'min-h-[120px]' : 'h-16 justify-center'
      }`}>
        {isExpanded && (
          <>
            <span className="text-xl font-semibold mb-3 text-center">{APP_NAME}</span>
            <img
              src="/LogoRafa.png"
              alt="Logo Rafaela Cuida"
              className="w-16 h-16 object-contain rounded-lg transition-all duration-300 hover:scale-110"
            />
          </>
        )}
        {!isExpanded && (
          <img
            src="/LogoRafa.png"
            alt="Logo Rafaela Cuida"
            className="w-10 h-10 object-contain rounded-lg transition-all duration-300 hover:scale-110"
            title={APP_NAME}
          />
        )}
      </div>
      {/* Navigation */}
      <nav className="flex-1 mt-4 px-2">
        {navItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            onClick={handleNavClick}
            className={({ isActive }) =>
              `flex items-center px-3 py-3 my-1 mx-1 transition-all duration-200 transform rounded-lg group relative ${
                isActive ? activeClassName : inactiveClassName
              } ${!isExpanded ? 'justify-center' : ''} hover:scale-105`
            }
            title={!isExpanded ? item.name : ''}
          >
            <span className="text-xl">{item.icon}</span>
            {isExpanded && <span className="ml-4 font-medium">{item.name}</span>}

            {/* Tooltip para modo collapsed */}
            {!isExpanded && (
              <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                {item.name}
              </div>
            )}
          </NavLink>
        ))}

        {/* Separador */}
        <div className="border-t border-neutral-light border-opacity-20 my-4 mx-2"></div>

        {/* Botão de Logout */}
        <button
          onClick={handleLogout}
          className={`flex items-center px-3 py-3 my-1 mx-1 transition-all duration-200 transform rounded-lg w-full text-left group relative ${inactiveClassName} hover:bg-red-600 hover:text-white ${
            !isExpanded ? 'justify-center' : ''
          } hover:scale-105`}
          title={!isExpanded ? 'Sair' : ''}
        >
          <span className="text-xl">{ICONS.logout}</span>
          {isExpanded && <span className="ml-4 font-medium">Sair</span>}

          {/* Tooltip para modo collapsed */}
          {!isExpanded && (
            <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              Sair
            </div>
          )}
        </button>
      </nav>
      {/* Footer */}
      <div className={`p-4 border-t border-neutral-light border-opacity-20 transition-all duration-300 ${
        isExpanded ? 'opacity-100' : 'opacity-0 h-0 p-0 overflow-hidden'
      }`}>
        <p className="text-xs text-gray-400 text-center">© {new Date().getFullYear()} Gestão Materna</p>
      </div>
    </div>
  );
};

export default Sidebar;
    