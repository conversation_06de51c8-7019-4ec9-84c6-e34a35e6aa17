
import React from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { APP_NAME, ICONS } from '../../constants';
import { authService } from '../../services/authService';

interface SidebarProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const navItems = [
  { name: 'Dashboard', path: '/dashboard', icon: ICONS.dashboard },
  { name: 'Gestantes', path: '/gestantes', icon: ICONS.pregnant },
  { name: 'WhatsApp & IA', path: '/whatsapp', icon: ICONS.whatsapp },
  { name: 'WhatsApp Auto', path: '/whatsapp-auto', icon: ICONS.microphone },
];

const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen }) => {
  const navigate = useNavigate();
  const activeClassName = "bg-primary text-white";
  const inactiveClassName = "text-gray-300 hover:bg-neutral-dark hover:text-white";

  const handleLogout = () => {
    try {
      authService.logout();
      toast.success('Logout realizado com sucesso!');
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Erro no logout:', error);
      toast.error('Erro ao fazer logout');
    }
  };

  return (
    <div
      data-testid="sidebar"
      className={`
        bg-neutral-dark text-white transition-all duration-300 ease-in-out flex flex-col
        ${isOpen ? 'w-64' : 'w-20'}
        fixed lg:relative z-30 h-full lg:h-auto
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}
    >
      <div className={`flex flex-col items-center p-4 border-b border-neutral-light border-opacity-20 ${isOpen ? 'min-h-[120px]' : 'h-16 justify-center'}`}>
        {isOpen && (
          <>
            <span className="text-xl font-semibold mb-3">{APP_NAME}</span>
            <img
              src="/LogoRafa.png"
              alt="Logo Rafaela Cuida"
              className="w-16 h-16 object-contain rounded-lg"
            />
          </>
        )}
        {!isOpen && (
          <img
            src="/LogoRafa.png"
            alt="Logo Rafaela Cuida"
            className="w-10 h-10 object-contain rounded-lg"
          />
        )}
        {/* <button onClick={() => setIsOpen(!isOpen)} className="p-1 rounded-md hover:bg-neutral-dark focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white md:hidden">
           {isOpen ? ICONS.close : <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" /></svg>}
        </button> */}
      </div>
      <nav className="flex-1 mt-4">
        {navItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center px-4 py-3 my-1 transition-colors duration-200 transform rounded-md ${ isActive ? activeClassName : inactiveClassName } ${isOpen ? '' : 'justify-center'}`
            }
            title={isOpen ? '' : item.name}
          >
            {item.icon}
            {isOpen && <span className="mx-4 font-medium">{item.name}</span>}
          </NavLink>
        ))}

        {/* Botão de Logout */}
        <button
          onClick={handleLogout}
          className={`flex items-center px-4 py-3 my-1 transition-colors duration-200 transform rounded-md w-full text-left ${inactiveClassName} hover:bg-red-600 hover:text-white ${isOpen ? '' : 'justify-center'}`}
          title={isOpen ? '' : 'Sair'}
        >
          {ICONS.logout}
          {isOpen && <span className="mx-4 font-medium">Sair</span>}
        </button>
      </nav>
      <div className={`p-4 border-t border-neutral-light border-opacity-20 ${isOpen ? '' : 'hidden'}`}>
        <p className="text-xs text-gray-400">© {new Date().getFullYear()} Gestão Materna</p>
      </div>
    </div>
  );
};

export default Sidebar;
    