
import React from 'react';

interface PageTitleProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode; // For buttons or other actions
}

const PageTitle: React.FC<PageTitleProps> = ({ title, subtitle, children }) => {
  return (
    <div className="mb-6 flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 animate-fade-in">
      <div className="flex-1 min-w-0">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-neutral-dark mb-1 truncate">
          {title}
        </h1>
        {subtitle && (
          <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
            {subtitle}
          </p>
        )}
      </div>
      {children && (
        <div className="flex-shrink-0 w-full lg:w-auto">
          <div className="flex flex-col sm:flex-row gap-2 lg:gap-3">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

export default PageTitle;
    