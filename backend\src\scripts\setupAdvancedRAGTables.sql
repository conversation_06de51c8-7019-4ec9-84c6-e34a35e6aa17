-- Script SQL para criar tabelas do sistema RAG avançado

-- Tabela para feedback dos usuários
CREATE TABLE IF NOT EXISTS user_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    query TEXT NOT NULL,
    response TEXT NOT NULL,
    sources TEXT[] DEFAULT '{}',
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    is_helpful BOOLEAN DEFAULT false,
    user_comment TEXT,
    response_time INTEGER, -- em millisegundos
    confidence DECIMAL(3,2), -- 0.00 a 1.00
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_user_feedback_session_id ON user_feedback(session_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_rating ON user_feedback(rating);
CREATE INDEX IF NOT EXISTS idx_user_feedback_created_at ON user_feedback(created_at);
CREATE INDEX IF NOT EXISTS idx_user_feedback_is_helpful ON user_feedback(is_helpful);

-- Tabela para jobs de indexação
CREATE TABLE IF NOT EXISTS indexing_jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('full', 'incremental')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    documents_processed INTEGER DEFAULT 0,
    documents_added INTEGER DEFAULT 0,
    documents_updated INTEGER DEFAULT 0,
    documents_deleted INTEGER DEFAULT 0,
    errors TEXT[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Índices para jobs
CREATE INDEX IF NOT EXISTS idx_indexing_jobs_job_id ON indexing_jobs(job_id);
CREATE INDEX IF NOT EXISTS idx_indexing_jobs_status ON indexing_jobs(status);
CREATE INDEX IF NOT EXISTS idx_indexing_jobs_start_time ON indexing_jobs(start_time);
CREATE INDEX IF NOT EXISTS idx_indexing_jobs_type ON indexing_jobs(type);

-- Tabela para métricas de performance
CREATE TABLE IF NOT EXISTS rag_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    total_queries INTEGER DEFAULT 0,
    avg_response_time DECIMAL(8,2), -- em millisegundos
    avg_confidence DECIMAL(3,2),
    avg_rating DECIMAL(3,2),
    success_rate DECIMAL(3,2), -- porcentagem
    cache_hit_rate DECIMAL(3,2), -- porcentagem
    total_documents INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(date)
);

-- Índice para métricas
CREATE INDEX IF NOT EXISTS idx_rag_metrics_date ON rag_metrics(date);

-- Tabela para padrões de aprendizado
CREATE TABLE IF NOT EXISTS learning_patterns (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pattern_key VARCHAR(255) UNIQUE NOT NULL,
    pattern_type VARCHAR(100) NOT NULL,
    frequency INTEGER DEFAULT 1,
    avg_rating DECIMAL(3,2),
    success_rate DECIMAL(3,2),
    common_queries TEXT[] DEFAULT '{}',
    improvement_suggestions TEXT[] DEFAULT '{}',
    last_updated TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Índices para padrões
CREATE INDEX IF NOT EXISTS idx_learning_patterns_pattern_key ON learning_patterns(pattern_key);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_pattern_type ON learning_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_frequency ON learning_patterns(frequency);

-- Tabela para configurações do sistema RAG
CREATE TABLE IF NOT EXISTS rag_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Inserir configurações padrão
INSERT INTO rag_config (config_key, config_value, description) VALUES
('similarity_threshold', '0.5', 'Threshold mínimo de similaridade para busca'),
('max_results', '15', 'Número máximo de resultados por busca'),
('cache_enabled', 'true', 'Habilitar cache semântico'),
('hybrid_embeddings', 'true', 'Usar embeddings híbridos'),
('reranking_enabled', 'true', 'Habilitar re-ranking de resultados'),
('query_expansion', 'true', 'Habilitar expansão de queries'),
('chroma_db_enabled', 'true', 'Usar ChromaDB como vector database'),
('auto_indexing_interval', '30', 'Intervalo de indexação automática em minutos')
ON CONFLICT (config_key) DO NOTHING;

-- Tabela para sessões de usuário (para tracking)
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    queries_count INTEGER DEFAULT 0,
    avg_rating DECIMAL(3,2),
    total_response_time INTEGER DEFAULT 0,
    started_at TIMESTAMP DEFAULT NOW(),
    last_activity TIMESTAMP DEFAULT NOW(),
    ended_at TIMESTAMP
);

-- Índices para sessões
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_started_at ON user_sessions(started_at);

-- Função para atualizar timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_user_feedback_updated_at 
    BEFORE UPDATE ON user_feedback 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rag_config_updated_at 
    BEFORE UPDATE ON rag_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para calcular métricas diárias
CREATE OR REPLACE FUNCTION calculate_daily_metrics(target_date DATE DEFAULT CURRENT_DATE)
RETURNS VOID AS $$
DECLARE
    metrics_data RECORD;
BEGIN
    -- Calcular métricas do dia
    SELECT 
        COUNT(*) as total_queries,
        AVG(response_time) as avg_response_time,
        AVG(confidence) as avg_confidence,
        AVG(rating) as avg_rating,
        (COUNT(*) FILTER (WHERE is_helpful = true))::DECIMAL / COUNT(*) as success_rate
    INTO metrics_data
    FROM user_feedback 
    WHERE DATE(created_at) = target_date;

    -- Inserir ou atualizar métricas
    INSERT INTO rag_metrics (
        date, 
        total_queries, 
        avg_response_time, 
        avg_confidence, 
        avg_rating, 
        success_rate
    ) VALUES (
        target_date,
        metrics_data.total_queries,
        metrics_data.avg_response_time,
        metrics_data.avg_confidence,
        metrics_data.avg_rating,
        metrics_data.success_rate
    )
    ON CONFLICT (date) DO UPDATE SET
        total_queries = EXCLUDED.total_queries,
        avg_response_time = EXCLUDED.avg_response_time,
        avg_confidence = EXCLUDED.avg_confidence,
        avg_rating = EXCLUDED.avg_rating,
        success_rate = EXCLUDED.success_rate;
END;
$$ LANGUAGE plpgsql;

-- Comentários nas tabelas
COMMENT ON TABLE user_feedback IS 'Feedback dos usuários sobre respostas do sistema RAG';
COMMENT ON TABLE indexing_jobs IS 'Histórico de jobs de indexação de documentos';
COMMENT ON TABLE rag_metrics IS 'Métricas diárias de performance do sistema RAG';
COMMENT ON TABLE learning_patterns IS 'Padrões identificados pelo sistema de aprendizado';
COMMENT ON TABLE rag_config IS 'Configurações do sistema RAG';
COMMENT ON TABLE user_sessions IS 'Sessões de usuário para tracking e analytics';

-- Criar view para relatórios
CREATE OR REPLACE VIEW rag_performance_summary AS
SELECT 
    DATE_TRUNC('week', date) as week,
    AVG(total_queries) as avg_weekly_queries,
    AVG(avg_response_time) as avg_response_time,
    AVG(avg_confidence) as avg_confidence,
    AVG(avg_rating) as avg_rating,
    AVG(success_rate) as avg_success_rate
FROM rag_metrics 
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE_TRUNC('week', date)
ORDER BY week DESC;

COMMENT ON VIEW rag_performance_summary IS 'Resumo semanal de performance do sistema RAG';

-- Conceder permissões (ajustar conforme necessário)
-- GRANT ALL ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO your_app_user;
