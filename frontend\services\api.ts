import { authService } from './authService';
import type { Contact, Message, WhatsAppStatus } from '../types';

// Usar o cliente autenticado do authService
const api = authService.getAuthenticatedClient();

export type { Contact, Message, WhatsAppStatus };

export const apiService = {
  // Contatos
  getContacts: async (): Promise<Contact[]> => {
    const response = await api.get('/contacts');
    return response.data;
  },

  createContact: async (contact: Partial<Contact>): Promise<Contact> => {
    console.log('📝 Enviando dados para criar contato:', contact);
    const response = await api.post('/contacts', contact);
    console.log('✅ Resposta do servidor:', response.data);
    return response.data;
  },

  updateContact: async (id: string, contact: Partial<Contact>): Promise<Contact> => {
    const response = await api.put(`/contacts/${id}`, contact);
    return response.data;
  },

  deleteContact: async (id: string): Promise<void> => {
    await api.delete(`/contacts/${id}`);
  },

  // Mensagens
  getMessages: async (contactId: string): Promise<Message[]> => {
    const response = await api.get(`/contacts/${contactId}/messages`);
    return response.data;
  },

  sendMessage: async (contactId: string, content: string): Promise<Message> => {
    const response = await api.post(`/contacts/${contactId}/messages`, { content });
    return response.data;
  },

  // WhatsApp
  getWhatsAppStatus: async (): Promise<WhatsAppStatus> => {
    const response = await api.get('/whatsapp/status');
    return response.data;
  },

  processMessageWithAI: async (contactId: string, message: string): Promise<Message> => {
    const response = await api.post(`/contacts/${contactId}/process`, { message });
    return response.data;
  },

  // Acompanhamento
  generateFollowUp: async (contactId: string): Promise<{ message: string }> => {
    const response = await api.post(`/ai/generate-followup`, { contactId });
    return response.data;
  },

  // NOVO: Processar mensagem com áudio
  processAudioMessage: async (contactId: string, audioFile: File, message?: string): Promise<{
    success: boolean;
    response: string;
    sentiment?: any;
    needs?: string[];
    suggestions?: string[];
    audioProcessed: boolean;
    timestamp: string;
  }> => {
    const formData = new FormData();
    formData.append('audio', audioFile);
    formData.append('contactId', contactId);
    if (message) {
      formData.append('message', message);
    }

    const response = await api.post('/chat/audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }
};