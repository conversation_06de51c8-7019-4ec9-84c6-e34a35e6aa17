import React, { useState, useEffect } from 'react';
import Modal from '../shared/Modal';
import Input, { Textarea } from '../shared/Input';
import Button from '../shared/Button';
import type { Contact } from '../../src/services/api';
import { normalizeBrazilianPhoneNumber, formatBrazilianPhoneNumberForDisplay } from '../../utils/phoneUtils';

interface PregnantFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (contact: Partial<Contact>) => Promise<void>;
  contact?: Contact | null;
}

const PregnantFormModal: React.FC<PregnantFormModalProps> = ({ isOpen, onClose, onSave, contact }) => {
  const [formData, setFormData] = useState<Partial<Contact>>({
    name: '',
    phone: '',
    babyGender: 'unknown'
  });

  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name || '',
        phone: contact.phone || '',
        babyGender: contact.babyGender || contact.baby_gender || 'unknown'
      });
    } else {
      setFormData({
        name: '',
        phone: '',
        babyGender: 'unknown'
      });
    }
  }, [contact]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(formData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">
          {contact ? 'Editar Gestante' : 'Nova Gestante'}
        </h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">Nome da Gestante</label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="Digite o nome completo"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">Telefone (WhatsApp)</label>
            <input
              type="tel"
              value={formData.phone || ''}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              placeholder="(84) 99999-9999"
              required
            />
            <p className="mt-1 text-sm text-gray-500">
              Formato: (DDD) 9XXXX-XXXX
            </p>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700">Gênero do Bebê</label>
            <select
              value={formData.babyGender || 'unknown'}
              onChange={(e) => setFormData({ ...formData, babyGender: e.target.value as 'male' | 'female' | 'unknown' })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              <option value="unknown">Não sei ainda / Não quero informar</option>
              <option value="male">Menino 👶</option>
              <option value="female">Menina 👶</option>
            </select>
            <p className="mt-1 text-sm text-gray-500">
              Esta informação é opcional e pode ser alterada depois
            </p>
          </div>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
            >
              Salvar
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PregnantFormModal;
