import React, { useState, useRef, useEffect } from 'react';
import { MicrophoneIcon, StopIcon, PlayIcon, PauseIcon, TrashIcon } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';

interface AudioRecorderProps {
  onAudioReady: (audioFile: File) => void;
  onCancel?: () => void;
  disabled?: boolean;
  maxDuration?: number; // em segundos
}

export const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onAudioReady,
  onCancel,
  disabled = false,
  maxDuration = 300 // 5 minutos por padrão
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Limpar recursos ao desmontar
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Iniciar gravação
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        } 
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/webm;codecs=opus' });
        setAudioBlob(blob);
        
        // Criar URL para reprodução
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);

        // Parar todas as tracks do stream
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start(1000); // Capturar dados a cada segundo
      setIsRecording(true);
      setRecordingTime(0);

      // Timer para contagem regressiva
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          if (newTime >= maxDuration) {
            stopRecording();
            toast.error(`Gravação limitada a ${maxDuration / 60} minutos`);
          }
          return newTime;
        });
      }, 1000);

      toast.success('Gravação iniciada! 🎤');
    } catch (error) {
      console.error('Erro ao iniciar gravação:', error);
      toast.error('Erro ao acessar o microfone. Verifique as permissões.');
    }
  };

  // Parar gravação
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      toast.success('Gravação finalizada! ✅');
    }
  };

  // Reproduzir áudio
  const playAudio = () => {
    if (audioUrl && audioRef.current) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // Pausar áudio
  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  // Descartar gravação
  const discardRecording = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
    }
    setAudioBlob(null);
    setAudioUrl(null);
    setRecordingTime(0);
    setIsPlaying(false);
    
    if (onCancel) {
      onCancel();
    }
    
    toast.success('Gravação descartada');
  };

  // Confirmar e enviar áudio
  const confirmAudio = () => {
    if (audioBlob) {
      // Converter blob para File
      const audioFile = new File([audioBlob], `audio_${Date.now()}.webm`, {
        type: 'audio/webm;codecs=opus'
      });
      
      onAudioReady(audioFile);
      toast.success('Áudio pronto para envio! 🎵');
    }
  };

  // Formatar tempo
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          🎤 Gravação de Áudio
        </h3>
        <div className="text-sm text-gray-500">
          {formatTime(recordingTime)} / {formatTime(maxDuration)}
        </div>
      </div>

      {/* Visualizador de status */}
      <div className="flex items-center justify-center space-x-4">
        {isRecording && (
          <div className="flex items-center space-x-2 text-red-600">
            <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse"></div>
            <span className="font-medium">Gravando...</span>
          </div>
        )}
        
        {audioBlob && !isRecording && (
          <div className="flex items-center space-x-2 text-green-600">
            <div className="w-3 h-3 bg-green-600 rounded-full"></div>
            <span className="font-medium">Gravação pronta</span>
          </div>
        )}
      </div>

      {/* Controles de gravação */}
      <div className="flex items-center justify-center space-x-3">
        {!isRecording && !audioBlob && (
          <button
            onClick={startRecording}
            disabled={disabled}
            className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <MicrophoneIcon className="w-5 h-5" />
            <span>Iniciar Gravação</span>
          </button>
        )}

        {isRecording && (
          <button
            onClick={stopRecording}
            className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <StopIcon className="w-5 h-5" />
            <span>Parar</span>
          </button>
        )}

        {audioBlob && !isRecording && (
          <>
            <button
              onClick={isPlaying ? pauseAudio : playAudio}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg transition-colors"
            >
              {isPlaying ? (
                <PauseIcon className="w-5 h-5" />
              ) : (
                <PlayIcon className="w-5 h-5" />
              )}
            </button>

            <button
              onClick={discardRecording}
              className="flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors"
            >
              <TrashIcon className="w-5 h-5" />
            </button>

            <button
              onClick={confirmAudio}
              disabled={disabled}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <span>✅ Usar Áudio</span>
            </button>
          </>
        )}
      </div>

      {/* Player de áudio oculto */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onEnded={() => setIsPlaying(false)}
          onPause={() => setIsPlaying(false)}
          className="hidden"
        />
      )}

      {/* Informações */}
      <div className="text-xs text-gray-500 text-center space-y-1">
        <p>• Clique em "Iniciar Gravação" para começar</p>
        <p>• Máximo: {Math.floor(maxDuration / 60)} minutos por gravação</p>
        <p>• Você pode ouvir antes de enviar</p>
      </div>
    </div>
  );
};
