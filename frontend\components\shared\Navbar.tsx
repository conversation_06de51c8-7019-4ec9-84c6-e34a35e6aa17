
import React from 'react';
import { ICONS, DEFAULT_API_KEY_NOTICE } from '../../constants';

interface NavbarProps {
  toggleSidebar: () => void;
  apiKeyStatus: string;
  isMobile: boolean;
  sidebarCollapsed: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar, apiKeyStatus, isMobile, sidebarCollapsed }) => {
  return (
    <header className="h-16 bg-white shadow-md flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10 border-b border-gray-200">
      {/* Left Section - Toggle Button */}
      <div className="flex items-center">
        <button
          data-testid="sidebar-toggle"
          onClick={toggleSidebar}
          className="text-gray-500 focus:outline-none hover:text-primary p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 hover:scale-105"
          aria-label={isMobile ? "Toggle sidebar" : (sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar")}
        >
          {isMobile ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              {sidebarCollapsed ? (
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              )}
            </svg>
          )}
        </button>

        {/* Breadcrumb ou título da página (opcional) */}
        <div className="hidden sm:block ml-4">
          <h1 className="text-lg font-semibold text-gray-800">Rafaela Cuida</h1>
        </div>
      </div>

      {/* Right Section - Status and User */}
      <div className="flex items-center space-x-2 sm:space-x-3 lg:space-x-4">
        {/* API Status */}
        <div className={`
          text-xs sm:text-sm flex items-center px-2 py-1 rounded-full transition-all duration-200
          ${apiKeyStatus === DEFAULT_API_KEY_NOTICE
            ? 'text-red-600 bg-red-50 border border-red-200'
            : 'text-green-600 bg-green-50 border border-green-200'
          }
        `}>
          <span className="mr-1 sm:mr-2">
            {apiKeyStatus === DEFAULT_API_KEY_NOTICE ? ICONS.warning : ICONS.checkCircle}
          </span>
          <span className="hidden sm:inline-block truncate max-w-24 md:max-w-32 lg:max-w-none">
            {apiKeyStatus}
          </span>
          <span className="sm:hidden">
            {apiKeyStatus === DEFAULT_API_KEY_NOTICE ? 'API' : 'OK'}
          </span>
        </div>

        {/* User Avatar */}
        <div className="relative group">
          <img
            src="https://picsum.photos/seed/useravatar/40/40"
            alt="User Avatar"
            className="w-8 h-8 sm:w-9 sm:h-9 lg:w-10 lg:h-10 rounded-full object-cover border-2 border-gray-200 hover:border-primary transition-all duration-200 cursor-pointer hover:scale-105"
          />

          {/* Tooltip */}
          <div className="absolute right-0 top-full mt-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            Usuário Logado
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
    