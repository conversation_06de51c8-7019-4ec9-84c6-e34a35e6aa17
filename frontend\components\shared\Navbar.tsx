
import React from 'react';
import { ICONS, DEFAULT_API_KEY_NOTICE } from '../../src/constants';

interface NavbarProps {
  toggleSidebar: () => void;
  apiKeyStatus: string;
}

const Navbar: React.FC<NavbarProps> = ({ toggleSidebar, apiKeyStatus }) => {
  return (
    <header className="h-16 bg-white shadow-md flex items-center justify-between px-4 lg:px-6 relative z-20">
      <button
        data-testid="sidebar-toggle"
        onClick={toggleSidebar}
        className="text-gray-500 focus:outline-none hover:text-primary p-2 rounded-md hover:bg-gray-100 lg:p-1"
        aria-label="Toggle sidebar"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
        </svg>
      </button>

      <div className="flex items-center space-x-2 lg:space-x-4">
        <div className={`
          text-xs lg:text-sm flex items-center
          ${apiKeyStatus === DEFAULT_API_KEY_NOTICE ? 'text-red-500' : 'text-green-600'}
        `}>
          <span className="hidden sm:inline-block mr-1 lg:mr-2">
            {apiKeyStatus === DEFAULT_API_KEY_NOTICE ? ICONS.warning : ICONS.checkCircle}
          </span>
          <span className="hidden md:inline-block lg:inline-block truncate max-w-32 lg:max-w-none">
            {apiKeyStatus}
          </span>
          <span className="md:hidden">
            {apiKeyStatus === DEFAULT_API_KEY_NOTICE ? '❌' : '✅'}
          </span>
        </div>

        <img
          src="https://picsum.photos/seed/useravatar/40/40"
          alt="User Avatar"
          className="w-8 h-8 lg:w-10 lg:h-10 rounded-full object-cover border-2 border-gray-200"
        />
      </div>
    </header>
  );
};

export default Navbar;
    