import { z } from 'zod';

// Schema para validação de telefone brasileiro
const brazilianPhoneRegex = /^(\+?55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/;

// Schema base para gestante
export const PregnantWomanSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  dueDate: z.string()
    .datetime('Data prevista deve ser uma data válida')
    .refine((date) => {
      const parsedDate = new Date(date);
      const now = new Date();
      const maxDate = new Date();
      maxDate.setMonth(maxDate.getMonth() + 10); // Máximo 10 meses no futuro

      return parsedDate > now && parsedDate <= maxDate;
    }, 'Data prevista deve estar entre hoje e 10 meses no futuro'),
  phone: z.string()
    .regex(brazilianPhoneRegex, 'Formato de telefone inválido')
    .transform((phone) => phone.replace(/\D/g, '')), // Remove caracteres não numéricos
  email: z.string()
    .email('Email inválido')
    .optional()
    .or(z.literal('')),
  observations: z.string()
    .max(500, 'Observações devem ter no máximo 500 caracteres')
    .optional(),
  createdAt: z.string().datetime('Data de criação inválida'),
  age: z.number()
    .min(12, 'Idade mínima é 12 anos')
    .max(60, 'Idade máxima é 60 anos')
    .optional(),
});

// Schema para criação de gestante (sem ID e createdAt)
export const CreatePregnantWomanSchema = PregnantWomanSchema.omit({
  id: true,
  createdAt: true,
});

// Schema para atualização de gestante (todos os campos opcionais exceto ID)
export const UpdatePregnantWomanSchema = PregnantWomanSchema.partial().required({
  id: true,
});

// Schema para mensagem
export const MessageSchema = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  sender: z.enum(['user', 'gestante', 'ai'], {
    errorMap: () => ({ message: 'Remetente deve ser user, gestante ou ai' }),
  }),
  text: z.string()
    .min(1, 'Texto da mensagem é obrigatório')
    .max(1000, 'Mensagem deve ter no máximo 1000 caracteres'),
  timestamp: z.string().datetime('Timestamp inválido'),
  status: z.enum(['sent', 'delivered', 'read', 'failed']).optional(),
});

// Schema para métricas do dashboard (simplificado)
export const DashboardMetricsSchema = z.object({
  totalPregnantWomen: z.number().min(0, 'Total de gestantes deve ser positivo'),
  avgWeeksGestation: z.number().min(0).optional(), // Opcional - não usado mais
  messagesSentThisMonth: z.number().min(0, 'Mensagens enviadas deve ser positivo'),
  activeConversations: z.number().min(0, 'Conversas ativas deve ser positivo'),
});

// Schema para dados de gráfico
export const ChartDataPointSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  value: z.number().min(0, 'Valor deve ser positivo'),
});

// Schema para configurações de API
export const ApiConfigSchema = z.object({
  apiKey: z.string().min(1, 'API Key é obrigatória'),
  baseUrl: z.string().url('URL base deve ser válida').optional(),
  timeout: z.number().min(1000, 'Timeout mínimo é 1000ms').optional(),
});

// Schema para validação de entrada de CSV
export const CSVRowSchema = z.object({
  nome: z.string().min(2, 'Nome é obrigatório'),
  telefone: z.string().regex(brazilianPhoneRegex, 'Telefone inválido'),
  'data-prevista': z.string().refine((date) => {
    const parsedDate = new Date(date);
    return !isNaN(parsedDate.getTime());
  }, 'Data prevista inválida'),
  email: z.string().email('Email inválido').optional(),
  idade: z.string().transform((val) => parseInt(val, 10))
    .refine((age) => age >= 12 && age <= 60, 'Idade deve estar entre 12 e 60 anos')
    .optional(),
});

// Tipos TypeScript derivados dos schemas
export type PregnantWoman = z.infer<typeof PregnantWomanSchema>;
export type CreatePregnantWoman = z.infer<typeof CreatePregnantWomanSchema>;
export type UpdatePregnantWoman = z.infer<typeof UpdatePregnantWomanSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type DashboardMetrics = z.infer<typeof DashboardMetricsSchema>;
export type ChartDataPoint = z.infer<typeof ChartDataPointSchema>;
export type ApiConfig = z.infer<typeof ApiConfigSchema>;
export type CSVRow = z.infer<typeof CSVRowSchema>;

// Função utilitária para validação segura
export const safeValidate = <T>(schema: z.ZodSchema<T>, data: unknown) => {
  try {
    return {
      success: true as const,
      data: schema.parse(data),
      error: null,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false as const,
        data: null,
        error: error.errors,
      };
    }
    return {
      success: false as const,
      data: null,
      error: [{ message: 'Erro de validação desconhecido', path: [] }],
    };
  }
};
