import React, { useState } from 'react';
import { XMarkIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';
import { AudioRecorder } from './AudioRecorder';
import { apiService, type Contact } from '../../services/api';
import { toast } from 'react-toastify';

interface AudioChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  contact: Contact;
}

export const AudioChatModal: React.FC<AudioChatModalProps> = ({
  isOpen,
  onClose,
  contact
}) => {
  const [step, setStep] = useState<'record' | 'message' | 'processing' | 'response'>('record');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [textMessage, setTextMessage] = useState('');
  const [aiResponse, setAiResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  if (!isOpen) return null;


  const handleAudioReady = (file: File) => {
    setAudioFile(file);
    setStep('message');
  };


  const handleBackToRecord = () => {
    setAudioFile(null);
    setTextMessage('');
    setStep('record');
  };


  const handleProcessAudio = async () => {
    if (!audioFile) return;

    setIsLoading(true);
    setStep('processing');

    try {
      const contactId = contact.id || contact._id;
      if (!contactId) {
        throw new Error('ID do contato não encontrado');
      }

      console.log('🎵 Enviando áudio para processamento:', {
        contactId,
        audioFile: audioFile.name,
        textMessage
      });

      const result = await apiService.processAudioMessage(
        contactId,
        audioFile,
        textMessage || undefined
      );

      console.log('✅ Resposta da IA recebida:', result);

      setAiResponse(result.response);
      setStep('response');
      
      toast.success('Áudio processado com sucesso! 🎵');
    } catch (error: any) {
      console.error('❌ Erro ao processar áudio:', error);
      toast.error('Erro ao processar áudio: ' + (error.message || 'Erro desconhecido'));
      setStep('message');
    } finally {
      setIsLoading(false);
    }
  };


  const handleClose = () => {
    setStep('record');
    setAudioFile(null);
    setTextMessage('');
    setAiResponse('');
    setIsLoading(false);
    onClose();
  };


  const handleNewConversation = () => {
    setStep('record');
    setAudioFile(null);
    setTextMessage('');
    setAiResponse('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <ChatBubbleLeftRightIcon className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Chat com Áudio - {contact.name}
              </h2>
              <p className="text-sm text-gray-500">
                Converse com a Rafaela usando sua voz
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Etapa 1: Gravação */}
          {step === 'record' && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  🎤 Grave sua mensagem
                </h3>
                <p className="text-gray-600">
                  Fale naturalmente sobre suas dúvidas ou preocupações
                </p>
              </div>
              
              <AudioRecorder
                onAudioReady={handleAudioReady}
                onCancel={handleClose}
                maxDuration={180}
              />
            </div>
          )}

          {/* Etapa 2: Mensagem complementar */}
          {step === 'message' && audioFile && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 text-green-800">
                  <span>✅</span>
                  <span className="font-medium">Áudio gravado com sucesso!</span>
                </div>
                <p className="text-sm text-green-700 mt-1">
                  Arquivo: {audioFile.name} ({(audioFile.size / 1024).toFixed(1)} KB)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mensagem complementar (opcional)
                </label>
                <textarea
                  value={textMessage}
                  onChange={(e) => setTextMessage(e.target.value)}
                  placeholder="Adicione informações extras por escrito, se desejar..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">
                  A IA analisará principalmente o áudio, mas pode usar este texto como contexto adicional.
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleBackToRecord}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  ← Gravar Novamente
                </button>
                <button
                  onClick={handleProcessAudio}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                >
                  Enviar para Rafaela 🧡
                </button>
              </div>
            </div>
          )}

          {/* Etapa 3: Processando */}
          {step === 'processing' && (
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center space-x-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="text-lg font-medium text-gray-900">
                  Processando áudio...
                </span>
              </div>
              <p className="text-gray-600">
                A Rafaela está ouvindo sua mensagem e preparando uma resposta personalizada
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                  🤖 Usando Gemini 1.5 Flash Latest para análise multimodal
                </p>
              </div>
            </div>
          )}

          {/* Etapa 4: Resposta */}
          {step === 'response' && aiResponse && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  💬 Resposta da Rafaela
                </h3>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-semibold">R</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-orange-900 font-medium mb-1">Rafaela</p>
                    <p className="text-orange-800 leading-relaxed">
                      {aiResponse}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleNewConversation}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  🎤 Nova Mensagem
                </button>
                <button
                  onClick={handleClose}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  ✅ Finalizar
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>🧡 Powered by Rafaela AI</span>
            <span>Gemini 1.5 Flash Latest</span>
          </div>
        </div>
      </div>
    </div>
  );
};
