/**
 * Monitor de Sessão WhatsApp
 * Monitora a estabilidade e saúde da sessão WhatsApp
 */

import { WppConnectIntegration } from './wppConnectIntegration';

export interface SessionMetrics {
  uptime: number;
  totalMessages: number;
  lastActivity: Date | null;
  reconnectCount: number;
  healthScore: number;
  batteryLevel?: number;
  connectionState?: string;
}

export class SessionMonitor {
  private wppIntegration: WppConnectIntegration | null = null;
  private metrics: SessionMetrics;
  private startTime: Date;
  private monitorInterval: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startTime = new Date();
    this.metrics = {
      uptime: 0,
      totalMessages: 0,
      lastActivity: null,
      reconnectCount: 0,
      healthScore: 0
    };
  }

  /**
   * Inicializar monitor
   */
  public initialize(wppIntegration: WppConnectIntegration): void {
    this.wppIntegration = wppIntegration;
    this.startMonitoring();
    console.log('📊 Monitor de sessão WhatsApp inicializado');
  }

  /**
   * Iniciar monitoramento
   */
  private startMonitoring(): void {
    // Monitor geral a cada 30 segundos
    this.monitorInterval = setInterval(() => {
      this.updateMetrics();
    }, 30000);

    // Health check a cada 2 minutos
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 120000);
  }

  /**
   * Atualizar métricas
   */
  private updateMetrics(): void {
    if (!this.wppIntegration) return;

    const now = new Date();
    this.metrics.uptime = now.getTime() - this.startTime.getTime();

    const status = this.wppIntegration.getConnectionStatus();
    if (status.lastActivity) {
      this.metrics.lastActivity = status.lastActivity;
    }

    // Calcular score de saúde baseado em vários fatores
    this.calculateHealthScore();
  }

  /**
   * Realizar verificação de saúde
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.wppIntegration) return;

    try {
      const healthCheck = await this.wppIntegration.checkSessionHealth();
      
      if (healthCheck.details.batteryLevel !== undefined) {
        this.metrics.batteryLevel = healthCheck.details.batteryLevel;
      }
      
      if (healthCheck.details.connectionState) {
        this.metrics.connectionState = healthCheck.details.connectionState;
      }

      // Log de saúde
      console.log('🏥 Health Check:', {
        isHealthy: healthCheck.isHealthy,
        batteryLevel: this.metrics.batteryLevel,
        connectionState: this.metrics.connectionState,
        healthScore: this.metrics.healthScore
      });

      // Se a saúde está baixa, alertar
      if (this.metrics.healthScore < 50) {
        console.warn('⚠️ Saúde da sessão baixa:', this.metrics.healthScore);
      }

    } catch (error) {
      console.error('❌ Erro no health check:', error);
    }
  }

  /**
   * Calcular score de saúde (0-100)
   */
  private calculateHealthScore(): void {
    if (!this.wppIntegration) {
      this.metrics.healthScore = 0;
      return;
    }

    let score = 0;
    const status = this.wppIntegration.getConnectionStatus();

    // Conexão ativa (40 pontos)
    if (status.isConnected) score += 40;

    // Autenticado (30 pontos)
    if (status.isAuthenticated) score += 30;

    // Atividade recente (20 pontos)
    if (status.lastActivity) {
      const timeSinceActivity = Date.now() - status.lastActivity.getTime();
      const hoursAgo = timeSinceActivity / (1000 * 60 * 60);
      
      if (hoursAgo < 1) score += 20;
      else if (hoursAgo < 6) score += 15;
      else if (hoursAgo < 24) score += 10;
      else score += 5;
    }

    // Bateria do celular (10 pontos)
    if (this.metrics.batteryLevel !== undefined) {
      if (this.metrics.batteryLevel > 50) score += 10;
      else if (this.metrics.batteryLevel > 20) score += 5;
      else score += 2;
    }

    this.metrics.healthScore = Math.min(100, score);
  }

  /**
   * Incrementar contador de mensagens
   */
  public incrementMessageCount(): void {
    this.metrics.totalMessages++;
    this.metrics.lastActivity = new Date();
  }

  /**
   * Incrementar contador de reconexões
   */
  public incrementReconnectCount(): void {
    this.metrics.reconnectCount++;
  }

  /**
   * Obter métricas atuais
   */
  public getMetrics(): SessionMetrics {
    this.updateMetrics();
    return { ...this.metrics };
  }

  /**
   * Obter relatório detalhado
   */
  public getDetailedReport(): {
    metrics: SessionMetrics;
    status: string;
    recommendations: string[];
  } {
    const metrics = this.getMetrics();
    let status = 'unknown';
    const recommendations: string[] = [];

    // Determinar status geral
    if (metrics.healthScore >= 80) {
      status = 'excellent';
    } else if (metrics.healthScore >= 60) {
      status = 'good';
    } else if (metrics.healthScore >= 40) {
      status = 'fair';
    } else {
      status = 'poor';
    }

    // Gerar recomendações
    if (metrics.healthScore < 60) {
      recommendations.push('Considere reconectar o WhatsApp');
    }

    if (metrics.batteryLevel && metrics.batteryLevel < 20) {
      recommendations.push('Bateria do celular baixa - carregue o dispositivo');
    }

    if (metrics.lastActivity) {
      const hoursAgo = (Date.now() - metrics.lastActivity.getTime()) / (1000 * 60 * 60);
      if (hoursAgo > 24) {
        recommendations.push('Sessão inativa há mais de 24 horas');
      }
    }

    if (metrics.reconnectCount > 5) {
      recommendations.push('Muitas reconexões - verifique estabilidade da internet');
    }

    return {
      metrics,
      status,
      recommendations
    };
  }

  /**
   * Parar monitoramento
   */
  public stop(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    console.log('📊 Monitor de sessão parado');
  }
}

// Instância singleton
export const sessionMonitor = new SessionMonitor();
