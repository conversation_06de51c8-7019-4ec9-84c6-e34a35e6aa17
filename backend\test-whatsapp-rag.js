/**
 * Teste da Integração WhatsApp + RAG
 */

const { GeminiAIService } = require('./dist/services/gemini');

async function testWhatsAppRAGIntegration() {
  try {
    console.log('🧪 Testando Integração WhatsApp + RAG...\n');

    // Simular contato
    const mockContact = {
      name: '<PERSON>',
      phone: '5584988501582',
      baby_gender: 'female'
    };

    // Inicializar serviço <PERSON>
    const geminiService = new GeminiAIService();
    
    // Aguardar inicialização
    await new Promise(resolve => setTimeout(resolve, 1000));

    const testMessages = [
      {
        message: 'Como solicitar poda de árvore na minha rua?',
        expectedRAG: true,
        category: 'infrastructure'
      },
      {
        message: 'Preciso marcar uma consulta médica no posto de saúde',
        expectedRAG: true,
        category: 'health'
      },
      {
        message: 'Quais são os projetos da vereadora Rafaela?',
        expectedRAG: true,
        category: 'legislation'
      },
      {
        message: 'O<PERSON>, como você está?',
        expectedRAG: false,
        category: 'other'
      },
      {
        message: 'Quando será a próxima audiência pública?',
        expectedRAG: true,
        category: 'transparency'
      }
    ];

    console.log(`📋 Testando ${testMessages.length} mensagens...\n`);

    for (let i = 0; i < testMessages.length; i++) {
      const test = testMessages[i];
      
      console.log(`🔍 Teste ${i + 1}: "${test.message}"`);
      console.log(`   📊 Categoria esperada: ${test.category}`);
      console.log(`   🧠 RAG esperado: ${test.expectedRAG ? 'Sim' : 'Não'}`);
      
      try {
        const startTime = Date.now();
        
        // Testar novo método RAG-enhanced
        const response = await geminiService.generateRAGEnhancedResponse(
          mockContact, 
          test.message
        );

        const processingTime = Date.now() - startTime;
        
        console.log(`   ✅ Resposta (${processingTime}ms):`);
        console.log(`      "${response}"`);
        
        // Verificar características da resposta
        const hasPersonalization = response.toLowerCase().includes(mockContact.name.toLowerCase().split(' ')[0]);
        const hasSignature = response.includes('🧡');
        const isReasonableLength = response.length >= 20 && response.length <= 300;
        
        console.log(`   📝 Análise da resposta:`);
        console.log(`      - Personalizada: ${hasPersonalization ? '✅' : '❌'}`);
        console.log(`      - Com assinatura: ${hasSignature ? '✅' : '❌'}`);
        console.log(`      - Tamanho adequado: ${isReasonableLength ? '✅' : '❌'} (${response.length} chars)`);
        
      } catch (error) {
        console.log(`   ❌ Erro: ${error.message}`);
      }
      
      console.log(''); // Linha em branco
      
      // Delay entre testes
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Teste de comparação: método antigo vs novo
    console.log('🔄 Teste de Comparação: Método Antigo vs RAG-Enhanced\n');
    
    const comparisonMessage = 'Como solicitar poda de árvore?';
    
    try {
      console.log(`📋 Mensagem de teste: "${comparisonMessage}"`);
      
      // Método RAG-enhanced
      console.log('\n🧠 Método RAG-Enhanced:');
      const startTimeNew = Date.now();
      const ragResponse = await geminiService.generateRAGEnhancedResponse(
        mockContact, 
        comparisonMessage
      );
      const timeNew = Date.now() - startTimeNew;
      
      console.log(`   ⏱️ Tempo: ${timeNew}ms`);
      console.log(`   📝 Resposta: "${ragResponse}"`);
      
      // Método otimizado (que agora usa RAG por padrão)
      console.log('\n⚡ Método Otimizado (com RAG):');
      const startTimeOpt = Date.now();
      const optResponse = await geminiService.generateOptimizedResponse(
        mockContact, 
        comparisonMessage
      );
      const timeOpt = Date.now() - startTimeOpt;
      
      console.log(`   ⏱️ Tempo: ${timeOpt}ms`);
      console.log(`   📝 Resposta: "${optResponse}"`);
      
      console.log('\n📊 Comparação:');
      console.log(`   - Ambos usam RAG: ${ragResponse === optResponse ? '✅ Sim' : '❌ Não'}`);
      console.log(`   - Diferença de tempo: ${Math.abs(timeNew - timeOpt)}ms`);
      
    } catch (error) {
      console.log(`❌ Erro na comparação: ${error.message}`);
    }

    // Teste de diagnóstico do sistema
    console.log('\n🔧 Diagnóstico do Sistema:\n');
    
    try {
      const health = geminiService.getSystemHealth();
      console.log('📊 Status do Sistema:');
      console.log(`   - Inicializado: ${health.initialized ? '✅' : '❌'}`);
      console.log(`   - Cache: ${health.cacheStats.size} itens`);
      console.log(`   - Configuração: ${JSON.stringify(health.configuration)}`);
      
      const diagnostics = await geminiService.runDiagnostics();
      console.log(`\n🏥 Diagnóstico: ${diagnostics.status}`);
      console.log(`   - Conectividade: ${diagnostics.details.connectivityTest.success ? '✅' : '❌'}`);
      if (diagnostics.details.connectivityTest.latency > 0) {
        console.log(`   - Latência: ${diagnostics.details.connectivityTest.latency}ms`);
      }
      
      if (diagnostics.recommendations.length > 0) {
        console.log('   💡 Recomendações:');
        diagnostics.recommendations.forEach(rec => {
          console.log(`      - ${rec}`);
        });
      }
      
    } catch (error) {
      console.log(`❌ Erro no diagnóstico: ${error.message}`);
    }

    console.log('\n🎉 Teste de Integração WhatsApp + RAG concluído!');

  } catch (error) {
    console.error('❌ Erro geral no teste:', error);
  }
}

// Executar teste se chamado diretamente
if (require.main === module) {
  testWhatsAppRAGIntegration();
}

module.exports = { testWhatsAppRAGIntegration };
