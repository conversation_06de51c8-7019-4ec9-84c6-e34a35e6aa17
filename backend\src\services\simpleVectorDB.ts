/**
 * Serviço de Vector Database Simplificado
 * Implementação em memória para o sistema RAG
 */

import { embeddingService, EmbeddingResult } from './embeddingService';
import * as fs from 'fs';
import * as path from 'path';

export interface DocumentMetadata {
  id: string;
  title: string;
  content: string;
  documentType: 'legislation' | 'project' | 'service' | 'faq' | 'agenda' | 'admin';
  category: 'health' | 'education' | 'infrastructure' | 'transparency' | 'legislation' | 'other';
  filePath?: string;
  uploadedAt: string;
  chunkIndex?: number;
  totalChunks?: number;
  source?: string;
  tags?: string[];
}

export interface SearchResult {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  similarity: number;
  distance: number;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
}

interface StoredDocument {
  id: string;
  content: string;
  embedding: number[];
  metadata: DocumentMetadata;
}

export class SimpleVectorDatabase {
  private documents: StoredDocument[] = [];
  private dataFile: string;

  constructor() {
    this.dataFile = path.join(process.cwd(), 'data', 'vector_db.json');
    this.ensureDataDir();
    this.loadFromFile();
  }

  /**
   * Garantir que o diretório de dados existe
   */
  private ensureDataDir(): void {
    const dataDir = path.dirname(this.dataFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  /**
   * Carregar dados do arquivo
   */
  private loadFromFile(): void {
    try {
      if (fs.existsSync(this.dataFile)) {
        const data = fs.readFileSync(this.dataFile, 'utf-8');
        this.documents = JSON.parse(data);
        console.log(`📊 Carregados ${this.documents.length} documentos do arquivo`);
      }
    } catch (error) {
      console.error('❌ Erro ao carregar dados:', error);
      this.documents = [];
    }
  }

  /**
   * Salvar dados no arquivo
   */
  private saveToFile(): void {
    try {
      fs.writeFileSync(this.dataFile, JSON.stringify(this.documents, null, 2));
    } catch (error) {
      console.error('❌ Erro ao salvar dados:', error);
    }
  }

  /**
   * Inicializar a base de dados
   */
  async initialize(): Promise<void> {
    console.log('🔧 Inicializando Simple Vector Database...');
    console.log(`📊 ${this.documents.length} documentos carregados`);
  }

  /**
   * Adicionar documento à base de conhecimento
   */
  async addDocument(
    content: string,
    metadata: Omit<DocumentMetadata, 'id' | 'uploadedAt'>
  ): Promise<string> {
    try {
      const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      console.log(`📝 Adicionando documento: ${metadata.title}`);

      // Dividir em chunks se necessário
      const chunks = embeddingService.splitIntoChunks(content, 1000, 200);
      console.log(`📄 Documento dividido em ${chunks.length} chunks`);

      // Gerar embeddings para cada chunk
      const embeddings = await embeddingService.generateEmbeddingBatch(chunks);

      // Adicionar cada chunk como documento
      embeddings.embeddings.forEach((embResult, index) => {
        const chunkId = chunks.length > 1 ? `${documentId}_chunk_${index}` : documentId;
        
        const storedDoc: StoredDocument = {
          id: chunkId,
          content: embResult.text,
          embedding: embResult.embedding,
          metadata: {
            ...metadata,
            id: chunkId,
            content: embResult.text,
            uploadedAt: new Date().toISOString(),
            chunkIndex: index,
            totalChunks: chunks.length
          }
        };

        this.documents.push(storedDoc);
      });

      // Salvar no arquivo
      this.saveToFile();

      console.log(`✅ Documento adicionado: ${embeddings.embeddings.length} chunks indexados`);
      return documentId;

    } catch (error) {
      console.error('❌ Erro ao adicionar documento:', error);
      throw new Error(`Falha ao adicionar documento: ${error}`);
    }
  }

  /**
   * Buscar documentos similares
   */
  async search(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    try {
      const {
        limit = 10,
        threshold = 0.7,
        filter = {},
        includeMetadata = true
      } = options;

      console.log(`🔍 Buscando: "${query}" (limite: ${limit})`);

      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);

      // Calcular similaridades
      const similarities = this.documents.map(doc => {
        const similarity = embeddingService.calculateCosineSimilarity(
          queryEmbedding.embedding,
          doc.embedding
        );

        return {
          ...doc,
          similarity,
          distance: 1 - similarity
        };
      });

      // Filtrar por threshold
      let filteredResults = similarities.filter(result => result.similarity >= threshold);

      // Aplicar filtros adicionais
      if (Object.keys(filter).length > 0) {
        filteredResults = filteredResults.filter(result => {
          return Object.entries(filter).every(([key, value]) => {
            return (result.metadata as any)[key] === value;
          });
        });
      }

      // Ordenar por similaridade e limitar
      const searchResults = filteredResults
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit)
        .map(result => ({
          id: result.id,
          content: result.content,
          metadata: result.metadata,
          similarity: result.similarity,
          distance: result.distance
        }));

      console.log(`📊 Encontrados ${searchResults.length} resultados relevantes`);
      return searchResults;

    } catch (error) {
      console.error('❌ Erro na busca:', error);
      throw new Error(`Falha na busca: ${error}`);
    }
  }

  /**
   * Buscar por categoria
   */
  async searchByCategory(
    query: string,
    category: DocumentMetadata['category'],
    limit: number = 5
  ): Promise<SearchResult[]> {
    return this.search(query, {
      limit,
      filter: { category }
    });
  }

  /**
   * Buscar por tipo de documento
   */
  async searchByDocumentType(
    query: string,
    documentType: DocumentMetadata['documentType'],
    limit: number = 5
  ): Promise<SearchResult[]> {
    return this.search(query, {
      limit,
      filter: { documentType }
    });
  }

  /**
   * Obter documento por ID
   */
  async getDocument(id: string): Promise<SearchResult | null> {
    const doc = this.documents.find(d => d.id === id);
    
    if (doc) {
      return {
        id: doc.id,
        content: doc.content,
        metadata: doc.metadata,
        similarity: 1,
        distance: 0
      };
    }

    return null;
  }

  /**
   * Remover documento
   */
  async removeDocument(id: string): Promise<boolean> {
    try {
      const initialLength = this.documents.length;
      
      // Remover documento e seus chunks
      this.documents = this.documents.filter(doc => 
        !doc.id.startsWith(id) && doc.id !== id
      );

      const removed = this.documents.length < initialLength;
      
      if (removed) {
        this.saveToFile();
        console.log(`🗑️ Documento removido: ${id}`);
      }

      return removed;
    } catch (error) {
      console.error('❌ Erro ao remover documento:', error);
      return false;
    }
  }

  /**
   * Obter estatísticas da coleção
   */
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    const documentsByType: Record<string, number> = {};
    const documentsByCategory: Record<string, number> = {};

    this.documents.forEach(doc => {
      const docType = doc.metadata.documentType || 'unknown';
      const category = doc.metadata.category || 'unknown';

      documentsByType[docType] = (documentsByType[docType] || 0) + 1;
      documentsByCategory[category] = (documentsByCategory[category] || 0) + 1;
    });

    return {
      totalDocuments: this.documents.length,
      documentsByType,
      documentsByCategory
    };
  }

  /**
   * Limpar toda a coleção
   */
  async clearCollection(): Promise<void> {
    this.documents = [];
    this.saveToFile();
    console.log('🗑️ Coleção limpa completamente');
  }
}

// Instância singleton
export const simpleVectorDatabase = new SimpleVectorDatabase();
