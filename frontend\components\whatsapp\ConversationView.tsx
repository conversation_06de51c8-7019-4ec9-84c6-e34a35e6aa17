import React, { useState, useRef, useEffect, cloneElement } from 'react';
import { Message, Contact, GroundingChunk } from '../../src/types';
import MessageInput from './MessageInput';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale/pt-BR';

import { ICONS } from '../../src/constants';
import Spinner from '../shared/Spinner';
import Button from '../shared/Button';

interface ConversationViewProps {
  contactName: string;
  messages: Message[];
  onSendMessage: (text: string) => Message;
  onSendAiMessage: (text: string) => Message;
  currentContact: Contact;
}

const ConversationView: React.FC<ConversationViewProps> = ({ contactName, messages, onSendMessage, onSendAiMessage, currentContact }) => {
  const [aiLoading, setAiLoading] = useState(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [aiSuggestion, setAiSuggestion] = useState<string | null>(null);
  const [groundingChunks, setGroundingChunks] = useState<GroundingChunk[]>([]);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const generateContextForAI = (): string => {
    let context = `Informações do contato: Nome: ${currentContact.name}, Telefone: ${currentContact.phone}.`;
    if (currentContact.babyGender) {
      context += ` Gênero do bebê: ${currentContact.babyGender}.`;
    }
    context += "\n\nHistórico da conversa recente (últimas 5 mensagens):\n";
    messages.slice(-5).forEach(msg => {
      context += `${msg.sender === 'user' ? 'Assistente' : msg.sender === 'ai' ? 'Sugestão IA' : contactName}: ${msg.text}\n`;
    });
    return context;
  };

  const handleGetAISuggestion = async (promptType: 'general' | 'empathy' | 'information' | 'next_step') => {
    setAiLoading(true);
    setAiError(null);
    setAiSuggestion(null);
    setGroundingChunks([]);

    const conversationContext = generateContextForAI();
    let systemInstruction = "Você é um assistente virtual para suporte a gestantes. Seja empático, claro e forneça informações úteis e seguras. Responda em português brasileiro.";
    let userPrompt = "";

    switch (promptType) {
        case 'empathy':
            userPrompt = `Com base no contexto, formule uma resposta empática para a última mensagem da gestante.`;
            break;
        case 'information':
            userPrompt = `A gestante parece precisar de informação. Com base no contexto, forneça uma informação relevante e útil. Se for uma pergunta sobre saúde, sugira consultar um médico. Se for uma dúvida que pode ser respondida com informações gerais, responda. Se for algo recente ou que precise de dados da web, avise que irá pesquisar.`;

            break;
        case 'next_step':
            userPrompt = `Qual seria um bom próximo passo ou pergunta para continuar o acompanhamento desta gestante, baseado no contexto atual?`;
            break;
        case 'general':
        default:
            userPrompt = `Sugira uma resposta apropriada para a última mensagem da gestante, considerando todo o contexto fornecido.`;
            break;
    }
    
    const fullPrompt = `${conversationContext}\n\n Tarefa para IA: ${userPrompt}`;

    try {

      const response = await fetch('http://localhost:3334/api/ai/generate-suggestion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: fullPrompt,
          systemInstruction,
          promptType,
          context: conversationContext
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAiSuggestion(data.suggestion || data.message);
        if (data.groundingChunks) {
          setGroundingChunks(data.groundingChunks);
        }
      } else {
        throw new Error('Falha ao obter sugestão da IA');
      }
    } catch (error) {
      console.error("AI suggestion error:", error);
      setAiError(error instanceof Error ? error.message : "Falha ao obter sugestão da IA.");
    } finally {
      setAiLoading(false);
    }
  };

  const MessageBubble: React.FC<{ message: Message }> = ({ message }) => {
    const isUser = message.sender === 'user';
    const isAI = message.sender === 'ai';
    const isFromMe = (message as any).from_me === true;
    const isAudio = (message as any).type === 'audio';

    // Determinar classes baseado no remetente e tipo
    let bubbleClasses = isUser || isAI || isFromMe
      ? 'bg-primary text-white self-end rounded-l-xl rounded-tr-xl'
      : 'bg-gray-200 text-neutral-dark self-start rounded-r-xl rounded-tl-xl';

    // Estilo especial para mensagens de áudio
    if (isAudio && !isFromMe) {
      bubbleClasses = 'bg-blue-50 border border-blue-200 text-blue-900 self-start rounded-r-xl rounded-tl-xl';
    }

    const aiPrefix = isAI ? <span className="font-semibold text-xs block mb-1 opacity-80">Sugestão IA Enviada:</span> : null;

    // Processar conteúdo da mensagem
    let displayText = message.text || (message as any).content || '';
    let isTranscription = false;

    if (isAudio && displayText.startsWith('[ÁUDIO]')) {
      displayText = displayText.replace('[ÁUDIO] ', '');
      isTranscription = true;
    }

    return (
      <div className={`max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl px-4 py-2 my-1 ${bubbleClasses} shadow-sm`}>
        {aiPrefix}

        {/* Indicador de áudio */}
        {isAudio && (
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-1">
              <span className="text-sm">🎵</span>
              <span className="text-xs font-medium opacity-90">
                {isTranscription ? 'Transcrição do áudio' : 'Mensagem de áudio'}
              </span>
            </div>
            {isTranscription && (
              <span className="text-xs bg-blue-500 bg-opacity-20 px-2 py-0.5 rounded-full font-medium">
                IA Gemini
              </span>
            )}
          </div>
        )}

        <p className="text-sm whitespace-pre-wrap leading-relaxed">{displayText}</p>

        <span className="text-xs opacity-70 mt-1 block text-right">
          {format(new Date(message.timestamp), "HH:mm", { locale: ptBR })}
        </span>
      </div>
    );
  };


  return (
    <div className="bg-white rounded-lg shadow flex flex-col h-[calc(100vh-12rem)] max-h-[700px]"> {/* Adjusted height */}
      <header className="bg-gray-50 p-4 border-b border-gray-200 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-neutral-dark">{contactName}</h3>
            <p className="text-xs text-gray-500">Telefone: {currentContact.phone}</p>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-3 text-xs text-gray-600">
              <span className="flex items-center space-x-1">
                <span>💬</span>
                <span>{messages.length} mensagens</span>
              </span>
              <span className="flex items-center space-x-1">
                <span>🎵</span>
                <span>{messages.filter(m => (m as any).type === 'audio').length} áudios</span>
              </span>
            </div>
          </div>
        </div>
      </header>

      <div className="flex-1 p-4 overflow-y-auto space-y-2 bg-gray-100">
        {messages.map(msg => <MessageBubble key={msg.id} message={msg} />)}
        <div ref={messagesEndRef} />
      </div>

      {aiLoading && <div className="p-2 border-t"><Spinner size="sm"/> <p className="text-xs text-center text-gray-500">IA pensando...</p></div>}
      {aiError && <p className="p-2 text-xs text-red-500 text-center border-t">{aiError}</p>}
      {aiSuggestion && (
        <div className="p-3 border-t bg-blue-50">
          <div className="flex justify-between items-center mb-1">
            <p className="text-xs font-semibold text-primary">Sugestão da IA:</p>
            <button onClick={() => setAiSuggestion(null)} className="text-xs text-gray-400 hover:text-gray-600">&times; Dispensar</button>
          </div>
          <p className="text-sm text-neutral-dark whitespace-pre-wrap">{aiSuggestion}</p>
          {groundingChunks && groundingChunks.length > 0 && (
            <div className="mt-2">
              <p className="text-xs font-semibold text-gray-600">Fontes (Google Search):</p>
              <ul className="list-disc list-inside text-xs">
                {groundingChunks.map((chunk, index) => (
                  chunk.web && <li key={index}><a href={chunk.web.uri} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">{chunk.web.title || chunk.web.uri}</a></li>
                ))}
              </ul>
            </div>
          )}
          <Button 
            size="sm" 
            variant="secondary" 
            onClick={() => { onSendAiMessage(aiSuggestion); setAiSuggestion(null); setGroundingChunks([]); }} 
            className="mt-2 w-full"
            disabled={false}
          >
            Enviar Resposta da IA
          </Button>
        </div>
      )}

      <div className="p-2 border-t border-gray-200 bg-gray-50">
        <div className="flex space-x-1 mb-2 justify-end">
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('general')} disabled={aiLoading} title="Sugestão Geral">{cloneElement(ICONS.ai, {className:"w-4 h-4"})}</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('empathy')} disabled={aiLoading} title="Resposta Empática">Empatia</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('information')} disabled={aiLoading} title="Fornecer Informação">Info</Button>
            <Button size="sm" variant="outline" onClick={() => handleGetAISuggestion('next_step')} disabled={aiLoading} title="Próximo Passo">Ação</Button>
        </div>
        <MessageInput onSend={onSendMessage} disabled={false} />
      </div>
    </div>
  );
};

export default ConversationView;