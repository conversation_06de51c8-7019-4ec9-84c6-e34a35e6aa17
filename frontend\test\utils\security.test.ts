import { describe, it, expect, vi } from 'vitest';
import {
  escapeHtml,
  sanitizeInput,
  sanitizeObject,
  isValidEmail,
  isValidBrazilianPhone,
  normalizeBrazilianPhone,
  isValidDateRange,
  generateCSRFToken,
  validateCSRFToken,
  validateFileSize,
  validateFileType,
  containsOnlySafeChars,
  cleanText,
} from '../../utils/security';

describe('Security Utils', () => {
  describe('escapeHtml', () => {
    it('deve escapar caracteres HTML perigosos', () => {
      const input = '<script>alert("xss")</script>';
      const expected = '&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;';
      expect(escapeHtml(input)).toBe(expected);
    });

    it('deve escapar aspas simples e duplas', () => {
      const input = `"Hello" and 'World'`;
      const expected = '&quot;Hello&quot; and &#x27;World&#x27;';
      expect(escapeHtml(input)).toBe(expected);
    });

    it('deve lidar com valores não-string', () => {
      expect(escapeHtml(123 as any)).toBe('123');
      expect(escapeHtml(null as any)).toBe('null');
    });
  });

  describe('sanitizeInput', () => {
    it('deve remover scripts perigosos', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = sanitizeInput(input);
      expect(result).not.toContain('<script>');
      expect(result).toContain('Hello');
    });

    it('deve remover javascript: URLs', () => {
      const input = 'javascript:alert("xss")';
      const result = sanitizeInput(input);
      expect(result).not.toContain('javascript:');
    });

    it('deve remover caracteres de controle', () => {
      const input = 'Hello\x00\x01World';
      const result = sanitizeInput(input);
      expect(result).toBe('HelloWorld');
    });

    it('deve limitar o tamanho da string', () => {
      const input = 'a'.repeat(20000);
      const result = sanitizeInput(input);
      expect(result.length).toBe(10000);
    });

    it('deve fazer trim da string', () => {
      const input = '  Hello World  ';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello World');
    });
  });

  describe('sanitizeObject', () => {
    it('deve sanitizar strings em objetos', () => {
      const input = {
        name: '<script>alert("xss")</script>Ana',
        age: 25,
        email: '<EMAIL>',
      };
      
      const result = sanitizeObject(input);
      expect(result.name).not.toContain('<script>');
      expect(result.name).toContain('Ana');
      expect(result.age).toBe(25);
      expect(result.email).toBe('<EMAIL>');
    });

    it('deve sanitizar arrays', () => {
      const input = {
        tags: ['<script>tag1</script>', 'tag2', 'tag3'],
      };
      
      const result = sanitizeObject(input);
      expect(result.tags[0]).not.toContain('<script>');
      expect(result.tags[1]).toBe('tag2');
    });

    it('deve sanitizar objetos aninhados', () => {
      const input = {
        user: {
          name: '<script>Ana</script>',
          profile: {
            bio: 'javascript:alert("xss")',
          },
        },
      };
      
      const result = sanitizeObject(input);
      expect(result.user.name).not.toContain('<script>');
      expect(result.user.profile.bio).not.toContain('javascript:');
    });
  });

  describe('isValidEmail', () => {
    it('deve validar emails corretos', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('deve rejeitar emails inválidos', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('user@')).toBe(false);
      expect(isValidEmail('')).toBe(false);
    });

    it('deve rejeitar emails muito longos', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      expect(isValidEmail(longEmail)).toBe(false);
    });

    it('deve lidar com valores não-string', () => {
      expect(isValidEmail(123 as any)).toBe(false);
      expect(isValidEmail(null as any)).toBe(false);
    });
  });

  describe('isValidBrazilianPhone', () => {
    it('deve validar telefones brasileiros válidos', () => {
      expect(isValidBrazilianPhone('11999999999')).toBe(true);
      expect(isValidBrazilianPhone('1199999999')).toBe(true);
      expect(isValidBrazilianPhone('5511999999999')).toBe(true);
      expect(isValidBrazilianPhone('(11) 99999-9999')).toBe(true);
    });

    it('deve rejeitar telefones inválidos', () => {
      expect(isValidBrazilianPhone('123')).toBe(false);
      expect(isValidBrazilianPhone('999999999999999')).toBe(false);
      expect(isValidBrazilianPhone('')).toBe(false);
    });

    it('deve lidar com valores não-string', () => {
      expect(isValidBrazilianPhone(123 as any)).toBe(false);
      expect(isValidBrazilianPhone(null as any)).toBe(false);
    });
  });

  describe('normalizeBrazilianPhone', () => {
    it('deve normalizar telefones com formatação', () => {
      expect(normalizeBrazilianPhone('(11) 99999-9999')).toBe('11999999999');
      expect(normalizeBrazilianPhone('+55 11 99999-9999')).toBe('11999999999');
      expect(normalizeBrazilianPhone('5511999999999')).toBe('11999999999');
    });

    it('deve manter telefones já normalizados', () => {
      expect(normalizeBrazilianPhone('11999999999')).toBe('11999999999');
    });

    it('deve lidar com valores não-string', () => {
      expect(normalizeBrazilianPhone(123 as any)).toBe('');
      expect(normalizeBrazilianPhone(null as any)).toBe('');
    });
  });

  describe('isValidDateRange', () => {
    it('deve validar datas futuras válidas', () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 6);
      expect(isValidDateRange(futureDate.toISOString())).toBe(true);
    });

    it('deve rejeitar datas no passado', () => {
      const pastDate = new Date();
      pastDate.setMonth(pastDate.getMonth() - 1);
      expect(isValidDateRange(pastDate.toISOString())).toBe(false);
    });

    it('deve rejeitar datas muito distantes no futuro', () => {
      const distantFuture = new Date();
      distantFuture.setFullYear(distantFuture.getFullYear() + 2);
      expect(isValidDateRange(distantFuture.toISOString())).toBe(false);
    });

    it('deve rejeitar datas inválidas', () => {
      expect(isValidDateRange('invalid-date')).toBe(false);
      expect(isValidDateRange('')).toBe(false);
    });
  });

  describe('generateCSRFToken', () => {
    it('deve gerar token com tamanho correto', () => {
      const token = generateCSRFToken();
      expect(token).toHaveLength(64); // 32 bytes * 2 chars per byte
    });

    it('deve gerar tokens únicos', () => {
      const token1 = generateCSRFToken();
      const token2 = generateCSRFToken();
      expect(token1).not.toBe(token2);
    });

    it('deve gerar apenas caracteres hexadecimais', () => {
      const token = generateCSRFToken();
      expect(/^[0-9a-f]+$/.test(token)).toBe(true);
    });
  });

  describe('validateCSRFToken', () => {
    it('deve validar tokens iguais', () => {
      const token = 'abc123def456';
      expect(validateCSRFToken(token, token)).toBe(true);
    });

    it('deve rejeitar tokens diferentes', () => {
      expect(validateCSRFToken('abc123', 'def456')).toBe(false);
    });

    it('deve rejeitar tokens de tamanhos diferentes', () => {
      expect(validateCSRFToken('abc', 'abcdef')).toBe(false);
    });

    it('deve lidar com valores não-string', () => {
      expect(validateCSRFToken(123 as any, 'abc')).toBe(false);
      expect(validateCSRFToken('abc', null as any)).toBe(false);
    });
  });

  describe('validateFileSize', () => {
    it('deve validar arquivos dentro do limite', () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });
      expect(validateFileSize(file, 1)).toBe(true);
    });

    it('deve rejeitar arquivos muito grandes', () => {
      const largeContent = 'a'.repeat(6 * 1024 * 1024); // 6MB
      const file = new File([largeContent], 'large.txt', { type: 'text/plain' });
      expect(validateFileSize(file, 5)).toBe(false);
    });
  });

  describe('validateFileType', () => {
    it('deve validar tipos permitidos', () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });
      expect(validateFileType(file, ['text/plain', 'image/jpeg'])).toBe(true);
    });

    it('deve rejeitar tipos não permitidos', () => {
      const file = new File(['content'], 'test.exe', { type: 'application/x-executable' });
      expect(validateFileType(file, ['text/plain', 'image/jpeg'])).toBe(false);
    });
  });

  describe('containsOnlySafeChars', () => {
    it('deve validar strings seguras', () => {
      expect(containsOnlySafeChars('Ana Silva')).toBe(true);
      expect(containsOnlySafeChars('Email: <EMAIL>')).toBe(true);
      expect(containsOnlySafeChars('Telefone: (11) 99999-9999')).toBe(true);
    });

    it('deve rejeitar caracteres perigosos', () => {
      expect(containsOnlySafeChars('<script>')).toBe(false);
      expect(containsOnlySafeChars('javascript:')).toBe(false);
    });
  });

  describe('cleanText', () => {
    it('deve remover caracteres perigosos', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = cleanText(input);
      expect(result).not.toContain('<script>');
      expect(result).toContain('Hello');
    });

    it('deve remover URLs perigosas', () => {
      expect(cleanText('javascript:alert("xss")')).not.toContain('javascript:');
      expect(cleanText('data:text/html,<script>')).not.toContain('data:');
    });

    it('deve remover event handlers', () => {
      expect(cleanText('onclick=alert("xss")')).not.toContain('onclick=');
      expect(cleanText('onload=malicious()')).not.toContain('onload=');
    });

    it('deve lidar com valores não-string', () => {
      expect(cleanText(123 as any)).toBe('');
      expect(cleanText(null as any)).toBe('');
    });
  });
});
