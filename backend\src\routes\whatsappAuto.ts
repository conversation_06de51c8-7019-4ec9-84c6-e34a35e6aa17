import { Router } from 'express';
import { supabase } from '../config/supabase';
import { wppConnectIntegration } from '../services/wppConnectIntegration';
import { followUpService } from '../services/followUpService';

const router = Router();

// Função para converter telefone brasileiro para formato WhatsApp
const phoneToWhatsApp = (phone: string): string => {
  const cleanPhone = phone.replace(/\D/g, '');
  if (cleanPhone.length === 11) {
    return `55${cleanPhone}@c.us`;
  } else if (cleanPhone.length === 10) {
    return `55${cleanPhone}@c.us`;
  }
  return `${cleanPhone}@c.us`;
};

/**
 * GET /api/whatsapp-auto/status
 * Obter status do sistema WhatsApp Auto
 */
router.get('/status', async (req, res) => {
  try {
    console.log('📊 Status do WhatsApp Auto solicitado');
    
    const status = {
      whatsapp: {
        connected: true,
        sessions: ['gestao-materna', 'rafaela-audio-session'],
        status: 'active'
      },
      audio: {
        processing: true,
        queue: 0,
        processed: 0
      },
      ai: {
        gemini: true,
        status: 'active'
      },
      database: {
        supabase: true,
        status: 'connected'
      }
    };

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao obter status:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/whatsapp-auto/recent-messages
 * Obter mensagens automáticas recentes
 */
router.get('/recent-messages', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    
    console.log('📱 Buscando mensagens automáticas recentes...');
    
    // Buscar mensagens de áudio e respostas automáticas recentes
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        id,
        content,
        type,
        from_me,
        timestamp,
        message_id,
        contacts!inner(id, name, phone, registration_status)
      `)
      .in('type', ['audio', 'text'])
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('❌ Erro ao buscar mensagens:', error);
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    // Formatar mensagens para o frontend
    const formattedMessages = (messages || []).map((msg: any) => ({
      id: msg.id,
      content: msg.content,
      type: msg.type,
      fromMe: msg.from_me,
      timestamp: msg.timestamp,
      contact: {
        id: msg.contacts.id,
        name: msg.contacts.name,
        phone: msg.contacts.phone,
        status: msg.contacts.registration_status
      }
    }));

    console.log(`✅ ${formattedMessages.length} mensagens automáticas encontradas`);

    res.json({
      success: true,
      data: formattedMessages,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao buscar mensagens automáticas:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/whatsapp-auto/logs
 * Obter logs recentes (desenvolvimento)
 */
router.get('/logs', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        error: 'Endpoint de logs não disponível em produção'
      });
    }

    // Simular logs recentes
    const logs = [
      {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: 'Sistema funcionando normalmente',
        service: 'whatsapp-auto'
      },
      {
        timestamp: new Date(Date.now() - 60000).toISOString(),
        level: 'info',
        message: 'Áudio processado com sucesso',
        service: 'audio-queue'
      },
      {
        timestamp: new Date(Date.now() - 120000).toISOString(),
        level: 'info',
        message: 'Mensagem enviada automaticamente',
        service: 'wpp-connect'
      }
    ];

    res.json({
      success: true,
      data: logs,
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao obter logs:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/whatsapp-auto/send-message
 * Enviar mensagem via WhatsApp
 */
router.post('/send-message', async (req, res) => {
  try {
    const { phoneNumber, message } = req.body;

    if (!phoneNumber || !message) {
      return res.status(400).json({
        success: false,
        error: 'phoneNumber e message são obrigatórios'
      });
    }

    console.log(`📱 Enviando mensagem para: ${phoneNumber}`);
    console.log(`💬 Conteúdo: ${message}`);

    // Converter telefone para formato WhatsApp
    const whatsappPhone = phoneToWhatsApp(phoneNumber);
    console.log(`📞 Formato WhatsApp: ${whatsappPhone}`);

    // Enviar via WhatsApp
    const sent = await wppConnectIntegration.sendText(whatsappPhone, message);

    if (sent) {
      console.log('✅ Mensagem enviada com sucesso via WhatsApp');
      res.json({
        success: true,
        message: 'Mensagem enviada com sucesso',
        data: {
          phoneNumber: whatsappPhone,
          content: message,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      console.error('❌ Falha ao enviar mensagem via WhatsApp');
      res.status(500).json({
        success: false,
        error: 'Falha ao enviar mensagem via WhatsApp'
      });
    }

  } catch (error: any) {
    console.error('❌ Erro ao enviar mensagem:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Erro interno do servidor'
    });
  }
});

/**
 * GET /api/whatsapp-auto/follow-up-status
 * Obter status do serviço de follow-up automático
 */
router.get('/follow-up-status', async (req, res) => {
  try {
    const status = followUpService.getStatus();

    res.json({
      success: true,
      data: {
        followUp: status,
        description: 'Serviço de follow-up automático para gestantes',
        testMode: true,
        testInterval: '5 minutos (simulando 1 semana)',
        productionInterval: '1 semana real'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao obter status do follow-up:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/whatsapp-auto/test-follow-up
 * Testar follow-up manual (apenas quando WhatsApp conectado)
 */
router.post('/test-follow-up', async (req, res) => {
  try {
    // Verificar se WhatsApp está conectado
    const whatsappStatus = wppConnectIntegration.getConnectionStatus();
    if (!whatsappStatus?.isConnected || !whatsappStatus?.isAuthenticated) {
      return res.status(400).json({
        success: false,
        error: 'WhatsApp não está conectado',
        whatsappStatus: {
          connected: whatsappStatus?.isConnected || false,
          authenticated: whatsappStatus?.isAuthenticated || false,
          status: whatsappStatus?.sessionStatus || 'unknown'
        }
      });
    }

    // Executar follow-up manualmente
    console.log('🧪 Executando teste de follow-up manual...');
    await followUpService.executeManualFollowUp();

    res.json({
      success: true,
      message: 'Teste de follow-up executado com sucesso',
      whatsappStatus: {
        connected: whatsappStatus.isConnected,
        authenticated: whatsappStatus.isAuthenticated,
        status: whatsappStatus.sessionStatus
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro no teste de follow-up:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
