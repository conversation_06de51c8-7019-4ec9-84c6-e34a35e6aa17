import { describe, it, expect } from 'vitest';
import {
  PregnantWomanSchema,
  CreatePregnantWomanSchema,
  MessageSchema,
  DashboardMetricsSchema,
  ChartDataPointSchema,
  CSVRowSchema,
  safeValidate,
} from '../../schemas/validation';

describe('Validation Schemas', () => {
  describe('PregnantWomanSchema', () => {
    it('deve validar gestante válida', () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 6);

      const validPregnant = {
        id: '1',
        name: '<PERSON> <PERSON>',
        dueDate: futureDate.toISOString(),
        phone: '(11) 99999-9999',
        email: '<EMAIL>',
        observations: 'Primeira gestação',
        createdAt: '2024-01-01T00:00:00.000Z',
        age: 28,
      };

      const result = safeValidate(PregnantWomanSchema, validPregnant);
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        ...validPregnant,
        phone: '11999999999', // Telefone normalizado
      });
    });

    it('deve rejeitar nome inválido', () => {
      const invalidPregnant = {
        id: '1',
        name: 'A', // Muito curto
        dueDate: '2024-06-15T00:00:00.000Z',
        phone: '11999999999',
        createdAt: '2024-01-01T00:00:00.000Z',
      };

      const result = safeValidate(PregnantWomanSchema, invalidPregnant);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve rejeitar telefone inválido', () => {
      const invalidPregnant = {
        id: '1',
        name: 'Ana Silva',
        dueDate: '2024-06-15T00:00:00.000Z',
        phone: '123', // Formato inválido
        createdAt: '2024-01-01T00:00:00.000Z',
      };

      const result = safeValidate(PregnantWomanSchema, invalidPregnant);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve rejeitar data prevista no passado', () => {
      const invalidPregnant = {
        id: '1',
        name: 'Ana Silva',
        dueDate: '2020-01-01T00:00:00.000Z', // Data no passado
        phone: '11999999999',
        createdAt: '2024-01-01T00:00:00.000Z',
      };

      const result = safeValidate(PregnantWomanSchema, invalidPregnant);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve rejeitar idade inválida', () => {
      const invalidPregnant = {
        id: '1',
        name: 'Ana Silva',
        dueDate: '2024-06-15T00:00:00.000Z',
        phone: '11999999999',
        createdAt: '2024-01-01T00:00:00.000Z',
        age: 10, // Idade muito baixa
      };

      const result = safeValidate(PregnantWomanSchema, invalidPregnant);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve normalizar telefone removendo caracteres especiais', () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 6);

      const pregnantWithFormattedPhone = {
        id: '1',
        name: 'Ana Silva',
        dueDate: futureDate.toISOString(),
        phone: '(11) 99999-9999',
        createdAt: '2024-01-01T00:00:00.000Z',
      };

      const result = safeValidate(PregnantWomanSchema, pregnantWithFormattedPhone);
      expect(result.success).toBe(true);
      expect(result.data?.phone).toBe('11999999999');
    });
  });

  describe('CreatePregnantWomanSchema', () => {
    it('deve validar criação sem ID e createdAt', () => {
      const futureDate = new Date();
      futureDate.setMonth(futureDate.getMonth() + 8);

      const newPregnant = {
        name: 'Maria Santos',
        dueDate: futureDate.toISOString(),
        phone: '11888888888',
        email: '<EMAIL>',
        age: 25,
      };

      const result = safeValidate(CreatePregnantWomanSchema, newPregnant);
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        ...newPregnant,
        phone: '11888888888',
      });
    });

    it('deve rejeitar se incluir ID', () => {
      const invalidData = {
        id: '1', // Não deve ter ID na criação
        name: 'Maria Santos',
        dueDate: '2024-08-15T00:00:00.000Z',
        phone: '11888888888',
      };

      const result = safeValidate(CreatePregnantWomanSchema, invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('MessageSchema', () => {
    it('deve validar mensagem válida', () => {
      const validMessage = {
        id: 'msg-1',
        sender: 'user',
        text: 'Olá! Como você está?',
        timestamp: '2024-01-01T12:00:00.000Z',
        status: 'sent',
      };

      const result = safeValidate(MessageSchema, validMessage);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validMessage);
    });

    it('deve rejeitar sender inválido', () => {
      const invalidMessage = {
        id: 'msg-1',
        sender: 'invalid', // Sender inválido
        text: 'Olá!',
        timestamp: '2024-01-01T12:00:00.000Z',
      };

      const result = safeValidate(MessageSchema, invalidMessage);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve rejeitar texto muito longo', () => {
      const invalidMessage = {
        id: 'msg-1',
        sender: 'user',
        text: 'a'.repeat(1001), // Texto muito longo
        timestamp: '2024-01-01T12:00:00.000Z',
      };

      const result = safeValidate(MessageSchema, invalidMessage);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('DashboardMetricsSchema', () => {
    it('deve validar métricas válidas', () => {
      const validMetrics = {
        totalPregnantWomen: 25,
        messagesSentThisMonth: 150,
        activeConversations: 12,
      };

      const result = safeValidate(DashboardMetricsSchema, validMetrics);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validMetrics);
    });

    it('deve rejeitar valores negativos', () => {
      const invalidMetrics = {
        totalPregnantWomen: -5,
        messagesSentThisMonth: 150,
        activeConversations: 12,
      };

      const result = safeValidate(DashboardMetricsSchema, invalidMetrics);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('deve aceitar métricas sem avgWeeksGestation', () => {
      const validMetrics = {
        totalPregnantWomen: 25,
        messagesSentThisMonth: 150,
        activeConversations: 12,
      };

      const result = safeValidate(DashboardMetricsSchema, validMetrics);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validMetrics);
    });
  });

  describe('ChartDataPointSchema', () => {
    it('deve validar ponto de dados válido', () => {
      const validDataPoint = {
        name: 'Janeiro',
        value: 15,
      };

      const result = safeValidate(ChartDataPointSchema, validDataPoint);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(validDataPoint);
    });

    it('deve rejeitar valor negativo', () => {
      const invalidDataPoint = {
        name: 'Janeiro',
        value: -5,
      };

      const result = safeValidate(ChartDataPointSchema, invalidDataPoint);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('CSVRowSchema', () => {
    it('deve validar linha de CSV válida', () => {
      const validRow = {
        nome: 'Ana Silva',
        telefone: '11999999999',
        'data-prevista': '2024-06-15',
        email: '<EMAIL>',
        idade: '28',
      };

      const result = safeValidate(CSVRowSchema, validRow);
      expect(result.success).toBe(true);
      expect(result.data?.idade).toBe(28); // Convertido para número
    });

    it('deve rejeitar telefone inválido no CSV', () => {
      const invalidRow = {
        nome: 'Ana Silva',
        telefone: '123', // Formato inválido
        'data-prevista': '2024-06-15',
      };

      const result = safeValidate(CSVRowSchema, invalidRow);
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('safeValidate', () => {
    it('deve retornar sucesso para dados válidos', () => {
      const validData = { name: 'Test', value: 10 };
      const result = safeValidate(ChartDataPointSchema, validData);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(validData);
      expect(result.error).toBeNull();
    });

    it('deve retornar erro para dados inválidos', () => {
      const invalidData = { name: '', value: -5 };
      const result = safeValidate(ChartDataPointSchema, invalidData);

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeDefined();
      expect(Array.isArray(result.error)).toBe(true);
    });

    it('deve lidar com erros não-Zod', () => {
      const schema = {
        parse: () => {
          throw new Error('Erro customizado');
        },
      } as any;

      const result = safeValidate(schema, {});

      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toEqual([{ message: 'Erro de validação desconhecido', path: [] }]);
    });
  });
});
