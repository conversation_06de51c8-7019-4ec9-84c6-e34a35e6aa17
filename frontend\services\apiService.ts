import axios, { AxiosInstance, AxiosError } from 'axios';
import { toast } from 'react-toastify';
import { authService } from './authService';
import { 
  PregnantWomanSchema, 
  DashboardMetricsSchema, 
  MessageSchema,
  ChartDataPointSchema,
  safeValidate 
} from '../schemas/validation';
import type {
  PregnantWoman,
  DashboardMetrics,
  Message,
  ChartDataPoint,
  CreatePregnantWoman,
  UpdatePregnantWoman
} from '../schemas/validation';


export interface Contact {
  id?: string;
  _id?: string;
  name: string;
  phone: string;
  email?: string;
  baby_gender?: 'male' | 'female' | 'unknown';
  babyGender?: 'male' | 'female' | 'unknown';
  registration_status?: 'registered' | 'unregistered' | 'not_interested';
  is_active?: boolean;
  last_interaction?: string;
  created_at?: string;
  updated_at?: string;
}

const API_BASE_URL = (import.meta.env.VITE_API_URL || 'http://localhost:3002') + '/api';
const API_TIMEOUT = 10000;

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });


    this.client.interceptors.request.use(
      (config) => {

        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );


    this.client.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleApiError(error: AxiosError) {
    if (error.response) {
      // Erro do servidor
      const status = error.response.status;
      const message = (error.response.data as any)?.message || 'Erro no servidor';
      
      switch (status) {
        case 400:
          toast.error(`Dados inválidos: ${message}`);
          break;
        case 401:
          toast.error('Não autorizado. Faça login novamente.');
          // Limpar dados de autenticação e redirecionar
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
          break;
        case 403:
          toast.error('Acesso negado.');
          break;
        case 404:
          toast.error('Recurso não encontrado.');
          break;
        case 500:
          toast.error('Erro interno do servidor. Tente novamente.');
          break;
        default:
          toast.error(`Erro: ${message}`);
      }
    } else if (error.request) {
      // Erro de rede
      toast.error('Erro de conexão. Verifique sua internet.');
    } else {
      // Erro desconhecido
      toast.error('Erro inesperado. Tente novamente.');
    }
  }

  // Gestantes
  async getPregnantWomen(): Promise<PregnantWoman[]> {
    try {
      const response = await this.client.get('/contacts');
      const validation = safeValidate(PregnantWomanSchema.array(), response.data);

      if (!validation.success) {
        console.error('Erro de validação:', validation.error);
        throw new Error('Dados inválidos recebidos do servidor');
      }

      return validation.data;
    } catch (error) {
      console.error('Erro ao buscar gestantes:', error);
      throw error;
    }
  }

  // Alias para compatibilidade
  async getContacts(): Promise<any[]> {
    try {
      const response = await this.client.get('/contacts');
      console.log('📊 Resposta da API /contacts:', response.data);

      // Se a resposta for um array, retornar diretamente
      if (Array.isArray(response.data)) {
        return response.data;
      }

      // Se a resposta for um objeto com propriedade contacts
      if (response.data && response.data.contacts && Array.isArray(response.data.contacts)) {
        return response.data.contacts;
      }

      // Se a resposta for um objeto com propriedade data
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      // Caso contrário, retornar array vazio
      console.warn('⚠️ Resposta da API não é um array:', response.data);
      return [];
    } catch (error) {
      console.error('❌ Erro ao buscar contatos:', error);
      throw error;
    }
  }

  async createPregnantWoman(data: CreatePregnantWoman): Promise<PregnantWoman> {
    try {
      const response = await this.client.post('/contacts', data);
      const validation = safeValidate(PregnantWomanSchema, response.data);
      
      if (!validation.success) {
        throw new Error('Dados inválidos retornados pelo servidor');
      }
      
      toast.success('Gestante cadastrada com sucesso!');
      return validation.data;
    } catch (error) {
      console.error('Erro ao criar gestante:', error);
      throw error;
    }
  }

  async updatePregnantWoman(data: UpdatePregnantWoman): Promise<PregnantWoman> {
    try {
      const response = await this.client.put(`/contacts/${data.id}`, data);
      const validation = safeValidate(PregnantWomanSchema, response.data);
      
      if (!validation.success) {
        throw new Error('Dados inválidos retornados pelo servidor');
      }
      
      toast.success('Gestante atualizada com sucesso!');
      return validation.data;
    } catch (error) {
      console.error('Erro ao atualizar gestante:', error);
      throw error;
    }
  }

  async deletePregnantWoman(id: string): Promise<void> {
    try {
      await this.client.delete(`/contacts/${id}`);
      toast.success('Gestante removida com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar gestante:', error);
      throw error;
    }
  }

  // Alias para compatibilidade
  async deleteContact(id: string): Promise<void> {
    return this.deletePregnantWoman(id);
  }

  // Criar contato (alias para compatibilidade)
  async createContact(data: any): Promise<any> {
    try {
      const response = await this.client.post('/contacts', {
        name: data.name,
        phone: data.phone,
        babyGender: data.babyGender || data.baby_gender || 'unknown'
      });

      console.log('✅ Contato criado:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao criar contato:', error);
      throw error;
    }
  }

  // Atualizar contato (alias para compatibilidade)
  async updateContact(id: string, data: any): Promise<any> {
    try {
      const response = await this.client.put(`/contacts/${id}`, {
        name: data.name,
        phone: data.phone,
        babyGender: data.babyGender || data.baby_gender || 'unknown'
      });

      console.log('✅ Contato atualizado:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao atualizar contato:', error);
      throw error;
    }
  }

  // Gerar mensagem de acompanhamento via IA
  async generateFollowUp(contactId: string): Promise<{ message: string }> {
    try {
      const response = await this.client.post(`/ai/generate-followup`, {
        contactId
      });

      if (!response.data || !response.data.message) {
        throw new Error('Resposta inválida da API');
      }

      return response.data;
    } catch (error) {
      console.error('Erro ao gerar acompanhamento:', error);
      throw error;
    }
  }

  // Dashboard
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const response = await this.client.get('/analytics/dashboard');

      console.log('📊 Resposta completa do analytics:', JSON.stringify(response.data, null, 2));

      // Mapear dados do backend para o formato esperado pelo frontend
      const backendData = response.data;
      console.log('📊 Dados do backend:', backendData);
      console.log('📊 Contacts do backend:', backendData.contacts);
      console.log('📊 Total de contacts:', backendData.contacts?.total);

      // O backend está retornando formato: {totalContacts, babyGenders: {...}, messages: {...}}
      const mappedData = {
        totalPregnantWomen: backendData.totalContacts || 0,
        avgWeeksGestation: 0, // Removido - não usamos mais semanas gestacionais
        messagesSentThisMonth: backendData.messages?.total || 0,
        activeConversations: backendData.totalContacts || 0
      };

      console.log('📊 Dados mapeados:', mappedData);

      const validation = safeValidate(DashboardMetricsSchema, mappedData);

      if (!validation.success) {
        console.error('❌ Erro de validação:', validation.error);
        throw new Error('Dados inválidos recebidos do servidor');
      }

      console.log('✅ Dados validados:', validation.data);
      return validation.data;
    } catch (error) {
      console.error('❌ Erro ao buscar métricas:', error);
      throw error;
    }
  }

  async getNewRegistrationsData(): Promise<ChartDataPoint[]> {
    try {
      console.log('📊 Buscando dados reais de novas gestantes cadastradas...');

      const response = await this.client.get('/analytics/new-registrations');

      // Validar se a resposta é um array
      if (!Array.isArray(response.data)) {
        console.warn('⚠️ Resposta da API não é um array:', response.data);
        return [];
      }

      console.log('📊 Dados de novas gestantes recebidos:', response.data);

      const validation = safeValidate(ChartDataPointSchema.array(), response.data);

      if (!validation.success) {
        console.error('❌ Erro de validação:', validation.error);
        // Retornar dados mesmo com erro de validação, mas loggar o problema
        return response.data;
      }

      return validation.data;
    } catch (error) {
      console.error('❌ Erro ao buscar dados de novas gestantes:', error);

      // Retornar array vazio em caso de erro
      return [];
    }
  }

  async getAgeDistributionData(): Promise<ChartDataPoint[]> {
    try {
      const response = await this.client.get('/analytics/dashboard');
      const backendData = response.data;

      // Mapear dados de gênero do bebê para distribuição
      // O backend retorna: {babyGenders: {male, female, unknown}}
      const babyGenders = backendData.babyGenders || {};
      const mappedData = [
        { name: 'Meninos', value: babyGenders.male || 0 },
        { name: 'Meninas', value: babyGenders.female || 0 },
        { name: 'Não informado', value: babyGenders.unknown || 0 }
      ];

      const validation = safeValidate(ChartDataPointSchema.array(), mappedData);

      if (!validation.success) {
        console.error('Erro de validação:', validation.error);
        throw new Error('Dados inválidos recebidos do servidor');
      }

      return validation.data;
    } catch (error) {
      console.error('Erro ao buscar distribuição de idade:', error);
      throw error;
    }
  }

  async getFullAnalytics(): Promise<any> {
    try {
      const response = await this.client.get('/analytics/dashboard');
      console.log('📊 Analytics completo recebido:', response.data);

      // Mapear dados para o formato esperado pelo dashboard
      const backendData = response.data;
      return {
        dailyActivity: [], // Por enquanto vazio, pode ser implementado depois
        topContacts: [], // Por enquanto vazio, pode ser implementado depois
        totalContacts: backendData.totalContacts || 0,
        pregnancyStages: backendData.pregnancyStages || {},
        messages: backendData.messages || {}
      };
    } catch (error) {
      console.error('Erro ao buscar analytics completo:', error);
      throw error;
    }
  }

  // Mensagens
  async getMessages(pregnantWomanId: string): Promise<Message[]> {
    try {
      const response = await this.client.get(`/messages/${pregnantWomanId}`);
      const validation = safeValidate(MessageSchema.array(), response.data);
      
      if (!validation.success) {
        console.error('Erro de validação:', validation.error);
        throw new Error('Dados inválidos recebidos do servidor');
      }
      
      return validation.data;
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      throw error;
    }
  }

  async sendMessage(phone: string, text: string): Promise<any> {
    try {
      console.log('📤 Enviando mensagem via WhatsApp para:', phone);
      const response = await this.client.post('/whatsapp-auto/send-message', {
        phoneNumber: phone,
        message: text,
      });

      console.log('✅ Resposta do envio:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  // Método original para compatibilidade
  async sendMessageToPregnantWoman(pregnantWomanId: string, text: string): Promise<Message> {
    try {
      const response = await this.client.post('/messages/send', {
        pregnantWomanId,
        text,
      });

      const validation = safeValidate(MessageSchema, response.data);

      if (!validation.success) {
        throw new Error('Dados inválidos retornados pelo servidor');
      }

      return validation.data;
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  // WhatsApp
  async getWhatsAppStatus(): Promise<{ status: string; qrCode?: string }> {
    try {
      const response = await this.client.get('/whatsapp/status');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar status do WhatsApp:', error);
      throw error;
    }
  }

  async connectWhatsApp(): Promise<void> {
    try {
      await this.client.post('/whatsapp/connect');
    } catch (error) {
      console.error('Erro ao conectar WhatsApp:', error);
      throw error;
    }
  }

  async disconnectWhatsApp(): Promise<void> {
    try {
      await this.client.post('/whatsapp/disconnect');
    } catch (error) {
      console.error('Erro ao desconectar WhatsApp:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      console.error('Erro no health check:', error);
      throw error;
    }
  }

  // Transcrições de áudio
  async getTranscriptions(limit: number = 20, offset: number = 0): Promise<any> {
    try {
      const response = await this.client.get(`/transcriptions?limit=${limit}&offset=${offset}`);
      console.log('📝 Transcrições recebidas:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao buscar transcrições:', error);
      throw error;
    }
  }

  async getContactTranscriptions(contactId: string): Promise<any> {
    try {
      const response = await this.client.get(`/contacts/${contactId}/transcriptions`);
      console.log('📝 Transcrições do contato recebidas:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ Erro ao buscar transcrições do contato:', error);
      throw error;
    }
  }
}

// Instância singleton
export const apiService = new ApiService();
export default apiService;
