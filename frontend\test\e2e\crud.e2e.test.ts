import { test, expect } from '@playwright/test';

// Configuração base para testes E2E
const BASE_URL = 'http://localhost:5173';

test.describe('Testes E2E - CRUD de Gestantes', () => {
  test.beforeEach(async ({ page }) => {
    // Navegar para a página principal
    await page.goto(BASE_URL);
    
    // Aguardar a página carregar completamente
    await page.waitForLoadState('networkidle');
  });

  test('deve navegar para página de gestantes', async ({ page }) => {
    // Clicar no menu de gestantes
    await page.click('[data-testid="menu-gestantes"]');
    
    // Verificar se chegou na página correta
    await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
    await expect(page.locator('h1')).toContainText('Gerenciamento de Gestantes');
  });

  test('deve listar gestantes existentes', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Aguardar a tabela carregar
    await page.waitForSelector('[data-testid="pregnant-table"]');
    
    // Verificar se há gestantes na lista
    const rows = page.locator('[data-testid="pregnant-table"] tbody tr');
    await expect(rows).toHaveCount.greaterThan(0);
  });

  test('deve criar nova gestante', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Clicar no botão "Nova Gestante"
    await page.click('[data-testid="new-pregnant-button"]');
    
    // Aguardar modal abrir
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Preencher formulário
    await page.fill('[aria-label="Nome"]', 'Maria da Silva E2E');
    await page.fill('[aria-label="Telefone"]', '11999999999');
    await page.fill('[aria-label="Email"]', '<EMAIL>');
    
    // Submeter formulário
    await page.click('button[type="submit"]');
    
    // Verificar se gestante foi criada
    await expect(page.locator('text=Gestante cadastrada com sucesso!')).toBeVisible();
    
    // Verificar se aparece na lista
    await expect(page.locator('text=Maria da Silva E2E')).toBeVisible();
  });

  test('deve editar gestante existente', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Aguardar tabela carregar
    await page.waitForSelector('[data-testid="pregnant-table"]');
    
    // Clicar no primeiro botão de editar
    await page.click('[data-testid="pregnant-table"] button:has-text("Editar")');
    
    // Aguardar modal abrir
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Verificar se dados estão preenchidos
    const nameInput = page.locator('[aria-label="Nome"]');
    await expect(nameInput).not.toHaveValue('');
    
    // Alterar nome
    await nameInput.fill('Nome Editado E2E');
    
    // Salvar alterações
    await page.click('button[type="submit"]');
    
    // Verificar sucesso
    await expect(page.locator('text=Gestante atualizada com sucesso!')).toBeVisible();
    
    // Verificar se nome foi alterado na lista
    await expect(page.locator('text=Nome Editado E2E')).toBeVisible();
  });

  test('deve excluir gestante', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Aguardar tabela carregar
    await page.waitForSelector('[data-testid="pregnant-table"]');
    
    // Obter nome da primeira gestante
    const firstRowName = await page.locator('[data-testid="pregnant-table"] tbody tr:first-child td:first-child').textContent();
    
    // Clicar no botão excluir
    await page.click('[data-testid="pregnant-table"] tbody tr:first-child button:has-text("Excluir")');
    
    // Aguardar modal de confirmação
    await page.waitForSelector('[data-testid="delete-confirm-modal"]');
    
    // Verificar se nome aparece na confirmação
    await expect(page.locator(`text=${firstRowName}`)).toBeVisible();
    
    // Confirmar exclusão
    await page.click('button:has-text("Excluir")');
    
    // Verificar sucesso
    await expect(page.locator('text=Gestante removida com sucesso!')).toBeVisible();
    
    // Verificar se gestante foi removida da lista
    await expect(page.locator(`text=${firstRowName}`)).not.toBeVisible();
  });

  test('deve buscar gestantes', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Aguardar tabela carregar
    await page.waitForSelector('[data-testid="pregnant-table"]');
    
    // Obter total de linhas inicial
    const initialRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
    
    // Buscar por um nome específico
    await page.fill('[data-testid="search-input"]', 'Ana');
    
    // Aguardar filtro ser aplicado
    await page.waitForTimeout(500);
    
    // Verificar se resultados foram filtrados
    const filteredRows = await page.locator('[data-testid="pregnant-table"] tbody tr').count();
    expect(filteredRows).toBeLessThanOrEqual(initialRows);
    
    // Verificar se todas as linhas visíveis contêm "Ana"
    const visibleNames = await page.locator('[data-testid="pregnant-table"] tbody tr td:first-child').allTextContents();
    visibleNames.forEach(name => {
      expect(name.toLowerCase()).toContain('ana');
    });
  });

  test('deve validar campos obrigatórios', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Abrir modal de criação
    await page.click('[data-testid="new-pregnant-button"]');
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Tentar submeter sem preencher campos
    await page.click('button[type="submit"]');
    
    // Verificar se validação HTML5 impede submissão
    const nameInput = page.locator('[aria-label="Nome"]');
    await expect(nameInput).toHaveAttribute('required');
    
    // Verificar se modal ainda está aberto (não foi submetido)
    await expect(page.locator('[data-testid="pregnant-form-modal"]')).toBeVisible();
  });

  test('deve cancelar criação de gestante', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Abrir modal
    await page.click('[data-testid="new-pregnant-button"]');
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Preencher alguns campos
    await page.fill('[aria-label="Nome"]', 'Teste Cancelar');
    
    // Cancelar
    await page.click('button:has-text("Cancelar")');
    
    // Verificar se modal fechou
    await expect(page.locator('[data-testid="pregnant-form-modal"]')).not.toBeVisible();
    
    // Verificar se gestante não foi criada
    await expect(page.locator('text=Teste Cancelar')).not.toBeVisible();
  });

  test('deve exibir mensagens de erro para dados inválidos', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Abrir modal
    await page.click('[data-testid="new-pregnant-button"]');
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Preencher com dados inválidos
    await page.fill('[aria-label="Nome"]', 'A'); // Muito curto
    await page.fill('[aria-label="Telefone"]', '123'); // Inválido
    await page.fill('[aria-label="Email"]', 'email-inválido'); // Formato inválido
    
    // Tentar submeter
    await page.click('button[type="submit"]');
    
    // Verificar se validação impede submissão
    const emailInput = page.locator('[aria-label="Email"]');
    await expect(emailInput).toHaveAttribute('type', 'email');
    
    // Modal deve permanecer aberto
    await expect(page.locator('[data-testid="pregnant-form-modal"]')).toBeVisible();
  });

  test('deve funcionar em dispositivos móveis', async ({ page }) => {
    // Simular viewport mobile
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Verificar se página é responsiva
    await expect(page.locator('h1')).toBeVisible();
    
    // Verificar se tabela é scrollável horizontalmente
    const table = page.locator('[data-testid="pregnant-table"]');
    await expect(table).toBeVisible();
    
    // Testar criação em mobile
    await page.click('[data-testid="new-pregnant-button"]');
    await page.waitForSelector('[data-testid="pregnant-form-modal"]');
    
    // Modal deve ser visível e utilizável
    await expect(page.locator('[aria-label="Nome"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('deve manter estado durante navegação', async ({ page }) => {
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Fazer uma busca
    await page.fill('[data-testid="search-input"]', 'Ana');
    await page.waitForTimeout(500);
    
    // Navegar para outra página
    await page.click('[data-testid="menu-dashboard"]');
    await expect(page).toHaveURL(`${BASE_URL}/dashboard`);
    
    // Voltar para gestantes
    await page.click('[data-testid="menu-gestantes"]');
    await expect(page).toHaveURL(`${BASE_URL}/gestantes`);
    
    // Verificar se busca foi mantida (ou limpa, dependendo do comportamento esperado)
    const searchValue = await page.locator('[data-testid="search-input"]').inputValue();
    // Pode ser '' (limpo) ou 'Ana' (mantido) - depende da implementação
    expect(typeof searchValue).toBe('string');
  });
});
