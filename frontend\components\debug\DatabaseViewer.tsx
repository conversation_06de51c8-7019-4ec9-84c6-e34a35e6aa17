import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/apiService';

interface DatabaseData {
  collections: string[];
  data: Record<string, {
    count: number;
    sampleData: any[];
    error?: string;
  }>;
  database: string;
  timestamp: string;
}

const DatabaseViewer: React.FC = () => {
  const [data, setData] = useState<DatabaseData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDatabaseData();
  }, []);

  const loadDatabaseData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fazer requisição para a rota de debug
      const response = await fetch('http://localhost:3000/api/debug/database', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result);
      console.log('📊 Dados do banco carregados:', result);
    } catch (err) {
      console.error('❌ Erro ao carregar dados do banco:', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">📊 Dados do Banco MongoDB</h1>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Carregando dados do banco...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">📊 Dados do Banco MongoDB</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Erro:</strong> {error}
        </div>
        <button
          onClick={loadDatabaseData}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">📊 Dados do Banco MongoDB</h1>
        <div className="text-gray-500">Nenhum dado disponível</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">📊 Dados do Banco MongoDB</h1>
        <button
          onClick={loadDatabaseData}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          🔄 Atualizar
        </button>
      </div>

      <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-6">
        <strong>Banco:</strong> {data.database} | 
        <strong> Última atualização:</strong> {new Date(data.timestamp).toLocaleString('pt-BR')}
      </div>

      <div className="grid gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">📋 Resumo das Coleções</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.collections.map(collection => (
              <div key={collection} className="bg-gray-50 p-4 rounded">
                <h3 className="font-medium text-lg">{collection}</h3>
                <p className="text-gray-600">
                  {data.data[collection]?.count || 0} documentos
                </p>
                {data.data[collection]?.error && (
                  <p className="text-red-500 text-sm mt-1">
                    Erro: {data.data[collection].error}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>

        {Object.entries(data.data).map(([collectionName, collectionData]) => (
          <div key={collectionName} className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              📄 {collectionName} ({collectionData.count} documentos)
            </h2>
            
            {collectionData.error ? (
              <div className="text-red-500">
                Erro ao carregar dados: {collectionData.error}
              </div>
            ) : (
              <div className="space-y-4">
                {collectionData.sampleData.length === 0 ? (
                  <p className="text-gray-500">Nenhum documento encontrado</p>
                ) : (
                  collectionData.sampleData.map((doc, index) => (
                    <div key={index} className="bg-gray-50 p-4 rounded">
                      <h4 className="font-medium mb-2">Documento {index + 1}:</h4>
                      <pre className="text-sm bg-gray-100 p-3 rounded overflow-x-auto">
                        {JSON.stringify(doc, null, 2)}
                      </pre>
                    </div>
                  ))
                )}
                
                {collectionData.count > 5 && (
                  <p className="text-gray-500 text-sm">
                    Mostrando apenas os primeiros 5 documentos de {collectionData.count} total.
                  </p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DatabaseViewer;
