/**
 * Serviço RAG (Retrieval-Augmented Generation)
 * Integra busca semântica com Gemini AI para respostas contextualizadas
 */

import { simpleVectorDatabase, SearchResult } from './simpleVectorDB';
import { geminiService } from './gemini';

export interface RAGQuery {
  question: string;
  context?: string;
  category?: string;
  documentType?: string;
  maxResults?: number;
  threshold?: number;
}

export interface RAGResponse {
  answer: string;
  sources: SearchResult[];
  confidence: number;
  processingTime: number;
  usedContext: boolean;
  metadata: {
    queryType: string;
    documentsFound: number;
    averageSimilarity: number;
    geminiModel: string;
  };
}

export interface ContextualPrompt {
  systemPrompt: string;
  userPrompt: string;
  context: string;
  sources: string[];
}

export class RAGService {
  private geminiService = geminiService;

  constructor() {
    // Usar instância singleton
  }

  /**
   * Processar consulta RAG completa
   */
  async processQuery(query: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Processando consulta RAG: "${query.question}"`);

      // 1. Buscar documentos relevantes
      const searchResults = await this.searchRelevantDocuments(query);
      
      // 2. Construir contexto
      const contextualPrompt = this.buildContextualPrompt(query.question, searchResults);
      
      // 3. Gerar resposta com Gemini
      const answer = await this.generateContextualAnswer(contextualPrompt);
      
      // 4. Calcular métricas
      const confidence = this.calculateConfidence(searchResults, answer);
      const averageSimilarity = searchResults.length > 0 
        ? searchResults.reduce((sum, r) => sum + r.similarity, 0) / searchResults.length 
        : 0;

      const processingTime = Date.now() - startTime;

      console.log(`✅ Consulta RAG processada em ${processingTime}ms`);

      return {
        answer,
        sources: searchResults,
        confidence,
        processingTime,
        usedContext: searchResults.length > 0,
        metadata: {
          queryType: this.classifyQueryType(query.question),
          documentsFound: searchResults.length,
          averageSimilarity,
          geminiModel: 'gemini-1.5-flash'
        }
      };

    } catch (error) {
      console.error('❌ Erro no processamento RAG:', error);
      
      // Fallback: resposta sem contexto
      const fallbackAnswer = await this.generateFallbackAnswer(query.question);
      
      return {
        answer: fallbackAnswer,
        sources: [],
        confidence: 0.3,
        processingTime: Date.now() - startTime,
        usedContext: false,
        metadata: {
          queryType: 'fallback',
          documentsFound: 0,
          averageSimilarity: 0,
          geminiModel: 'gemini-1.5-flash'
        }
      };
    }
  }

  /**
   * Buscar documentos relevantes
   */
  private async searchRelevantDocuments(query: RAGQuery): Promise<SearchResult[]> {
    const {
      question,
      category,
      documentType,
      maxResults = 5,
      threshold = 0.7
    } = query;

    // Construir filtros
    const filters: Record<string, any> = {};
    if (category) filters.category = category;
    if (documentType) filters.documentType = documentType;

    // Buscar documentos
    const results = await simpleVectorDatabase.search(question, {
      limit: maxResults,
      threshold,
      filter: filters,
      includeMetadata: true
    });

    console.log(`📊 Encontrados ${results.length} documentos relevantes`);
    return results;
  }

  /**
   * Construir prompt contextual
   */
  private buildContextualPrompt(question: string, sources: SearchResult[]): ContextualPrompt {
    const context = sources.length > 0 
      ? sources.map((source, index) => 
          `[Documento ${index + 1}] ${source.content}`
        ).join('\n\n')
      : '';

    const sourcesList = sources.map(source => 
      `- ${source.metadata.title} (${source.metadata.category})`
    );

    const systemPrompt = `Você é Rafaela, a assistente virtual do gabinete da vereadora. Sua função é responder perguntas dos cidadãos com base no conhecimento oficial do gabinete.

INSTRUÇÕES:
1. Use APENAS as informações fornecidas no contexto para responder
2. Se a informação não estiver no contexto, diga que não tem essa informação específica
3. Seja clara, objetiva e útil
4. Mantenha o tom profissional mas acessível
5. Se apropriado, sugira como o cidadão pode obter mais informações
6. Sempre cite as fontes quando usar informações específicas

CONTEXTO DISPONÍVEL:
${context || 'Nenhum documento específico encontrado para esta consulta.'}`;

    const userPrompt = `Pergunta do cidadão: ${question}

Por favor, responda com base no contexto fornecido. Se não houver informação suficiente no contexto, explique isso e sugira alternativas.`;

    return {
      systemPrompt,
      userPrompt,
      context,
      sources: sourcesList
    };
  }

  /**
   * Gerar resposta contextual com Gemini
   */
  private async generateContextualAnswer(prompt: ContextualPrompt): Promise<string> {
    try {
      const response = await this.geminiService.generateRAGResponse(
        prompt.userPrompt,
        prompt.systemPrompt
      );

      return response;
    } catch (error) {
      console.error('❌ Erro ao gerar resposta contextual:', error);
      throw error;
    }
  }

  /**
   * Gerar resposta de fallback
   */
  private async generateFallbackAnswer(question: string): Promise<string> {
    const fallbackPrompt = `Você é Rafaela, assistente do gabinete da vereadora. 

O cidadão perguntou: "${question}"

Não foi possível encontrar informações específicas na base de conhecimento do gabinete. 
Responda de forma educada, explicando que você não tem essa informação específica no momento, 
e sugira que o cidadão entre em contato diretamente com o gabinete para obter informações mais detalhadas.

Forneça os contatos do gabinete se apropriado.`;

    try {
      return await this.geminiService.generateRAGResponse(question, fallbackPrompt);
    } catch (error) {
      return `Olá! Sou a Rafaela, assistente virtual do gabinete da vereadora. 

Infelizmente, não encontrei informações específicas sobre sua pergunta em nossa base de conhecimento no momento. 

Para obter informações mais detalhadas, recomendo que entre em contato diretamente com nosso gabinete:
- WhatsApp: [número do gabinete]
- E-mail: [email do gabinete]
- Telefone: [telefone do gabinete]

Nossa equipe terá prazer em ajudá-lo com informações mais específicas! 🧡`;
    }
  }

  /**
   * Calcular confiança da resposta
   */
  private calculateConfidence(sources: SearchResult[], answer: string): number {
    if (sources.length === 0) return 0.3;

    // Fatores para calcular confiança
    const avgSimilarity = sources.reduce((sum, s) => sum + s.similarity, 0) / sources.length;
    const sourceCount = Math.min(sources.length / 5, 1); // Normalizar para máximo 5 fontes
    const answerLength = Math.min(answer.length / 500, 1); // Respostas mais longas podem ser mais detalhadas

    // Peso dos fatores
    const confidence = (
      avgSimilarity * 0.6 +      // 60% baseado na similaridade
      sourceCount * 0.3 +        // 30% baseado no número de fontes
      answerLength * 0.1         // 10% baseado no tamanho da resposta
    );

    return Math.min(Math.max(confidence, 0), 1); // Garantir entre 0 e 1
  }

  /**
   * Classificar tipo de consulta
   */
  private classifyQueryType(question: string): string {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('lei') || lowerQuestion.includes('projeto') || lowerQuestion.includes('legisla')) {
      return 'legislation';
    }
    if (lowerQuestion.includes('serviço') || lowerQuestion.includes('como') || lowerQuestion.includes('solicitar')) {
      return 'service';
    }
    if (lowerQuestion.includes('quando') || lowerQuestion.includes('agenda') || lowerQuestion.includes('reunião')) {
      return 'schedule';
    }
    if (lowerQuestion.includes('saúde') || lowerQuestion.includes('hospital') || lowerQuestion.includes('médico')) {
      return 'health';
    }
    if (lowerQuestion.includes('educação') || lowerQuestion.includes('escola') || lowerQuestion.includes('ensino')) {
      return 'education';
    }
    if (lowerQuestion.includes('rua') || lowerQuestion.includes('asfalto') || lowerQuestion.includes('buraco')) {
      return 'infrastructure';
    }

    return 'general';
  }

  /**
   * Buscar por categoria específica
   */
  async searchByCategory(
    question: string, 
    category: string, 
    limit: number = 3
  ): Promise<RAGResponse> {
    return this.processQuery({
      question,
      category,
      maxResults: limit
    });
  }

  /**
   * Buscar informações sobre legislação
   */
  async searchLegislation(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'legislation',
      category: 'legislation',
      maxResults: 3
    });
  }

  /**
   * Buscar informações sobre serviços
   */
  async searchServices(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'service',
      maxResults: 5
    });
  }

  /**
   * Buscar FAQ
   */
  async searchFAQ(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'faq',
      maxResults: 3,
      threshold: 0.6 // Threshold menor para FAQ
    });
  }

  /**
   * Obter estatísticas do serviço RAG
   */
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    return await simpleVectorDatabase.getStats();
  }
}

// Instância singleton
export const ragService = new RAGService();
