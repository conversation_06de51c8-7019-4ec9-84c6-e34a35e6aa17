/**
 * Serviço RAG (Retrieval-Augmented Generation)
 * Integra busca semântica com Gemini AI para respostas contextualizadas
 */

import { simpleVectorDatabase, SearchResult } from './simpleVectorDB';
import { chromaVectorDB, ChromaSearchResult } from './chromaVectorDB';
import { geminiService } from './gemini';
import { rerankingService } from './rerankingService';
import { semanticCache } from './semanticCache';
import { queryExpansionService } from './queryExpansion';
import { hybridEmbeddingService } from './hybridEmbeddings';
import { feedbackLearningService } from './feedbackLearning';
import { incrementalIndexingService } from './incrementalIndexing';

export interface RAGQuery {
  question: string;
  context?: string;
  category?: string;
  documentType?: string;
  maxResults?: number;
  threshold?: number;
  useHybridEmbeddings?: boolean;
  useChromaDB?: boolean;
  sessionId?: string;
  userId?: string;
}

export interface RAGResponse {
  answer: string;
  sources: SearchResult[];
  confidence: number;
  processingTime: number;
  usedContext: boolean;
  metadata: {
    queryType: string;
    documentsFound: number;
    averageSimilarity: number;
    geminiModel: string;
  };
}

export interface ContextualPrompt {
  systemPrompt: string;
  userPrompt: string;
  context: string;
  sources: string[];
}

export class RAGService {
  private geminiService = geminiService;

  constructor() {
    // Usar instância singleton
  }

  /**
   * Processar consulta RAG completa
   */
  async processQuery(query: RAGQuery): Promise<RAGResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Processando consulta RAG avançada: "${query.question}"`);

      // 1. Verificar cache semântico primeiro
      const cachedResponse = await semanticCache.get(query.question);
      if (cachedResponse) {
        return cachedResponse;
      }

      // 2. Expandir query para melhor busca
      const expandedQuery = await queryExpansionService.expandQuery(query.question);
      const intentAnalysis = queryExpansionService.detectGabineteIntent(query.question);

      // 3. Escolher sistema de busca (ChromaDB ou SimpleDB)
      const useChromaDB = query.useChromaDB !== false; // Default true
      const useHybridEmbeddings = query.useHybridEmbeddings !== false; // Default true

      // 4. Busca híbrida com múltiplas queries
      const searchQueries = await queryExpansionService.generateSearchQueries(query.question);
      let allResults: (SearchResult | ChromaSearchResult)[] = [];

      if (useChromaDB) {
        // Usar ChromaDB com embeddings híbridos
        for (const searchQuery of searchQueries) {
          const results = await this.searchWithChromaDB(searchQuery, {
            ...query,
            threshold: query.threshold || 0.5,
            maxResults: Math.ceil((query.maxResults || 15) / searchQueries.length),
            useHybridEmbeddings,
            ...intentAnalysis.suggestedFilters
          });
          allResults.push(...results);
        }
      } else {
        // Usar sistema simples
        for (const searchQuery of searchQueries) {
          const results = await this.searchRelevantDocuments({
            ...query,
            question: searchQuery,
            threshold: query.threshold || 0.5,
            maxResults: Math.ceil((query.maxResults || 15) / searchQueries.length),
            ...intentAnalysis.suggestedFilters
          });
          allResults.push(...results);
        }
      }

      // 5. Remover duplicatas e re-ranking inteligente
      const uniqueResults = this.removeDuplicates(allResults);
      const rerankedResults = await rerankingService.rerankResults({
        query: query.question,
        results: uniqueResults,
        topK: query.maxResults || 5,
        useSemanticRerank: true
      });

      // 6. Diversificar resultados
      const diversifiedResults = rerankingService.diversifyResults(
        rerankedResults.rerankedResults,
        2
      );

      // 7. Construir contexto otimizado
      const contextualPrompt = this.buildEnhancedContextualPrompt(
        query.question,
        diversifiedResults,
        expandedQuery
      );

      // 8. Gerar resposta com Gemini
      const answer = await this.generateContextualAnswer(contextualPrompt);

      // 9. Calcular métricas avançadas
      const confidence = this.calculateEnhancedConfidence(
        diversifiedResults,
        answer,
        intentAnalysis.confidence
      );
      const averageSimilarity = diversifiedResults.length > 0
        ? diversifiedResults.reduce((sum, r) => sum + r.similarity, 0) / diversifiedResults.length
        : 0;

      const processingTime = Date.now() - startTime;

      const response: RAGResponse = {
        answer,
        sources: diversifiedResults,
        confidence,
        processingTime,
        usedContext: diversifiedResults.length > 0,
        metadata: {
          queryType: intentAnalysis.intent,
          documentsFound: diversifiedResults.length,
          averageSimilarity,
          geminiModel: 'gemini-1.5-flash',
          queryExpansion: expandedQuery.expanded.length > 1,
          usedReranking: true,
          cacheEnabled: true,
          usedChromaDB: useChromaDB,
          usedHybridEmbeddings: useHybridEmbeddings
        }
      };

      // 10. Armazenar no cache se a resposta for boa
      if (confidence > 0.6) {
        await semanticCache.set(query.question, response);
      }

      // 11. Registrar para aprendizado (se tiver sessão)
      if (query.sessionId) {
        // Será registrado quando houver feedback do usuário
      }

      console.log(`✅ Consulta RAG avançada processada em ${processingTime}ms`);
      return response;

    } catch (error) {
      console.error('❌ Erro no processamento RAG:', error);

      // Fallback: resposta sem contexto
      const fallbackAnswer = await this.generateFallbackAnswer(query.question);

      return {
        answer: fallbackAnswer,
        sources: [],
        confidence: 0.3,
        processingTime: Date.now() - startTime,
        usedContext: false,
        metadata: {
          queryType: 'fallback',
          documentsFound: 0,
          averageSimilarity: 0,
          geminiModel: 'gemini-1.5-flash'
        }
      };
    }
  }

  /**
   * Buscar documentos com ChromaDB e embeddings híbridos
   */
  private async searchWithChromaDB(
    question: string,
    options: RAGQuery & { useHybridEmbeddings?: boolean }
  ): Promise<ChromaSearchResult[]> {
    try {
      const {
        category,
        documentType,
        maxResults = 15,
        threshold = 0.5,
        useHybridEmbeddings = true
      } = options;

      // Construir filtros
      const filters: Record<string, any> = {};
      if (category) filters.category = category;
      if (documentType) filters.documentType = documentType;

      // Buscar com ChromaDB
      const results = await chromaVectorDB.search(question, {
        limit: maxResults,
        threshold,
        filter: filters,
        includeMetadata: true
      });

      console.log(`📊 ChromaDB encontrou ${results.length} documentos relevantes`);
      return results;

    } catch (error) {
      console.error('❌ Erro na busca ChromaDB:', error);
      // Fallback para sistema simples
      return this.searchRelevantDocuments({ question, ...options });
    }
  }

  /**
   * Buscar documentos relevantes (sistema simples)
   */
  private async searchRelevantDocuments(query: RAGQuery): Promise<SearchResult[]> {
    const {
      question,
      category,
      documentType,
      maxResults = 15,
      threshold = 0.5
    } = query;

    // Construir filtros
    const filters: Record<string, any> = {};
    if (category) filters.category = category;
    if (documentType) filters.documentType = documentType;

    // Buscar documentos
    const results = await simpleVectorDatabase.search(question, {
      limit: maxResults,
      threshold,
      filter: filters,
      includeMetadata: true
    });

    console.log(`📊 Encontrados ${results.length} documentos relevantes`);
    return results;
  }

  /**
   * Remover documentos duplicados
   */
  private removeDuplicates(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    const unique: SearchResult[] = [];

    for (const result of results) {
      const key = `${result.id}_${result.content.substring(0, 100)}`;
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(result);
      }
    }

    return unique.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Construir prompt contextual aprimorado
   */
  private buildEnhancedContextualPrompt(
    question: string,
    searchResults: SearchResult[],
    expandedQuery: any
  ): ContextualPrompt {
    const context = searchResults
      .map((result, index) => {
        const relevanceScore = (result.similarity * 100).toFixed(1);
        return `[FONTE ${index + 1}] (Relevância: ${relevanceScore}%)
Título: ${result.metadata.title}
Tipo: ${result.metadata.documentType}
Categoria: ${result.metadata.category}
Conteúdo: ${result.content}
---`;
      })
      .join('\n\n');

    const systemPrompt = `Você é Rafaela, assistente virtual do gabinete da vereadora Rafaela Cuida.

INSTRUÇÕES IMPORTANTES:
- Use APENAS as informações fornecidas no contexto abaixo
- Se não houver informação suficiente, seja honesta sobre isso
- Mantenha o tom profissional mas acessível
- Cite as fontes quando relevante
- Responda de forma concisa e útil
- Use o emoji 🧡 como assinatura

CONTEXTO DISPONÍVEL:
${context}

PERGUNTA ORIGINAL: ${question}
${expandedQuery.reformulated !== question ? `PERGUNTA REFORMULADA: ${expandedQuery.reformulated}` : ''}`;

    const userPrompt = `Com base no contexto fornecido, responda à pergunta do usuário de forma clara e útil.`;

    return {
      systemPrompt,
      userPrompt,
      context,
      sources: searchResults.map(r => r.metadata.title)
    };
  }

  /**
   * Calcular confiança aprimorada
   */
  private calculateEnhancedConfidence(
    searchResults: SearchResult[],
    answer: string,
    intentConfidence: number
  ): number {
    if (searchResults.length === 0) return 0.2;

    // Fatores de confiança
    const avgSimilarity = searchResults.reduce((sum, r) => sum + r.similarity, 0) / searchResults.length;
    const sourceCount = Math.min(searchResults.length / 5, 1); // Normalizar para 5 fontes
    const answerLength = Math.min(answer.length / 200, 1); // Normalizar para 200 chars
    const diversityScore = this.calculateSourceDiversity(searchResults);

    // Combinar fatores
    const confidence = (
      avgSimilarity * 0.4 +
      sourceCount * 0.2 +
      answerLength * 0.1 +
      diversityScore * 0.2 +
      intentConfidence * 0.1
    );

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  /**
   * Calcular diversidade das fontes
   */
  private calculateSourceDiversity(searchResults: SearchResult[]): number {
    if (searchResults.length <= 1) return 0.5;

    const types = new Set(searchResults.map(r => r.metadata.documentType));
    const categories = new Set(searchResults.map(r => r.metadata.category));

    const typeScore = Math.min(types.size / 4, 1); // Máximo 4 tipos
    const categoryScore = Math.min(categories.size / 5, 1); // Máximo 5 categorias

    return (typeScore + categoryScore) / 2;
  }

  /**
   * Construir prompt contextual
   */
  private buildContextualPrompt(question: string, sources: SearchResult[]): ContextualPrompt {
    const context = sources.length > 0 
      ? sources.map((source, index) => 
          `[Documento ${index + 1}] ${source.content}`
        ).join('\n\n')
      : '';

    const sourcesList = sources.map(source => 
      `- ${source.metadata.title} (${source.metadata.category})`
    );

    const systemPrompt = `Você é Rafaela, a assistente virtual do gabinete da vereadora. Sua função é responder perguntas dos cidadãos com base no conhecimento oficial do gabinete.

INSTRUÇÕES:
1. Use APENAS as informações fornecidas no contexto para responder
2. Se a informação não estiver no contexto, diga que não tem essa informação específica
3. Seja clara, objetiva e útil
4. Mantenha o tom profissional mas acessível
5. Se apropriado, sugira como o cidadão pode obter mais informações
6. Sempre cite as fontes quando usar informações específicas

CONTEXTO DISPONÍVEL:
${context || 'Nenhum documento específico encontrado para esta consulta.'}`;

    const userPrompt = `Pergunta do cidadão: ${question}

Por favor, responda com base no contexto fornecido. Se não houver informação suficiente no contexto, explique isso e sugira alternativas.`;

    return {
      systemPrompt,
      userPrompt,
      context,
      sources: sourcesList
    };
  }

  /**
   * Gerar resposta contextual com Gemini
   */
  private async generateContextualAnswer(prompt: ContextualPrompt): Promise<string> {
    try {
      // Combinar system prompt e user prompt
      const fullPrompt = prompt.systemPrompt
        ? `${prompt.systemPrompt}\n\nUsuário: ${prompt.userPrompt}`
        : prompt.userPrompt;

      const response = await this.geminiService.generateContent(fullPrompt);

      return response;
    } catch (error) {
      console.error('❌ Erro ao gerar resposta contextual:', error);
      throw error;
    }
  }

  /**
   * Gerar resposta de fallback
   */
  private async generateFallbackAnswer(question: string): Promise<string> {
    const fallbackPrompt = `Você é Rafaela, assistente do gabinete da vereadora. 

O cidadão perguntou: "${question}"

Não foi possível encontrar informações específicas na base de conhecimento do gabinete. 
Responda de forma educada, explicando que você não tem essa informação específica no momento, 
e sugira que o cidadão entre em contato diretamente com o gabinete para obter informações mais detalhadas.

Forneça os contatos do gabinete se apropriado.`;

    try {
      return await this.geminiService.generateRAGResponse(question, fallbackPrompt);
    } catch (error) {
      return `Olá! Sou a Rafaela, assistente virtual do gabinete da vereadora. 

Infelizmente, não encontrei informações específicas sobre sua pergunta em nossa base de conhecimento no momento. 

Para obter informações mais detalhadas, recomendo que entre em contato diretamente com nosso gabinete:
- WhatsApp: [número do gabinete]
- E-mail: [email do gabinete]
- Telefone: [telefone do gabinete]

Nossa equipe terá prazer em ajudá-lo com informações mais específicas! 🧡`;
    }
  }

  /**
   * Calcular confiança da resposta
   */
  private calculateConfidence(sources: SearchResult[], answer: string): number {
    if (sources.length === 0) return 0.3;

    // Fatores para calcular confiança
    const avgSimilarity = sources.reduce((sum, s) => sum + s.similarity, 0) / sources.length;
    const sourceCount = Math.min(sources.length / 5, 1); // Normalizar para máximo 5 fontes
    const answerLength = Math.min(answer.length / 500, 1); // Respostas mais longas podem ser mais detalhadas

    // Peso dos fatores
    const confidence = (
      avgSimilarity * 0.6 +      // 60% baseado na similaridade
      sourceCount * 0.3 +        // 30% baseado no número de fontes
      answerLength * 0.1         // 10% baseado no tamanho da resposta
    );

    return Math.min(Math.max(confidence, 0), 1); // Garantir entre 0 e 1
  }

  /**
   * Classificar tipo de consulta
   */
  private classifyQueryType(question: string): string {
    const lowerQuestion = question.toLowerCase();

    if (lowerQuestion.includes('lei') || lowerQuestion.includes('projeto') || lowerQuestion.includes('legisla')) {
      return 'legislation';
    }
    if (lowerQuestion.includes('serviço') || lowerQuestion.includes('como') || lowerQuestion.includes('solicitar')) {
      return 'service';
    }
    if (lowerQuestion.includes('quando') || lowerQuestion.includes('agenda') || lowerQuestion.includes('reunião')) {
      return 'schedule';
    }
    if (lowerQuestion.includes('saúde') || lowerQuestion.includes('hospital') || lowerQuestion.includes('médico')) {
      return 'health';
    }
    if (lowerQuestion.includes('educação') || lowerQuestion.includes('escola') || lowerQuestion.includes('ensino')) {
      return 'education';
    }
    if (lowerQuestion.includes('rua') || lowerQuestion.includes('asfalto') || lowerQuestion.includes('buraco')) {
      return 'infrastructure';
    }

    return 'general';
  }

  /**
   * Buscar por categoria específica
   */
  async searchByCategory(
    question: string, 
    category: string, 
    limit: number = 3
  ): Promise<RAGResponse> {
    return this.processQuery({
      question,
      category,
      maxResults: limit
    });
  }

  /**
   * Buscar informações sobre legislação
   */
  async searchLegislation(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'legislation',
      category: 'legislation',
      maxResults: 3
    });
  }

  /**
   * Buscar informações sobre serviços
   */
  async searchServices(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'service',
      maxResults: 5
    });
  }

  /**
   * Buscar FAQ
   */
  async searchFAQ(question: string): Promise<RAGResponse> {
    return this.processQuery({
      question,
      documentType: 'faq',
      maxResults: 5,
      threshold: 0.4 // Threshold menor para FAQ
    });
  }

  /**
   * Obter estatísticas do serviço RAG
   */
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    return await simpleVectorDatabase.getStats();
  }
}

// Instância singleton
export const ragService = new RAGService();
