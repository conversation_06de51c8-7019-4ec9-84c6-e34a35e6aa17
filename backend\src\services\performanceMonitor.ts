import { performance } from 'perf_hooks';
import { supabase } from '../config/supabase';

interface PerformanceMetric {
  id?: string;
  metric_name: string;
  metric_value: number;
  metric_unit: string;
  service: string;
  endpoint?: string;
  timestamp: string;
  metadata?: any;
}

interface SystemMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  database: {
    connections: number;
    queryTime: number;
  };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
  };
  whatsapp: {
    messagesProcessed: number;
    connectionStatus: string;
    lastActivity: string;
  };
  gemini: {
    requestsCount: number;
    averageResponseTime: number;
    errorRate: number;
  };
}

/**
 * Serviço de monitoramento de performance
 * Coleta e armazena métricas de performance do sistema
 */
class PerformanceMonitorService {
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private requestTimes: Map<string, number> = new Map();
  private requestCounts: Map<string, number> = new Map();
  private errorCounts: Map<string, number> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Configurações
  private readonly COLLECTION_INTERVAL = 60 * 1000; // 1 minuto
  private readonly RETENTION_HOURS = 24; // Manter métricas por 24 horas
  private readonly MAX_METRICS_PER_TYPE = 1440; // 24h * 60min = 1440 pontos

  /**
   * Iniciar monitoramento
   */
  public start(): void {
    if (this.isRunning) {
      console.log('⚠️ Monitoramento de performance já está rodando');
      return;
    }

    console.log('📊 Iniciando monitoramento de performance...');
    this.isRunning = true;

    // Coletar métricas imediatamente
    this.collectSystemMetrics();

    // Configurar coleta periódica
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
      this.cleanupOldMetrics();
    }, this.COLLECTION_INTERVAL);

    console.log('✅ Monitoramento de performance iniciado');
  }

  /**
   * Parar monitoramento
   */
  public stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Monitoramento não está rodando');
      return;
    }

    console.log('🛑 Parando monitoramento de performance...');

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.isRunning = false;
    console.log('✅ Monitoramento parado');
  }

  /**
   * Coletar métricas do sistema
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const timestamp = new Date().toISOString();

      // Métricas de memória
      const memUsage = process.memoryUsage();
      await this.recordMetric({
        metric_name: 'memory_used',
        metric_value: Math.round(memUsage.heapUsed / 1024 / 1024),
        metric_unit: 'MB',
        service: 'system',
        timestamp
      });

      await this.recordMetric({
        metric_name: 'memory_total',
        metric_value: Math.round(memUsage.heapTotal / 1024 / 1024),
        metric_unit: 'MB',
        service: 'system',
        timestamp
      });

      // Métricas de CPU (aproximação)
      const cpuUsage = process.cpuUsage();
      await this.recordMetric({
        metric_name: 'cpu_user_time',
        metric_value: cpuUsage.user,
        metric_unit: 'microseconds',
        service: 'system',
        timestamp
      });

      // Métricas de uptime
      await this.recordMetric({
        metric_name: 'uptime',
        metric_value: Math.round(process.uptime()),
        metric_unit: 'seconds',
        service: 'system',
        timestamp
      });

      // Métricas de API
      await this.collectApiMetrics(timestamp);

      // Métricas de banco de dados
      await this.collectDatabaseMetrics(timestamp);

      console.log('📊 Métricas coletadas:', new Date().toLocaleTimeString());

    } catch (error) {
      console.error('❌ Erro ao coletar métricas:', error);
    }
  }

  /**
   * Coletar métricas de API
   */
  private async collectApiMetrics(timestamp: string): Promise<void> {
    // Calcular requests por minuto
    const totalRequests = Array.from(this.requestCounts.values()).reduce((a, b) => a + b, 0);
    await this.recordMetric({
      metric_name: 'api_requests_per_minute',
      metric_value: totalRequests,
      metric_unit: 'requests',
      service: 'api',
      timestamp
    });

    // Calcular tempo médio de resposta
    const totalTime = Array.from(this.requestTimes.values()).reduce((a, b) => a + b, 0);
    const avgTime = totalRequests > 0 ? totalTime / totalRequests : 0;
    await this.recordMetric({
      metric_name: 'api_avg_response_time',
      metric_value: Math.round(avgTime),
      metric_unit: 'ms',
      service: 'api',
      timestamp
    });

    // Calcular taxa de erro
    const totalErrors = Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0);
    const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
    await this.recordMetric({
      metric_name: 'api_error_rate',
      metric_value: Math.round(errorRate * 100) / 100,
      metric_unit: 'percentage',
      service: 'api',
      timestamp
    });

    // Reset contadores
    this.requestCounts.clear();
    this.requestTimes.clear();
    this.errorCounts.clear();
  }

  /**
   * Coletar métricas de banco de dados
   */
  private async collectDatabaseMetrics(timestamp: string): Promise<void> {
    try {
      const startTime = performance.now();
      
      // Teste de conectividade simples
      const { data, error } = await supabase
        .from('contacts')
        .select('count(*)', { count: 'exact', head: true });

      const queryTime = performance.now() - startTime;

      await this.recordMetric({
        metric_name: 'db_query_time',
        metric_value: Math.round(queryTime),
        metric_unit: 'ms',
        service: 'database',
        timestamp
      });

      if (!error) {
        await this.recordMetric({
          metric_name: 'db_connection_status',
          metric_value: 1,
          metric_unit: 'boolean',
          service: 'database',
          timestamp
        });
      } else {
        await this.recordMetric({
          metric_name: 'db_connection_status',
          metric_value: 0,
          metric_unit: 'boolean',
          service: 'database',
          timestamp
        });
      }

    } catch (error) {
      await this.recordMetric({
        metric_name: 'db_connection_status',
        metric_value: 0,
        metric_unit: 'boolean',
        service: 'database',
        timestamp: timestamp
      });
    }
  }

  /**
   * Registrar métrica
   */
  private async recordMetric(metric: PerformanceMetric): Promise<void> {
    try {
      // Armazenar localmente
      const key = `${metric.service}_${metric.metric_name}`;
      if (!this.metrics.has(key)) {
        this.metrics.set(key, []);
      }
      
      const metricsList = this.metrics.get(key)!;
      metricsList.push(metric);
      
      // Limitar número de métricas na memória
      if (metricsList.length > this.MAX_METRICS_PER_TYPE) {
        metricsList.shift();
      }

      // Armazenar no banco (opcional, para persistência)
      // Descomentado para evitar overhead em desenvolvimento
      /*
      const { error } = await supabase
        .from('performance_metrics')
        .insert(metric);

      if (error) {
        console.warn('⚠️ Erro ao salvar métrica no banco:', error);
      }
      */

    } catch (error) {
      console.error('❌ Erro ao registrar métrica:', error);
    }
  }

  /**
   * Middleware para medir tempo de resposta de APIs
   */
  public measureApiRequest(endpoint: string) {
    return (req: any, res: any, next: any) => {
      const startTime = performance.now();
      
      // Incrementar contador de requests
      const currentCount = this.requestCounts.get(endpoint) || 0;
      this.requestCounts.set(endpoint, currentCount + 1);

      // Interceptar o final da resposta
      const originalSend = res.send;
      const self = this;
      res.send = function(data: any) {
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        // Registrar tempo de resposta
        const currentTime = self.requestTimes.get(endpoint) || 0;
        self.requestTimes.set(endpoint, currentTime + responseTime);

        // Registrar erro se status >= 400
        if (res.statusCode >= 400) {
          const currentErrors = self.errorCounts.get(endpoint) || 0;
          self.errorCounts.set(endpoint, currentErrors + 1);
        }
        
        return originalSend.call(this, data);
      }.bind(this);

      next();
    };
  }

  /**
   * Registrar métrica customizada
   */
  public async recordCustomMetric(
    name: string,
    value: number,
    unit: string,
    service: string,
    metadata?: any
  ): Promise<void> {
    await this.recordMetric({
      metric_name: name,
      metric_value: value,
      metric_unit: unit,
      service,
      timestamp: new Date().toISOString(),
      metadata
    });
  }

  /**
   * Obter métricas do sistema
   */
  public getSystemMetrics(): SystemMetrics {
    const memUsage = process.memoryUsage();
    
    return {
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
      },
      cpu: {
        usage: 0 // Placeholder - requer biblioteca externa para CPU real
      },
      database: {
        connections: 1, // Placeholder
        queryTime: this.getLatestMetric('database', 'db_query_time') || 0
      },
      api: {
        requestsPerMinute: this.getLatestMetric('api', 'api_requests_per_minute') || 0,
        averageResponseTime: this.getLatestMetric('api', 'api_avg_response_time') || 0,
        errorRate: this.getLatestMetric('api', 'api_error_rate') || 0
      },
      whatsapp: {
        messagesProcessed: 0, // Placeholder
        connectionStatus: 'connected', // Placeholder
        lastActivity: new Date().toISOString()
      },
      gemini: {
        requestsCount: 0, // Placeholder
        averageResponseTime: 0, // Placeholder
        errorRate: 0 // Placeholder
      }
    };
  }

  /**
   * Obter última métrica de um tipo
   */
  private getLatestMetric(service: string, metricName: string): number | null {
    const key = `${service}_${metricName}`;
    const metricsList = this.metrics.get(key);
    
    if (!metricsList || metricsList.length === 0) {
      return null;
    }
    
    return metricsList[metricsList.length - 1].metric_value;
  }

  /**
   * Obter histórico de métricas
   */
  public getMetricsHistory(service?: string, metricName?: string): PerformanceMetric[] {
    if (service && metricName) {
      const key = `${service}_${metricName}`;
      return this.metrics.get(key) || [];
    }
    
    const allMetrics: PerformanceMetric[] = [];
    for (const metricsList of this.metrics.values()) {
      allMetrics.push(...metricsList);
    }
    
    return allMetrics.sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }

  /**
   * Limpar métricas antigas
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - this.RETENTION_HOURS);
    
    for (const [key, metricsList] of this.metrics.entries()) {
      const filteredMetrics = metricsList.filter(
        metric => new Date(metric.timestamp) > cutoffTime
      );
      
      this.metrics.set(key, filteredMetrics);
    }
  }

  /**
   * Obter status do monitoramento
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      collectionInterval: this.COLLECTION_INTERVAL,
      retentionHours: this.RETENTION_HOURS,
      metricsCount: Array.from(this.metrics.values()).reduce((total, list) => total + list.length, 0),
      lastCollection: new Date().toISOString()
    };
  }
}

// Exportar instância singleton
export const performanceMonitor = new PerformanceMonitorService();
