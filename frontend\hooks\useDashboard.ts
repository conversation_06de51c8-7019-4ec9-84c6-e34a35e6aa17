import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/apiService';
import { DashboardMetrics, ChartDataPoint } from '../schemas/validation';
import { toast } from 'react-toastify';

interface DashboardData {
  metrics: DashboardMetrics | null;
  newRegistrations: ChartDataPoint[];
  ageDistribution: ChartDataPoint[];
  recentActivities: any[];
  topContacts: any[];
}

interface UseDashboardReturn {
  data: DashboardData;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

// Cache simples em memória
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutos (reduzido para atualização mais frequente)
const AUTO_REFRESH_INTERVAL = 30 * 1000; // 30 segundos para auto-refresh
let cachedData: DashboardData | null = null;
let cacheTimestamp: number | null = null;

const isCacheValid = (): boolean => {
  if (!cachedData || !cacheTimestamp) return false;
  return Date.now() - cacheTimestamp < CACHE_DURATION;
};

export const useDashboard = (): UseDashboardReturn => {
  const [data, setData] = useState<DashboardData>({
    metrics: null,
    newRegistrations: [],
    ageDistribution: [],
    recentActivities: [],
    topContacts: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = useCallback(async (useCache = true) => {
    // Verificar cache primeiro
    if (useCache && isCacheValid() && cachedData) {
      setData(cachedData);
      setLastUpdated(new Date(cacheTimestamp!));
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Buscar dados em paralelo
      const [metricsData, registrationsData, ageData, fullAnalytics] = await Promise.all([
        apiService.getDashboardMetrics(),
        apiService.getNewRegistrationsData(),
        apiService.getAgeDistributionData(),
        apiService.getFullAnalytics(),
      ]);

      const newData: DashboardData = {
        metrics: metricsData,
        newRegistrations: registrationsData,
        ageDistribution: ageData,
        recentActivities: fullAnalytics.dailyActivity || [],
        topContacts: fullAnalytics.topContacts || [],
      };

      // Atualizar cache
      cachedData = newData;
      cacheTimestamp = Date.now();

      setData(newData);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error 
        ? err.message 
        : 'Erro ao carregar dados do dashboard';
      
      console.error('Erro ao carregar dados do dashboard:', err);
      setError(errorMessage);
      
      // Se há dados em cache, usar mesmo que expirados
      if (cachedData) {
        setData(cachedData);
        setLastUpdated(new Date(cacheTimestamp!));
        toast.warning('Usando dados em cache. Alguns dados podem estar desatualizados.');
      } else {
        toast.error('Erro ao carregar dados do dashboard');
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchData(false); // Forçar busca sem cache
  }, [fetchData]);

  useEffect(() => {
    fetchData();

    // Configurar atualização automática
    const autoRefreshInterval = setInterval(() => {
      console.log('🔄 Auto-refresh do dashboard executado');
      fetchData(false); // Buscar dados frescos automaticamente
    }, AUTO_REFRESH_INTERVAL);

    return () => {
      clearInterval(autoRefreshInterval);
    };
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
    lastUpdated,
  };
};

// Hook para invalidar cache manualmente
export const useInvalidateDashboardCache = () => {
  return useCallback(() => {
    cachedData = null;
    cacheTimestamp = null;
  }, []);
};

// Hook para verificar se os dados estão atualizados
export const useDashboardFreshness = () => {
  const [isStale, setIsStale] = useState(false);

  useEffect(() => {
    const checkFreshness = () => {
      setIsStale(!isCacheValid());
    };

    // Verificar imediatamente
    checkFreshness();

    // Verificar a cada minuto
    const interval = setInterval(checkFreshness, 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  return isStale;
};
