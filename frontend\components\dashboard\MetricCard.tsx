
import React from 'react';

interface MetricCardProps {
  title: string;
  value: string;
  icon?: React.ReactNode;
  change?: string;
  changeType?: 'positive' | 'negative';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, change, changeType }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-500 uppercase">{title}</p>
          <p className="text-3xl font-bold text-neutral-dark mt-1">{value}</p>
        </div>
        {icon && <div className="text-primary p-3 bg-blue-100 rounded-full">{icon}</div>}
      </div>
      {change && (
        <p className={`text-xs mt-2 ${changeType === 'positive' ? 'text-green-500' : changeType === 'negative' ? 'text-red-500' : 'text-gray-500'}`}>
          {change}
        </p>
      )}
    </div>
  );
};

export default MetricCard;
    