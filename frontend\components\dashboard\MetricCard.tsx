
import React from 'react';

interface MetricCardProps {
  title: string;
  value: string;
  icon?: React.ReactNode | string;
  change?: string;
  changeType?: 'positive' | 'negative';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, change, changeType }) => {
  return (
    <div className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-primary/20 group">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <p className="text-xs sm:text-sm font-medium text-gray-500 uppercase tracking-wide mb-1 sm:mb-2">
            {title}
          </p>
          <p className="text-2xl sm:text-3xl font-bold text-neutral-dark group-hover:text-primary transition-colors duration-200 truncate">
            {value}
          </p>
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-3">
            {typeof icon === 'string' ? (
              <div className="text-2xl sm:text-3xl p-2 sm:p-3 bg-gradient-to-br from-primary/10 to-primary/20 rounded-xl group-hover:scale-110 transition-transform duration-200">
                {icon}
              </div>
            ) : (
              <div className="text-primary p-2 sm:p-3 bg-gradient-to-br from-primary/10 to-primary/20 rounded-xl group-hover:scale-110 transition-transform duration-200">
                {icon}
              </div>
            )}
          </div>
        )}
      </div>
      {change && (
        <div className="mt-3 sm:mt-4">
          <p className={`text-xs sm:text-sm font-medium flex items-center ${
            changeType === 'positive'
              ? 'text-green-600'
              : changeType === 'negative'
                ? 'text-red-600'
                : 'text-gray-500'
          }`}>
            {changeType === 'positive' && (
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            )}
            {changeType === 'negative' && (
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
            {change}
          </p>
        </div>
      )}
    </div>
  );
};

export default MetricCard;
    