
import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { ChartDataPoint } from '../../types';

interface PregnantWomenChartProps {
  data: ChartDataPoint[];
}

const PregnantWomenChart: React.FC<PregnantWomenChartProps> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart
        data={data}
        margin={{
          top: 5, right: 30, left: 0, bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0"/>
        <XAxis dataKey="name" stroke="#666" />
        <YAxis allowDecimals={false} stroke="#666" />
        <Tooltip wrapperClassName="bg-white !border-gray-300 !shadow-lg !rounded-md" />
        <Legend />
        <Line type="monotone" dataKey="value" name="Novas Gestantes" stroke="#0077C2" strokeWidth={2} activeDot={{ r: 8 }} />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default PregnantWomenChart;
    