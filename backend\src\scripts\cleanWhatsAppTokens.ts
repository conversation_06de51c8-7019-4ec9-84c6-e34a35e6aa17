import fs from 'fs';
import path from 'path';

/**
 * Script para limpar tokens antigos do WhatsApp
 * Use apenas se estiver tendo problemas de autenticação
 */

const TOKENS_DIR = path.join(__dirname, '../../tokens');

async function cleanTokens() {
  try {
    console.log('🧹 Iniciando limpeza de tokens WhatsApp...');
    
    if (!fs.existsSync(TOKENS_DIR)) {
      console.log('📁 Diretório de tokens não existe, criando...');
      fs.mkdirSync(TOKENS_DIR, { recursive: true });
      return;
    }

    const files = fs.readdirSync(TOKENS_DIR);
    console.log(`📋 Encontrados ${files.length} arquivo(s) no diretório tokens`);

    if (files.length === 0) {
      console.log('✅ Nenhum token para limpar');
      return;
    }

    // Listar arquivos encontrados
    console.log('📄 Arquivos encontrados:');
    files.forEach(file => {
      const filePath = path.join(TOKENS_DIR, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${stats.isDirectory() ? 'pasta' : 'arquivo'}) - ${stats.size} bytes`);
    });

    // Perguntar confirmação (em ambiente de desenvolvimento)
    if (process.env.NODE_ENV !== 'production') {
      console.log('\n⚠️  ATENÇÃO: Esta operação irá remover TODOS os tokens salvos!');
      console.log('📱 Você precisará escanear o QR Code novamente após a limpeza.');
      console.log('🔄 Para continuar, execute: npm run clean-tokens:force');
      return;
    }

    // Remover arquivos
    let removedCount = 0;
    for (const file of files) {
      const filePath = path.join(TOKENS_DIR, file);
      try {
        if (fs.statSync(filePath).isDirectory()) {
          fs.rmSync(filePath, { recursive: true, force: true });
        } else {
          fs.unlinkSync(filePath);
        }
        removedCount++;
        console.log(`🗑️  Removido: ${file}`);
      } catch (error) {
        console.error(`❌ Erro ao remover ${file}:`, error);
      }
    }

    console.log(`✅ Limpeza concluída! ${removedCount} item(s) removido(s)`);
    console.log('🔄 Reinicie o servidor para gerar novos tokens');

  } catch (error) {
    console.error('❌ Erro durante limpeza:', error);
  }
}

async function forceClean() {
  try {
    console.log('🧹 LIMPEZA FORÇADA - Removendo todos os tokens...');
    
    if (!fs.existsSync(TOKENS_DIR)) {
      console.log('✅ Diretório de tokens não existe');
      return;
    }

    // Remover diretório inteiro
    fs.rmSync(TOKENS_DIR, { recursive: true, force: true });
    console.log('🗑️  Diretório tokens removido completamente');

    // Recriar diretório vazio
    fs.mkdirSync(TOKENS_DIR, { recursive: true });
    console.log('📁 Diretório tokens recriado');

    console.log('✅ Limpeza forçada concluída!');
    console.log('📱 Na próxima inicialização, será necessário escanear o QR Code');

  } catch (error) {
    console.error('❌ Erro durante limpeza forçada:', error);
  }
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2);

if (args.includes('--force') || args.includes('-f')) {
  forceClean();
} else {
  cleanTokens();
}

export { cleanTokens, forceClean };
