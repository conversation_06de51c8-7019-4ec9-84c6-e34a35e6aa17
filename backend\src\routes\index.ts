// Arquivo de rotas limpo - apenas Supabase (MongoDB removido)
import express from 'express';
import { WhatsAppClient } from '../services/whatsapp';
import { GeminiAIService } from '../services/gemini';
import healthMonitor from '../services/healthMonitor';

// Importar rotas modulares
import authRoutes from './auth';
import contactRoutes from './contacts';
// import messageRoutes from './messages'; // Desabilitado temporariamente
// import analyticsRoutes from './analytics'; // Desabilitado temporariamente
import whatsappRoutes, { setWhatsAppClient } from './whatsapp';
import whatsappAutoRoutes from './whatsappAuto';
// import scheduleRoutes from './schedules'; // Desabilitado temporariamente
// import templateRoutes from './templates'; // Desabilitado temporariamente
// import webhookRoutes from './webhooks'; // Desabilitado temporariamente
// import healthRoutes from './health'; // Desabilitado temporariamente

// Importar rotas Supabase (substitui MongoDB)
import { setupSupabaseRoutes } from './supabaseRoutes';
import monitoringRoutes from './monitoring';


export function setupRoutes(app: express.Application, whatsappClient: WhatsAppClient, geminiService: GeminiAIService) {
  // Configurar WhatsApp client nas rotas
  setWhatsAppClient(whatsappClient);

  // =====================================================
  // ROTAS SUPABASE (SUBSTITUI MONGODB COMPLETAMENTE)
  // =====================================================
  setupSupabaseRoutes(app, geminiService);

  // =====================================================
  // ROTAS COM AUTENTICAÇÃO
  // =====================================================

  // Rotas de autenticação
  app.use('/api/auth', authRoutes);

  // Rotas de contatos (se houver rotas específicas)
  app.use('/api/contacts', contactRoutes);

  // Rotas de áudio removidas (sistema simplificado)

  // Rotas de mensagens (desabilitado temporariamente)
  // app.use('/api/messages', messageRoutes);

  // Rotas de analytics (desabilitado temporariamente)
  // app.use('/api/analytics', analyticsRoutes);

  // Rotas do WhatsApp
  app.use('/api/whatsapp', whatsappRoutes);

  // Rotas do WhatsApp Auto
  app.use('/api/whatsapp-auto', whatsappAutoRoutes);

  // Rotas de monitoramento
  app.use('/api/monitoring', monitoringRoutes);

  // Rotas de agendamentos proativos (desabilitado temporariamente)
  // app.use('/api/schedules', scheduleRoutes);

  // Rotas de templates de mensagens (desabilitado temporariamente)
  // app.use('/api/templates', templateRoutes);

  // Rotas de webhooks (desabilitado temporariamente)
  // app.use('/api/webhooks', webhookRoutes);

  // Rotas de monitoramento de saúde (desabilitado temporariamente)
  // app.use('/api/health', healthRoutes);

  // =====================================================
  // ROTAS DE UTILIDADE
  // =====================================================

  // Rota de health check básico
  app.get('/api/health-simple', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: 'Supabase',
      mongodb_removed: true,
      version: '2.0.0'
    });
  });

  // Rota para processar mensagem com IA
  app.post('/api/ai/process', async (req, res) => {
    try {
      const { message, contactId } = req.body;
      
      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      // Processar com IA usando Gemini (método simplificado)
      const aiResponse = { response: 'Processamento de IA não implementado', sentiment: 'neutral' };
      
      res.json({
        originalMessage: message,
        aiResponse: aiResponse,
        contactId: contactId,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      console.error('❌ Erro ao processar mensagem com IA:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rota para gerar mensagem de acompanhamento
  app.post('/api/ai/generate-followup', async (req, res) => {
    try {
      const { contactId } = req.body;

      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      console.log('🤖 Gerando acompanhamento para contato:', contactId);

      // Buscar dados do contato no Supabase
      const { contactService } = await import('../services/contactService');
      const contact = await contactService.findById(contactId);

      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      console.log('👤 Contato encontrado:', {
        name: contact.name,
        phone: contact.phone,
        babyGender: contact.baby_gender
      });

      // Converter Contact para IContact e gerar mensagem de acompanhamento
      const iContact = {
        name: contact.name,
        phone: contact.phone,
        babyGender: contact.baby_gender || 'unknown',
        isActive: contact.is_active,
        lastInteraction: new Date(contact.last_interaction),
        registrationStatus: contact.registration_status as 'unregistered' | 'registered' | 'not_interested',
        evaluationMessages: contact.evaluation_messages,
        interestScore: contact.interest_score,
        evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
        createdAt: new Date(contact.created_at || ''),
        updatedAt: new Date(contact.updated_at || ''),
        updateInteraction: async () => {},
        incrementEvaluationMessages: async () => {},
        updateInterestScore: async () => {},
        markAsRegistered: async () => {},
        markAsNotInterested: async () => {},
        deactivate: async () => {},
        reactivate: async () => {}
      };

      console.log('🤖 Gerando mensagem com IA...');
      const followUpMessage = await geminiService.generateFollowUpMessage(iContact);
      console.log('✅ Mensagem gerada:', followUpMessage.substring(0, 100) + '...');

      res.json({
        message: followUpMessage,
        contactId: contactId,
        contactName: contact.name,
        timestamp: new Date().toISOString(),
        status: 'success'
      });
    } catch (error: any) {
      console.error('❌ Erro ao gerar acompanhamento:', error);
      res.status(500).json({
        error: error.message,
        details: 'Erro interno do servidor ao gerar mensagem de acompanhamento'
      });
    }
  });

  // Rota para verificar status do sistema
  app.get('/api/system/status', (req, res) => {
    res.json({
      status: 'operational',
      database: 'Supabase',
      mongodb_removed: true,
      whatsapp: 'connected',
      ai: 'gemini',
      version: '2.0.0',
      migration_completed: true,
      timestamp: new Date().toISOString()
    });
  });

  // Rota para informações do sistema
  app.get('/api/system/info', (req, res) => {
    res.json({
      name: 'Sistema Rafaela Cuida',
      version: '2.0.0',
      database: 'Supabase (PostgreSQL)',
      mongodb_status: 'Removido',
      migration_date: '2025-06-11',
      features: [
        'Gestão de gestantes',
        'WhatsApp integrado',
        'IA Gemini',
        'Dashboard em tempo real',
        'Analytics avançados'
      ],
      endpoints: [
        'GET /api/contacts - Listar gestantes',
        'POST /api/contacts - Criar gestante',
        'PUT /api/contacts/:id - Atualizar gestante',
        'DELETE /api/contacts/:id - Deletar gestante',
        'GET /api/analytics/dashboard - Métricas',
        'GET /api/analytics/new-registrations - Registros por mês',
        'POST /api/ai/process - Processar com IA',
        'POST /api/ai/generate-followup - Gerar acompanhamento',
        'POST /api/chat/audio - Processar mensagem com áudio (NOVO)'
      ]
    });
  });

  console.log('✅ Rotas configuradas com sucesso');
  console.log('🗑️ MongoDB removido completamente');
  console.log('🚀 Supabase ativo como banco principal');
  console.log('📊 Sistema funcionando em modo Supabase');
}
