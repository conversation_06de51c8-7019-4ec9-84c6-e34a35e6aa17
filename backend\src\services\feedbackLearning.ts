/**
 * Sistema de Feedback Learning e Fine-tuning
 * Aprende com interações dos usuários para melhorar o sistema RAG
 */

import { supabase } from '../config/supabase';
import { hybridEmbeddingService } from './hybridEmbeddings';
import { rerankingService } from './rerankingService';

export interface UserFeedback {
  id?: string;
  sessionId: string;
  query: string;
  response: string;
  sources: string[];
  rating: number; // 1-5
  isHelpful: boolean;
  userComment?: string;
  timestamp: string;
  responseTime: number;
  confidence: number;
  metadata: {
    queryType: string;
    documentsFound: number;
    usedCache: boolean;
    [key: string]: any;
  };
}

export interface LearningPattern {
  pattern: string;
  frequency: number;
  avgRating: number;
  successRate: number;
  commonQueries: string[];
  improvementSuggestions: string[];
}

export interface ModelPerformance {
  totalQueries: number;
  avgRating: number;
  avgResponseTime: number;
  avgConfidence: number;
  successRate: number;
  topFailurePatterns: string[];
  improvementAreas: string[];
}

export class FeedbackLearningService {
  private learningData: Map<string, UserFeedback[]> = new Map();
  private patterns: Map<string, LearningPattern> = new Map();

  /**
   * Registrar feedback do usuário
   */
  async recordFeedback(feedback: UserFeedback): Promise<void> {
    try {
      console.log(`📝 Registrando feedback: Rating ${feedback.rating}/5`);

      // Salvar no banco de dados
      const { error } = await supabase
        .from('user_feedback')
        .insert([{
          session_id: feedback.sessionId,
          query: feedback.query,
          response: feedback.response,
          sources: feedback.sources,
          rating: feedback.rating,
          is_helpful: feedback.isHelpful,
          user_comment: feedback.userComment,
          response_time: feedback.responseTime,
          confidence: feedback.confidence,
          metadata: feedback.metadata,
          created_at: feedback.timestamp
        }]);

      if (error) {
        console.error('❌ Erro ao salvar feedback:', error);
        return;
      }

      // Adicionar ao cache local para análise
      const queryKey = this.normalizeQuery(feedback.query);
      if (!this.learningData.has(queryKey)) {
        this.learningData.set(queryKey, []);
      }
      this.learningData.get(queryKey)!.push(feedback);

      // Analisar padrões se temos dados suficientes
      if (this.learningData.get(queryKey)!.length >= 3) {
        await this.analyzePattern(queryKey);
      }

      console.log('✅ Feedback registrado e analisado');

    } catch (error) {
      console.error('❌ Erro ao registrar feedback:', error);
    }
  }

  /**
   * Analisar padrões de feedback
   */
  private async analyzePattern(queryKey: string): Promise<void> {
    const feedbacks = this.learningData.get(queryKey) || [];
    
    if (feedbacks.length === 0) return;

    const avgRating = feedbacks.reduce((sum, f) => sum + f.rating, 0) / feedbacks.length;
    const successRate = feedbacks.filter(f => f.isHelpful).length / feedbacks.length;
    const frequency = feedbacks.length;

    const pattern: LearningPattern = {
      pattern: queryKey,
      frequency,
      avgRating,
      successRate,
      commonQueries: feedbacks.map(f => f.query),
      improvementSuggestions: []
    };

    // Gerar sugestões de melhoria
    if (avgRating < 3) {
      pattern.improvementSuggestions.push('Melhorar relevância das fontes');
    }
    if (successRate < 0.6) {
      pattern.improvementSuggestions.push('Revisar estratégia de busca');
    }

    this.patterns.set(queryKey, pattern);
    console.log(`📊 Padrão analisado: ${queryKey} (Rating: ${avgRating.toFixed(1)}, Success: ${(successRate * 100).toFixed(1)}%)`);
  }

  /**
   * Obter insights de performance
   */
  async getPerformanceInsights(): Promise<ModelPerformance> {
    try {
      // Buscar dados dos últimos 30 dias
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: feedbacks, error } = await supabase
        .from('user_feedback')
        .select('*')
        .gte('created_at', thirtyDaysAgo.toISOString());

      if (error) {
        console.error('❌ Erro ao buscar feedbacks:', error);
        return this.getDefaultPerformance();
      }

      if (!feedbacks || feedbacks.length === 0) {
        return this.getDefaultPerformance();
      }

      // Calcular métricas
      const totalQueries = feedbacks.length;
      const avgRating = feedbacks.reduce((sum, f) => sum + f.rating, 0) / totalQueries;
      const avgResponseTime = feedbacks.reduce((sum, f) => sum + f.response_time, 0) / totalQueries;
      const avgConfidence = feedbacks.reduce((sum, f) => sum + f.confidence, 0) / totalQueries;
      const successRate = feedbacks.filter(f => f.is_helpful).length / totalQueries;

      // Identificar padrões de falha
      const failedQueries = feedbacks.filter(f => f.rating <= 2);
      const failurePatterns = this.identifyFailurePatterns(failedQueries);

      return {
        totalQueries,
        avgRating,
        avgResponseTime,
        avgConfidence,
        successRate,
        topFailurePatterns: failurePatterns,
        improvementAreas: this.generateImprovementAreas(feedbacks)
      };

    } catch (error) {
      console.error('❌ Erro ao obter insights:', error);
      return this.getDefaultPerformance();
    }
  }

  /**
   * Treinar modelo baseado em feedback
   */
  async trainFromFeedback(): Promise<void> {
    console.log('🎓 Iniciando treinamento baseado em feedback...');

    try {
      // Buscar feedbacks positivos e negativos
      const { data: feedbacks, error } = await supabase
        .from('user_feedback')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1000);

      if (error || !feedbacks) {
        console.error('❌ Erro ao buscar dados de treinamento:', error);
        return;
      }

      // Separar feedbacks positivos e negativos
      const positiveFeedbacks = feedbacks.filter(f => f.rating >= 4 && f.is_helpful);
      const negativeFeedbacks = feedbacks.filter(f => f.rating <= 2 || !f.is_helpful);

      console.log(`📊 Dados de treinamento: ${positiveFeedbacks.length} positivos, ${negativeFeedbacks.length} negativos`);

      // Preparar dados para otimização de embeddings
      const trainingData = positiveFeedbacks.map(f => ({
        query: f.query,
        relevantDocs: f.sources,
        irrelevantDocs: negativeFeedbacks
          .filter(nf => this.calculateQuerySimilarity(f.query, nf.query) > 0.7)
          .map(nf => nf.sources)
          .flat()
      }));

      // Otimizar embeddings híbridos
      await hybridEmbeddingService.optimizeWeights(trainingData);

      // Atualizar configurações de re-ranking
      await this.optimizeRerankingParameters(positiveFeedbacks, negativeFeedbacks);

      console.log('✅ Treinamento concluído');

    } catch (error) {
      console.error('❌ Erro no treinamento:', error);
    }
  }

  /**
   * Otimizar parâmetros de re-ranking
   */
  private async optimizeRerankingParameters(
    positiveFeedbacks: any[],
    negativeFeedbacks: any[]
  ): Promise<void> {
    // Analisar quais tipos de documento tiveram melhor performance
    const positiveDocTypes = positiveFeedbacks
      .flatMap(f => f.metadata?.documentsFound || [])
      .reduce((acc, type) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    console.log('📈 Tipos de documento com melhor performance:', positiveDocTypes);

    // Em uma implementação real, ajustaria os pesos do re-ranking
    // baseado nesta análise
  }

  /**
   * Gerar sugestões de melhoria automáticas
   */
  async generateImprovementSuggestions(): Promise<string[]> {
    const performance = await this.getPerformanceInsights();
    const suggestions: string[] = [];

    if (performance.avgRating < 3.5) {
      suggestions.push('Melhorar qualidade das respostas com mais contexto');
    }

    if (performance.avgResponseTime > 3000) {
      suggestions.push('Otimizar performance com cache mais agressivo');
    }

    if (performance.successRate < 0.7) {
      suggestions.push('Revisar estratégia de busca e threshold');
    }

    if (performance.avgConfidence < 0.6) {
      suggestions.push('Adicionar mais documentos à base de conhecimento');
    }

    return suggestions;
  }

  /**
   * Detectar queries problemáticas
   */
  async detectProblematicQueries(): Promise<Array<{
    query: string;
    frequency: number;
    avgRating: number;
    issues: string[];
  }>> {
    const { data: feedbacks } = await supabase
      .from('user_feedback')
      .select('*')
      .lte('rating', 2);

    if (!feedbacks) return [];

    const queryGroups = feedbacks.reduce((acc, f) => {
      const key = this.normalizeQuery(f.query);
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(f);
      return acc;
    }, {} as Record<string, any[]>);

    return Object.entries(queryGroups)
      .filter(([_, group]) => group.length >= 2)
      .map(([query, group]) => ({
        query,
        frequency: group.length,
        avgRating: group.reduce((sum, f) => sum + f.rating, 0) / group.length,
        issues: this.identifyQueryIssues(group)
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Métodos auxiliares
   */
  private normalizeQuery(query: string): string {
    return query.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .trim()
      .substring(0, 50);
  }

  private calculateQuerySimilarity(query1: string, query2: string): number {
    const words1 = new Set(query1.toLowerCase().split(/\s+/));
    const words2 = new Set(query2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  private identifyFailurePatterns(failedQueries: any[]): string[] {
    const patterns = failedQueries.reduce((acc, f) => {
      const type = f.metadata?.queryType || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(patterns)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([pattern, count]) => `${pattern} (${count} falhas)`);
  }

  private generateImprovementAreas(feedbacks: any[]): string[] {
    const areas: string[] = [];
    
    const lowConfidenceQueries = feedbacks.filter(f => f.confidence < 0.5).length;
    if (lowConfidenceQueries > feedbacks.length * 0.3) {
      areas.push('Melhorar confiança das respostas');
    }

    const slowQueries = feedbacks.filter(f => f.response_time > 5000).length;
    if (slowQueries > feedbacks.length * 0.2) {
      areas.push('Otimizar tempo de resposta');
    }

    return areas;
  }

  private identifyQueryIssues(feedbacks: any[]): string[] {
    const issues: string[] = [];
    
    const avgConfidence = feedbacks.reduce((sum, f) => sum + f.confidence, 0) / feedbacks.length;
    if (avgConfidence < 0.4) {
      issues.push('Baixa confiança');
    }

    const avgResponseTime = feedbacks.reduce((sum, f) => sum + f.response_time, 0) / feedbacks.length;
    if (avgResponseTime > 5000) {
      issues.push('Resposta lenta');
    }

    return issues;
  }

  private getDefaultPerformance(): ModelPerformance {
    return {
      totalQueries: 0,
      avgRating: 0,
      avgResponseTime: 0,
      avgConfidence: 0,
      successRate: 0,
      topFailurePatterns: [],
      improvementAreas: []
    };
  }
}

// Instância singleton
export const feedbackLearningService = new FeedbackLearningService();
