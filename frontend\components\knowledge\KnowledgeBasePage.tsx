/**
 * Página de Gestão da Base de Conhecimento
 * Interface para upload, visualização e gerenciamento de documentos
 */

import React, { useState, useEffect } from 'react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Spinner } from '../ui/Spinner';

interface Document {
  id: string;
  title: string;
  content: string;
  documentType: 'legislation' | 'project' | 'service' | 'faq' | 'agenda' | 'admin';
  category: 'health' | 'education' | 'infrastructure' | 'transparency' | 'legislation' | 'other';
  uploadedAt: string;
  source?: string;
  tags?: string[];
  similarity?: number;
}

interface KnowledgeStats {
  totalDocuments: number;
  documentsByType: Record<string, number>;
  documentsByCategory: Record<string, number>;
}

interface SearchResult {
  id: string;
  content: string;
  metadata: Document;
  similarity: number;
  distance: number;
}

interface RAGResponse {
  answer: string;
  sources: SearchResult[];
  confidence: number;
  processingTime: number;
  usedContext: boolean;
  metadata: {
    queryType: string;
    documentsFound: number;
    averageSimilarity: number;
    geminiModel: string;
  };
}

export const KnowledgeBasePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'upload' | 'search' | 'intents' | 'documents'>('overview');
  const [stats, setStats] = useState<KnowledgeStats | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<RAGResponse | null>(null);
  const [uploadFiles, setUploadFiles] = useState<FileList | null>(null);
  const [uploadProgress, setUploadProgress] = useState(false);
  const [intentQuery, setIntentQuery] = useState('');
  const [intentResult, setIntentResult] = useState<any>(null);
  const [intentLoading, setIntentLoading] = useState(false);

  // Carregar estatísticas
  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const response = await fetch('/api/knowledge-base/stats');
      const data = await response.json();
      if (data.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  // Buscar na base de conhecimento
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const response = await fetch('/api/knowledge-base/rag/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: searchQuery,
          maxResults: 10,
          threshold: 0.5
        }),
      });

      const data = await response.json();
      if (data.success) {
        setSearchResults(data.response);
      }
    } catch (error) {
      console.error('Erro na busca:', error);
    } finally {
      setLoading(false);
    }
  };

  // Upload de documentos
  const handleUpload = async () => {
    if (!uploadFiles || uploadFiles.length === 0) return;

    setUploadProgress(true);
    try {
      const formData = new FormData();
      
      for (let i = 0; i < uploadFiles.length; i++) {
        formData.append('documents', uploadFiles[i]);
      }
      
      formData.append('documentType', 'admin');
      formData.append('category', 'other');
      formData.append('source', 'dashboard_upload');

      const response = await fetch('/api/knowledge-base/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      if (data.success) {
        alert(`${data.results.successful} documentos enviados com sucesso!`);
        setUploadFiles(null);
        loadStats(); // Recarregar estatísticas
      } else {
        alert('Erro no upload: ' + data.error);
      }
    } catch (error) {
      console.error('Erro no upload:', error);
      alert('Erro no upload de documentos');
    } finally {
      setUploadProgress(false);
    }
  };

  // Classificar intenção
  const handleIntentClassification = async () => {
    if (!intentQuery.trim()) return;

    setIntentLoading(true);
    try {
      const response = await fetch('/api/knowledge-base/classify-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: intentQuery,
          threshold: 0.6,
          includeReasoning: true,
          suggestActions: true
        }),
      });

      const data = await response.json();
      if (data.success) {
        setIntentResult(data.classification);
      }
    } catch (error) {
      console.error('Erro na classificação:', error);
    } finally {
      setIntentLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      legislation: 'Legislação',
      project: 'Projeto',
      service: 'Serviço',
      faq: 'FAQ',
      agenda: 'Agenda',
      admin: 'Administrativo'
    };
    return labels[type] || type;
  };

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      health: 'Saúde',
      education: 'Educação',
      infrastructure: 'Infraestrutura',
      transparency: 'Transparência',
      legislation: 'Legislação',
      other: 'Outros'
    };
    return labels[category] || category;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          📚 Base de Conhecimento do Gabinete
        </h1>
        <p className="text-gray-600">
          Gerencie documentos, visualize métricas e teste o sistema RAG
        </p>
      </div>

      {/* Navegação por abas */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: '📊 Visão Geral', icon: '📊' },
            { id: 'upload', label: '📤 Upload', icon: '📤' },
            { id: 'search', label: '🔍 Buscar', icon: '🔍' },
            { id: 'intents', label: '🎯 Intenções', icon: '🎯' },
            { id: 'documents', label: '📄 Documentos', icon: '📄' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Conteúdo das abas */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">📚</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total de Documentos</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats?.totalDocuments || 0}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">📋</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Tipos de Documento</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats ? Object.keys(stats.documentsByType).length : 0}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">🏷️</span>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Categorias</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {stats ? Object.keys(stats.documentsByCategory).length : 0}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Gráficos de distribuição */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  📊 Documentos por Tipo
                </h3>
                <div className="space-y-3">
                  {Object.entries(stats.documentsByType).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{getTypeLabel(type)}</span>
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                          <div
                            className="bg-blue-500 h-2 rounded-full"
                            style={{
                              width: `${(count / stats.totalDocuments) * 100}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  🏷️ Documentos por Categoria
                </h3>
                <div className="space-y-3">
                  {Object.entries(stats.documentsByCategory).map(([category, count]) => (
                    <div key={category} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{getCategoryLabel(category)}</span>
                      <div className="flex items-center">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{
                              width: `${(count / stats.totalDocuments) * 100}%`
                            }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          )}
        </div>
      )}

      {activeTab === 'upload' && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            📤 Upload de Documentos
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selecionar Arquivos
              </label>
              <input
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt,.html"
                onChange={(e) => setUploadFiles(e.target.files)}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="mt-1 text-xs text-gray-500">
                Formatos suportados: PDF, DOC, DOCX, TXT, HTML (máximo 50MB por arquivo)
              </p>
            </div>

            {uploadFiles && uploadFiles.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Arquivos Selecionados ({uploadFiles.length}):
                </h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {Array.from(uploadFiles).map((file, index) => (
                    <li key={index} className="flex items-center">
                      <span className="mr-2">📄</span>
                      {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <Button
              onClick={handleUpload}
              disabled={!uploadFiles || uploadFiles.length === 0 || uploadProgress}
              className="w-full"
            >
              {uploadProgress ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Processando...
                </>
              ) : (
                '📤 Enviar Documentos'
              )}
            </Button>
          </div>
        </Card>
      )}

      {activeTab === 'search' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              🔍 Testar Sistema RAG
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pergunta
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Ex: Como solicitar poda de árvore?"
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button onClick={handleSearch} disabled={loading || !searchQuery.trim()}>
                    {loading ? <Spinner size="sm" /> : '🔍 Buscar'}
                  </Button>
                </div>
              </div>

              {searchResults && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">
                      🤖 Resposta da IA:
                    </h4>
                    <p className="text-gray-700 bg-white p-3 rounded border">
                      {searchResults.answer}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Confiança</p>
                      <p className="text-lg font-semibold text-blue-600">
                        {(searchResults.confidence * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Fontes</p>
                      <p className="text-lg font-semibold text-green-600">
                        {searchResults.sources.length}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Tempo</p>
                      <p className="text-lg font-semibold text-purple-600">
                        {searchResults.processingTime}ms
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Contexto</p>
                      <p className="text-lg font-semibold text-orange-600">
                        {searchResults.usedContext ? '✅' : '❌'}
                      </p>
                    </div>
                  </div>

                  {searchResults.sources.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        📚 Fontes Utilizadas:
                      </h4>
                      <div className="space-y-2">
                        {searchResults.sources.map((source, index) => (
                          <div key={index} className="bg-white p-3 rounded border">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="text-sm font-medium text-gray-900">
                                {source.metadata.title}
                              </h5>
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                {(source.similarity * 100).toFixed(1)}% similar
                              </span>
                            </div>
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {source.content.substring(0, 200)}...
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'intents' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              🎯 Classificação de Intenções
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Consulta do Cidadão
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={intentQuery}
                    onChange={(e) => setIntentQuery(e.target.value)}
                    placeholder="Ex: Preciso marcar uma consulta médica"
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={(e) => e.key === 'Enter' && handleIntentClassification()}
                  />
                  <Button onClick={handleIntentClassification} disabled={intentLoading || !intentQuery.trim()}>
                    {intentLoading ? <Spinner size="sm" /> : '🎯 Classificar'}
                  </Button>
                </div>
              </div>

              {intentResult && (
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Intenção Identificada</p>
                      <p className="text-lg font-semibold text-blue-600">
                        {intentResult.intent.name}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Categoria</p>
                      <p className="text-lg font-semibold text-green-600">
                        {getCategoryLabel(intentResult.intent.category)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-xs text-gray-500">Confiança</p>
                      <p className="text-lg font-semibold text-purple-600">
                        {(intentResult.confidence * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>

                  {intentResult.reasoning && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        🧠 Raciocínio da IA:
                      </h4>
                      <p className="text-gray-700 bg-white p-3 rounded border text-sm">
                        {intentResult.reasoning}
                      </p>
                    </div>
                  )}

                  {intentResult.suggestedActions && intentResult.suggestedActions.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        💡 Ações Sugeridas:
                      </h4>
                      <ul className="bg-white p-3 rounded border space-y-1">
                        {intentResult.suggestedActions.map((action: string, index: number) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start">
                            <span className="mr-2 text-blue-500">•</span>
                            {action}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between bg-white p-3 rounded border">
                    <span className="text-sm text-gray-600">Requer Atendimento Humano:</span>
                    <span className={`text-sm font-medium ${
                      intentResult.requiresHuman ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {intentResult.requiresHuman ? '⚠️ Sim' : '✅ Não'}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              📋 Exemplos de Consultas por Categoria
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                {
                  category: 'health',
                  label: '🏥 Saúde',
                  examples: ['Como marcar consulta?', 'Onde fica o posto de saúde?', 'Como solicitar exames?']
                },
                {
                  category: 'education',
                  label: '🎓 Educação',
                  examples: ['Como fazer matrícula?', 'Quando abrem creches?', 'Calendário escolar?']
                },
                {
                  category: 'infrastructure',
                  label: '🛣️ Infraestrutura',
                  examples: ['Buraco na rua', 'Poda de árvore', 'Iluminação queimada']
                },
                {
                  category: 'legislation',
                  label: '📜 Legislação',
                  examples: ['Projetos da vereadora', 'Como funciona a lei?', 'Quando será votado?']
                },
                {
                  category: 'transparency',
                  label: '🔍 Transparência',
                  examples: ['Orçamento da saúde', 'Gastos públicos', 'Audiência pública']
                },
                {
                  category: 'other',
                  label: '📞 Geral',
                  examples: ['Telefone do gabinete', 'Horário de atendimento', 'Endereço']
                }
              ].map((cat) => (
                <div key={cat.category} className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">{cat.label}</h4>
                  <ul className="space-y-1">
                    {cat.examples.map((example, index) => (
                      <li key={index} className="text-xs text-gray-600 flex items-start">
                        <span className="mr-1">•</span>
                        <button
                          onClick={() => {
                            setIntentQuery(example);
                            handleIntentClassification();
                          }}
                          className="text-left hover:text-blue-600 hover:underline"
                        >
                          {example}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'documents' && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            📄 Documentos na Base de Conhecimento
          </h3>
          
          <div className="text-center py-8 text-gray-500">
            <p>🚧 Lista de documentos em desenvolvimento</p>
            <p className="text-sm">Use a aba "Buscar" para testar o sistema RAG</p>
          </div>
        </Card>
      )}
    </div>
  );
};
