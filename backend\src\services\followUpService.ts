import { supabase } from '../config/supabase';
import { wppConnectIntegration } from './wppConnectIntegration';
import { GeminiAIService } from './gemini';

interface FollowUpContact {
  id: string;
  name: string;
  phone: string;
  baby_gender?: string;
  registration_status: string;
  last_interaction: string;
  created_at: string;
}

class FollowUpService {
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning = false;
  private isProcessing = false; // Previne execuções simultâneas
  private geminiService: GeminiAIService;
  
  // Para testes: 5 minutos = 5 * 60 * 1000 = 300000ms
  // Para produção: 1 semana = 7 * 24 * 60 * 60 * 1000 = 604800000ms
  private readonly CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutos para testes
  private readonly FOLLOW_UP_THRESHOLD = 5 * 60 * 1000; // 5 minutos para testes (simula 1 semana)

  constructor() {
    this.geminiService = new GeminiAIService();
  }

  /**
   * Iniciar o serviço de follow-up automático
   */
  public start(): void {
    if (this.isRunning) {
      console.log('⚠️ Serviço de follow-up já está rodando');
      return;
    }

    console.log('🚀 Iniciando serviço de follow-up automático...');
    console.log(`⏰ Verificação a cada: ${this.CHECK_INTERVAL / 1000 / 60} minutos`);
    console.log(`📅 Threshold de follow-up: ${this.FOLLOW_UP_THRESHOLD / 1000 / 60} minutos (simulando 1 semana)`);

    this.isRunning = true;
    
    // Executar imediatamente uma vez
    this.checkAndSendFollowUps();
    
    // Configurar execução periódica
    this.intervalId = setInterval(() => {
      this.checkAndSendFollowUps();
    }, this.CHECK_INTERVAL);

    console.log('✅ Serviço de follow-up iniciado com sucesso!');
  }

  /**
   * Parar o serviço de follow-up automático
   */
  public stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Serviço de follow-up não está rodando');
      return;
    }

    console.log('🛑 Parando serviço de follow-up automático...');
    
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.isRunning = false;
    console.log('✅ Serviço de follow-up parado com sucesso!');
  }

  /**
   * Verificar status do serviço
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.CHECK_INTERVAL,
      followUpThreshold: this.FOLLOW_UP_THRESHOLD,
      nextCheck: this.intervalId ? new Date(Date.now() + this.CHECK_INTERVAL).toISOString() : null,
      testMode: this.FOLLOW_UP_THRESHOLD < 24 * 60 * 60 * 1000 // Se threshold < 24h, é modo teste
    };
  }

  /**
   * Executar verificação manual de follow-up (para testes)
   */
  public async executeManualFollowUp(): Promise<void> {
    console.log('🧪 Executando follow-up manual...');
    await this.checkAndSendFollowUps();
  }

  /**
   * Verificar e enviar follow-ups necessários
   */
  private async checkAndSendFollowUps(): Promise<void> {
    // Prevenir execuções simultâneas
    if (this.isProcessing) {
      console.log('⚠️ Follow-up já está sendo processado, pulando execução');
      return;
    }

    this.isProcessing = true;

    try {
      // Verificar se WhatsApp está conectado antes de tentar enviar
      const whatsappStatus = wppConnectIntegration.getConnectionStatus();
      if (!whatsappStatus?.isConnected || !whatsappStatus?.isAuthenticated) {
        console.log('📱 WhatsApp não está conectado, aguardando conexão...');
        console.log(`   Status atual: conectado=${whatsappStatus?.isConnected}, autenticado=${whatsappStatus?.isAuthenticated}`);

        // Aguardar conexão por até 30 segundos
        const connected = await this.waitForWhatsAppConnection(30000);
        if (!connected) {
          console.log('⚠️ WhatsApp não conectou a tempo, pulando follow-up automático');
          return;
        }
      }

      console.log('\n🔍 Verificando gestantes que precisam de follow-up...');

      const contactsNeedingFollowUp = await this.getContactsNeedingFollowUp();
      
      if (contactsNeedingFollowUp.length === 0) {
        console.log('✅ Nenhuma gestante precisa de follow-up no momento');
        return;
      }

      console.log(`📋 ${contactsNeedingFollowUp.length} gestante(s) precisam de follow-up:`);
      
      for (const contact of contactsNeedingFollowUp) {
        await this.sendFollowUpMessage(contact);
        // Aguardar 2 segundos entre mensagens para evitar spam
        await this.delay(2000);
      }

      console.log('✅ Verificação de follow-up concluída\n');

    } catch (error) {
      console.error('❌ Erro durante verificação de follow-up:', error);
    } finally {
      this.isProcessing = false; // Sempre liberar o lock
    }
  }

  /**
   * Buscar contatos que precisam de follow-up
   */
  private async getContactsNeedingFollowUp(): Promise<FollowUpContact[]> {
    const thresholdDate = new Date(Date.now() - this.FOLLOW_UP_THRESHOLD).toISOString();
    
    console.log(`📅 Buscando contatos sem interação desde: ${thresholdDate}`);

    const { data: contacts, error } = await supabase
      .from('contacts')
      .select('id, name, phone, baby_gender, registration_status, last_interaction, created_at')
      .eq('registration_status', 'registered') // Apenas gestantes registradas
      .lt('last_interaction', thresholdDate) // Última interação antes do threshold
      .order('last_interaction', { ascending: true });

    if (error) {
      console.error('❌ Erro ao buscar contatos para follow-up:', error);
      throw error;
    }

    const validContacts = (contacts || []).filter(contact => {
      // Verificar se tem nome e telefone válidos
      return contact.name && contact.phone && contact.phone.trim() !== '';
    });

    console.log(`📊 ${validContacts.length} contato(s) encontrado(s) para follow-up`);
    
    return validContacts;
  }

  /**
   * Enviar mensagem de follow-up personalizada
   */
  private async sendFollowUpMessage(contact: FollowUpContact): Promise<void> {
    try {
      console.log(`💬 Enviando follow-up para: ${contact.name} (${contact.phone})`);

      // Gerar mensagem personalizada com IA
      const followUpMessage = await this.generatePersonalizedFollowUp(contact);
      
      if (!followUpMessage) {
        console.error(`❌ Não foi possível gerar mensagem para ${contact.name}`);
        return;
      }

      // Verificar se WhatsApp está conectado antes de enviar
      const whatsappStatus = wppConnectIntegration.getConnectionStatus();
      if (!whatsappStatus?.isConnected || !whatsappStatus?.isAuthenticated) {
        console.error(`❌ WhatsApp não conectado para enviar mensagem para ${contact.name}`);
        console.log(`   Status: conectado=${whatsappStatus?.isConnected}, autenticado=${whatsappStatus?.isAuthenticated}`);
        return;
      }

      // Converter telefone para formato WhatsApp
      const whatsappPhone = this.phoneToWhatsApp(contact.phone);

      // Enviar via WhatsApp
      const sent = await wppConnectIntegration.sendText(whatsappPhone, followUpMessage);

      if (sent) {
        console.log(`✅ Follow-up enviado com sucesso para ${contact.name}`);
        
        // Salvar mensagem no histórico
        await this.saveFollowUpMessage(contact, followUpMessage);
        
        // Atualizar última interação
        await this.updateLastInteraction(contact.id);
        
      } else {
        console.error(`❌ Falha ao enviar follow-up via WhatsApp para ${contact.name}`);
      }

    } catch (error) {
      console.error(`❌ Erro ao enviar follow-up para ${contact.name}:`, error);
    }
  }

  /**
   * Gerar mensagem de follow-up personalizada com IA
   */
  private async generatePersonalizedFollowUp(contact: FollowUpContact): Promise<string | null> {
    try {
      const prompt = `
Você é Rafaela, uma assistente virtual especializada em cuidados maternos. 
Precisa enviar uma mensagem de follow-up carinhosa e personalizada para uma gestante.

Informações da gestante:
- Nome: ${contact.name}
- Gênero do bebê: ${contact.baby_gender || 'não informado'}
- Última interação: ${contact.last_interaction}
- Cadastrada desde: ${contact.created_at}

Instruções:
1. Use o estilo autêntico da Rafaela: carinhoso, maternal, com fé
2. Use frases características: "Nossa gente", "Seguimos firmes", "minha querida"
3. Use o emoji 🧡 como assinatura
4. Mencione o nome da gestante
5. Se souber o gênero do bebê, mencione sutilmente
6. Demonstre interesse genuíno pelo bem-estar
7. Seja proativa mas não invasiva
8. Mantenha entre 30-80 palavras
9. Inclua uma pergunta para estimular resposta

Gere uma mensagem de follow-up natural e acolhedora:`;

      // Converter para IContact compatível
      const iContact = {
        ...contact,
        babyGender: contact.baby_gender || 'unknown',
        isActive: true,
        lastInteraction: new Date(),
        registrationStatus: 'registered',
        interests: [],
        followUpCount: 0,
        lastFollowUp: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        notes: '',
        preferredTime: null,
        timezone: 'America/Fortaleza'
      } as any;

      const response = await this.geminiService.generateOptimizedResponse(iContact, prompt);

      // Extrair texto da resposta (agora sempre string)
      let responseText = '';
      if (typeof response === 'string') {
        responseText = response;
      } else {
        console.warn('⚠️ Resposta não é string:', typeof response);
        responseText = String(response);
      }

      if (responseText && responseText.trim()) {
        console.log(`✅ Mensagem personalizada gerada para ${contact.name}`);
        return responseText.trim();
      } else {
        console.warn(`⚠️ IA retornou resposta vazia para ${contact.name}`);
        return this.getFallbackMessage(contact);
      }

    } catch (error) {
      console.error(`❌ Erro ao gerar mensagem personalizada para ${contact.name}:`, error);
      return this.getFallbackMessage(contact);
    }
  }

  /**
   * Mensagem de fallback caso a IA falhe
   */
  private getFallbackMessage(contact: FollowUpContact): string {
    const babyRef = contact.baby_gender ? 
      (contact.baby_gender.toLowerCase() === 'menino' ? 'seu pequeno' : 'sua pequena') : 
      'seu bebê';

    return `Oi, ${contact.name}! Nossa gente, estava pensando em você e ${babyRef}. Como vocês estão? Espero que tudo esteja correndo bem nessa jornada linda! Seguimos firmes juntas! 🧡`;
  }

  /**
   * Salvar mensagem de follow-up no histórico
   */
  private async saveFollowUpMessage(contact: FollowUpContact, message: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          contact_id: contact.id,
          content: message,
          type: 'text',
          from_me: true,
          timestamp: new Date().toISOString(),
          message_id: `followup_${Date.now()}`
        });

      if (error) {
        console.error(`❌ Erro ao salvar follow-up no histórico para ${contact.name}:`, error);
      } else {
        console.log(`✅ Follow-up salvo no histórico para ${contact.name}`);
      }
    } catch (error) {
      console.error(`❌ Erro ao salvar follow-up para ${contact.name}:`, error);
    }
  }

  /**
   * Atualizar última interação do contato
   */
  private async updateLastInteraction(contactId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('contacts')
        .update({ last_interaction: new Date().toISOString() })
        .eq('id', contactId);

      if (error) {
        console.error(`❌ Erro ao atualizar última interação:`, error);
      }
    } catch (error) {
      console.error(`❌ Erro ao atualizar última interação:`, error);
    }
  }

  /**
   * Converter telefone brasileiro para formato WhatsApp
   */
  private phoneToWhatsApp(phone: string): string {
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length === 11) {
      return `55${cleanPhone}@c.us`;
    } else if (cleanPhone.length === 10) {
      return `55${cleanPhone}@c.us`;
    }
    return `${cleanPhone}@c.us`;
  }

  /**
   * Aguardar conexão WhatsApp (com timeout)
   */
  private async waitForWhatsAppConnection(timeoutMs: number = 30000): Promise<boolean> {
    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      const status = wppConnectIntegration.getConnectionStatus();

      if (status?.isConnected && status?.isAuthenticated) {
        console.log('✅ WhatsApp conectado e pronto para enviar mensagens');
        return true;
      }

      // Aguardar 2 segundos antes de verificar novamente
      await this.delay(2000);
    }

    console.warn(`⚠️ Timeout aguardando conexão WhatsApp (${timeoutMs}ms)`);
    return false;
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Exportar instância singleton
export const followUpService = new FollowUpService();
