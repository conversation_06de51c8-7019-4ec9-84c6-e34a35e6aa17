/**
 * Teste da API RAG
 */

const axios = require('axios');

async function testRAG() {
  try {
    console.log('🧪 Testando API RAG...');

    // Teste 1: Consulta sobre poda de árvore
    console.log('\n📋 Teste 1: Como solicitar poda de árvore?');
    const response1 = await axios.post('http://localhost:3001/api/knowledge-base/rag/query', {
      question: 'Como solicitar poda de árvore?'
    });

    const result1 = response1.data;
    console.log('✅ Resposta:', result1.response?.answer);
    console.log('📊 Confiança:', (result1.response?.confidence * 100).toFixed(1) + '%');
    console.log('📚 Fontes:', result1.response?.sources?.length || 0);

    // Teste 2: Consulta sobre projetos da vereadora
    console.log('\n📋 Teste 2: Quais são os projetos da vereadora Rafaela?');
    const response2 = await axios.post('http://localhost:3001/api/knowledge-base/rag/query', {
      question: 'Quais são os projetos da vereadora Rafaela?'
    });

    const result2 = response2.data;
    console.log('✅ Resposta:', result2.response?.answer);
    console.log('📊 Confiança:', (result2.response?.confidence * 100).toFixed(1) + '%');
    console.log('📚 Fontes:', result2.response?.sources?.length || 0);

    // Teste 3: Consulta sobre audiências públicas
    console.log('\n📋 Teste 3: Quando será a próxima audiência pública?');
    const response3 = await axios.post('http://localhost:3001/api/knowledge-base/rag/query', {
      question: 'Quando será a próxima audiência pública?'
    });

    const result3 = response3.data;
    console.log('✅ Resposta:', result3.response?.answer);
    console.log('📊 Confiança:', (result3.response?.confidence * 100).toFixed(1) + '%');
    console.log('📚 Fontes:', result3.response?.sources?.length || 0);

    // Teste 4: Estatísticas da base de conhecimento
    console.log('\n📊 Teste 4: Estatísticas da base de conhecimento');
    const statsResponse = await axios.get('http://localhost:3001/api/knowledge-base/stats');
    const stats = statsResponse.data;
    console.log('📈 Estatísticas:', stats.stats);

    console.log('\n🎉 Todos os testes concluídos!');

  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testRAG();
