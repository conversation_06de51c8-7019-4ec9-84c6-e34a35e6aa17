import React, { useState, useEffect } from 'react';
import {
  PhoneIcon,
  PlayIcon,
  StopIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const WhatsAppAutoPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <span className="text-3xl">📱</span>
            <h1 className="text-3xl font-bold text-gray-900">
              WhatsApp Automático
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Monitoramento e controle do sistema de processamento automático de áudios via WhatsApp
          </p>
        </div>

        {/* Informações do Sistema */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">🤖</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                Sistema de Processamento Automático
              </h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• <strong>Recebimento automático</strong> de mensagens de áudio via WhatsApp</p>
                <p>• <strong>Processamento em background</strong> com fila inteligente e priorização</p>
                <p>• <strong>Respostas automáticas</strong> personalizadas pela IA Rafaela</p>
                <p>• <strong>Criação automática</strong> de contatos para números desconhecidos</p>
                <p>• <strong>Monitoramento em tempo real</strong> com estatísticas detalhadas</p>
              </div>
            </div>
          </div>
        </div>

        {/* Fluxo do Sistema */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            🔄 Fluxo de Processamento
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🎵</span>
              </div>
              <h4 className="font-medium text-gray-900">Áudio Recebido</h4>
              <p className="text-xs text-gray-500 mt-1">Via WhatsApp</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">✅</span>
              </div>
              <h4 className="font-medium text-gray-900">Confirmação</h4>
              <p className="text-xs text-gray-500 mt-1">Imediata</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">📋</span>
              </div>
              <h4 className="font-medium text-gray-900">Fila</h4>
              <p className="text-xs text-gray-500 mt-1">Background</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🤖</span>
              </div>
              <h4 className="font-medium text-gray-900">IA Gemini</h4>
              <p className="text-xs text-gray-500 mt-1">Processamento</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">💬</span>
              </div>
              <h4 className="font-medium text-gray-900">Resposta</h4>
              <p className="text-xs text-gray-500 mt-1">Automática</p>
            </div>
          </div>
          
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              ⏱️ Tempo total: ~15-20 segundos | 🎯 Taxa de sucesso: &gt;95%
            </p>
          </div>
        </div>

        {/* Monitor Principal */}
        <SimpleWhatsAppMonitor />

        {/* Feed de Mensagens Automáticas */}
        <AutoMessagesFeed />

        {/* Instruções */}
        <div className="bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            📋 Como Usar
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">🚀 Inicialização</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Clique em "Iniciar" para ativar o serviço</li>
                <li>Escaneie o QR Code com WhatsApp</li>
                <li>Aguarde conexão e autenticação</li>
                <li>Sistema ficará ativo automaticamente</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">🧪 Testes</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Digite um número no campo de teste</li>
                <li>Clique em "Teste" para enviar mensagem</li>
                <li>Use "Testar Áudio" para simular processamento</li>
                <li>Monitore estatísticas em tempo real</li>
              </ol>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <span className="text-yellow-600 text-lg">⚠️</span>
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Importante:</p>
                <ul className="space-y-1 list-disc list-inside">
                  <li>Mantenha o WhatsApp conectado no dispositivo principal</li>
                  <li>O sistema funciona 24/7 após configuração inicial</li>
                  <li>Reconexão automática em caso de desconexão</li>
                  <li>Logs detalhados disponíveis para debugging</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Recursos Avançados */}
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            🔧 Recursos Avançados
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🎯</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Priorização Inteligente</h4>
              <p className="text-sm text-gray-600">
                Contatos conhecidos têm prioridade alta. Sistema aprende padrões de uso.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🔄</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Retry Automático</h4>
              <p className="text-sm text-gray-600">
                Falhas são automaticamente reprocessadas com backoff exponencial.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">📊</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Analytics Detalhado</h4>
              <p className="text-sm text-gray-600">
                Métricas completas de performance, tempo de resposta e taxa de sucesso.
              </p>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="bg-gray-900 text-white rounded-lg p-6 mt-8">
          <h3 className="text-lg font-medium mb-4">
            🔌 API Endpoints
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-mono">
            <div>
              <p className="text-green-400">GET /api/whatsapp-auto/status</p>
              <p className="text-gray-400 mb-2">Status completo do sistema</p>
              
              <p className="text-blue-400">POST /api/whatsapp-auto/start</p>
              <p className="text-gray-400 mb-2">Iniciar serviço</p>
              
              <p className="text-red-400">POST /api/whatsapp-auto/stop</p>
              <p className="text-gray-400 mb-2">Parar serviço</p>
            </div>
            
            <div>
              <p className="text-yellow-400">POST /api/whatsapp-auto/restart</p>
              <p className="text-gray-400 mb-2">Reiniciar serviço</p>
              
              <p className="text-purple-400">GET /api/whatsapp-auto/health</p>
              <p className="text-gray-400 mb-2">Health check</p>
              
              <p className="text-orange-400">POST /api/whatsapp-auto/send-test</p>
              <p className="text-gray-400 mb-2">Enviar mensagem de teste</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente simplificado de monitoramento
const SimpleWhatsAppMonitor: React.FC = () => {
  const [status, setStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Buscar status
  const fetchStatus = async () => {
    try {
      setError(null);
      const response = await fetch('/api/whatsapp-auto/status');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        setStatus(data.data);
      } else {
        setError(data.error || 'Erro desconhecido');
      }
    } catch (error: any) {
      console.error('Erro ao buscar status:', error);
      setError(error.message || 'Erro de conexão');
    }
  };

  useEffect(() => {
    fetchStatus();
    const interval = setInterval(fetchStatus, 10000); // Reduzido para 10s
    return () => clearInterval(interval);
  }, []);

  // Controlar serviço
  const controlService = async (action: 'start' | 'stop' | 'restart') => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/whatsapp-auto/${action}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        alert(`✅ ${data.message}`);
        setTimeout(fetchStatus, 2000);
      } else {
        alert(`❌ ${data.error || `Erro ao ${action}`}`);
      }
    } catch (error: any) {
      console.error(`Erro ao ${action}:`, error);
      alert(`❌ Erro ao ${action} serviço: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!status) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Cards */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Status do Sistema</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Serviço */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Serviço</p>
                <p className={`text-lg font-semibold ${status.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                  {status.isRunning ? 'Ativo' : 'Inativo'}
                </p>
              </div>
              {status.isRunning ? (
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              ) : (
                <XCircleIcon className="w-8 h-8 text-red-600" />
              )}
            </div>
          </div>

          {/* Conexão */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Conexão</p>
                <p className={`text-lg font-semibold ${status.connection?.isConnected ? 'text-green-600' : 'text-red-600'}`}>
                  {status.connection?.isConnected ? 'Conectado' : 'Desconectado'}
                </p>
              </div>
              <PhoneIcon className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          {/* Fila */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Fila de Áudio</p>
                <p className="text-lg font-semibold text-blue-600">
                  {status.stats?.audioQueue?.queue?.pending || 0} pendentes
                </p>
              </div>
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Controles */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Controles</h3>

        <div className="flex space-x-4">
          <button
            onClick={() => controlService('start')}
            disabled={isLoading || status.isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <PlayIcon className="w-4 h-4" />
            <span>Iniciar</span>
          </button>

          <button
            onClick={() => controlService('stop')}
            disabled={isLoading || !status.isRunning}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <StopIcon className="w-4 h-4" />
            <span>Parar</span>
          </button>

          <button
            onClick={() => controlService('restart')}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            <ArrowPathIcon className="w-4 h-4" />
            <span>Reiniciar</span>
          </button>
        </div>
      </div>

      {/* QR Code Info */}
      {!status.connection?.isConnected && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-lg font-medium text-yellow-900 mb-2">
                QR Code Necessário
              </h3>
              <p className="text-yellow-800 mb-4">
                Para conectar o WhatsApp automático, você precisa escanear o QR Code que aparece nos logs do servidor.
              </p>
              <div className="bg-yellow-100 rounded-lg p-4">
                <p className="text-sm text-yellow-800">
                  <strong>Como conectar:</strong>
                </p>
                <ol className="list-decimal list-inside text-sm text-yellow-800 mt-2 space-y-1">
                  <li>Verifique os logs do servidor no terminal</li>
                  <li>Procure pelo QR Code ASCII</li>
                  <li>Abra o WhatsApp no seu celular</li>
                  <li>Vá em "Dispositivos conectados" → "Conectar um dispositivo"</li>
                  <li>Escaneie o QR Code</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Componente para exibir mensagens automáticas em tempo real
const AutoMessagesFeed: React.FC = () => {
  const [messages, setMessages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Buscar mensagens automáticas
  const fetchMessages = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/whatsapp-auto/recent-messages?limit=10');

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMessages(data.data);
        }
      }
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto refresh
  useEffect(() => {
    fetchMessages();

    if (autoRefresh) {
      const interval = setInterval(fetchMessages, 5000); // A cada 5 segundos
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMessageIcon = (type: string, fromMe: boolean) => {
    if (type === 'audio' && !fromMe) return '🎵';
    if (type === 'text' && fromMe) return '🤖';
    if (type === 'text' && !fromMe) return '💬';
    return '📱';
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 mt-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">📱</span>
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Mensagens Automáticas Recentes
            </h3>
            <p className="text-sm text-gray-500">
              Áudios processados e respostas automáticas em tempo real
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-600">Auto-refresh</span>
          </label>

          <button
            onClick={fetchMessages}
            disabled={isLoading}
            className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? '🔄' : '↻'} Atualizar
          </button>
        </div>
      </div>

      {/* Lista de Mensagens */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <span className="text-4xl mb-2 block">📭</span>
            <p>Nenhuma mensagem automática recente</p>
            <p className="text-sm">Envie um áudio pelo WhatsApp para testar</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`p-3 rounded-lg border ${
                message.fromMe
                  ? 'bg-blue-50 border-blue-200 ml-8'
                  : 'bg-gray-50 border-gray-200 mr-8'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <span className="text-lg">
                    {getMessageIcon(message.type, message.fromMe)}
                  </span>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-sm text-gray-900">
                        {message.fromMe ? 'Rafaela (IA)' : message.contact.name}
                      </span>
                      <span className="text-xs text-gray-500">
                        {message.contact.phone}
                      </span>
                      <span className={`px-2 py-0.5 rounded-full text-xs ${
                        message.contact.status === 'registered'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {message.contact.status === 'registered' ? 'Registrado' : 'Não Registrado'}
                      </span>
                    </div>

                    <p className="text-sm text-gray-700 leading-relaxed">
                      {message.type === 'audio' && !message.fromMe
                        ? '🎵 Mensagem de áudio recebida'
                        : message.content
                      }
                    </p>
                  </div>
                </div>

                <span className="text-xs text-gray-400 ml-3">
                  {formatTimestamp(message.timestamp)}
                </span>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Estatísticas */}
      {messages.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              📊 {messages.length} mensagens recentes
            </span>
            <span>
              🔄 Última atualização: {new Date().toLocaleTimeString('pt-BR')}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default WhatsAppAutoPage;
