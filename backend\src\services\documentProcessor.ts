/**
 * Serviço de Processamento de Documentos
 * Extrai texto de PDFs, DOCs, TXTs e processa para indexação
 */

import * as fs from 'fs';
import * as path from 'path';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import { simpleVectorDatabase, DocumentMetadata } from './simpleVectorDB';

export interface ProcessingResult {
  success: boolean;
  documentId?: string;
  extractedText?: string;
  wordCount?: number;
  chunks?: number;
  error?: string;
  processingTime?: number;
}

export interface DocumentInfo {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  path: string;
}

export class DocumentProcessor {
  private uploadDir: string;
  private supportedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain',
    'text/html'
  ];

  constructor() {
    this.uploadDir = path.join(process.cwd(), 'uploads', 'documents');
    this.ensureUploadDir();
  }

  /**
   * Garantir que o diretório de upload existe
   */
  private ensureUploadDir(): void {
    if (!fs.existsSync(this.uploadDir)) {
      fs.mkdirSync(this.uploadDir, { recursive: true });
      console.log(`📁 Diretório de upload criado: ${this.uploadDir}`);
    }
  }

  /**
   * Verificar se o tipo de arquivo é suportado
   */
  isSupportedFileType(mimetype: string): boolean {
    return this.supportedTypes.includes(mimetype);
  }

  /**
   * Processar documento completo
   */
  async processDocument(
    file: DocumentInfo,
    metadata: Omit<DocumentMetadata, 'id' | 'uploadedAt' | 'content' | 'filePath'>
  ): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      console.log(`📄 Processando documento: ${file.originalName}`);

      // Verificar tipo de arquivo
      if (!this.isSupportedFileType(file.mimetype)) {
        throw new Error(`Tipo de arquivo não suportado: ${file.mimetype}`);
      }

      // Extrair texto baseado no tipo
      const extractedText = await this.extractText(file);

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('Nenhum texto foi extraído do documento');
      }

      // Contar palavras
      const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;

      console.log(`📊 Texto extraído: ${extractedText.length} caracteres, ${wordCount} palavras`);

      // Adicionar à base de conhecimento
      const documentId = await simpleVectorDatabase.addDocument(extractedText, {
        ...metadata,
        filePath: file.path
      });

      const processingTime = Date.now() - startTime;

      console.log(`✅ Documento processado com sucesso em ${processingTime}ms`);

      return {
        success: true,
        documentId,
        extractedText: extractedText.substring(0, 500) + '...', // Preview
        wordCount,
        processingTime
      };

    } catch (error) {
      console.error('❌ Erro ao processar documento:', error);
      
      // Limpar arquivo em caso de erro
      try {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      } catch (cleanupError) {
        console.error('❌ Erro ao limpar arquivo:', cleanupError);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Extrair texto baseado no tipo de arquivo
   */
  private async extractText(file: DocumentInfo): Promise<string> {
    const buffer = fs.readFileSync(file.path);

    switch (file.mimetype) {
      case 'application/pdf':
        return this.extractFromPDF(buffer);
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'application/msword':
        return this.extractFromWord(buffer);
      
      case 'text/plain':
        return this.extractFromText(buffer);
      
      case 'text/html':
        return this.extractFromHTML(buffer);
      
      default:
        throw new Error(`Tipo de arquivo não implementado: ${file.mimetype}`);
    }
  }

  /**
   * Extrair texto de PDF
   */
  private async extractFromPDF(buffer: Buffer): Promise<string> {
    try {
      const data = await pdfParse(buffer);
      return this.cleanText(data.text);
    } catch (error) {
      throw new Error(`Erro ao extrair PDF: ${error}`);
    }
  }

  /**
   * Extrair texto de documento Word
   */
  private async extractFromWord(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return this.cleanText(result.value);
    } catch (error) {
      throw new Error(`Erro ao extrair Word: ${error}`);
    }
  }

  /**
   * Extrair texto de arquivo de texto
   */
  private extractFromText(buffer: Buffer): string {
    try {
      const text = buffer.toString('utf-8');
      return this.cleanText(text);
    } catch (error) {
      throw new Error(`Erro ao extrair texto: ${error}`);
    }
  }

  /**
   * Extrair texto de HTML
   */
  private extractFromHTML(buffer: Buffer): string {
    try {
      const html = buffer.toString('utf-8');
      // Remover tags HTML básicas
      const text = html
        .replace(/<script[^>]*>.*?<\/script>/gis, '')
        .replace(/<style[^>]*>.*?<\/style>/gis, '')
        .replace(/<[^>]*>/g, ' ')
        .replace(/&[^;]+;/g, ' ');
      
      return this.cleanText(text);
    } catch (error) {
      throw new Error(`Erro ao extrair HTML: ${error}`);
    }
  }

  /**
   * Limpar e normalizar texto extraído
   */
  private cleanText(text: string): string {
    return text
      .replace(/\r\n/g, '\n') // Normalizar quebras de linha
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n') // Reduzir múltiplas quebras
      .replace(/\s{2,}/g, ' ') // Reduzir múltiplos espaços
      .replace(/[^\w\s\-.,!?;:()\[\]"']/g, ' ') // Remover caracteres especiais
      .trim();
  }

  /**
   * Processar múltiplos documentos
   */
  async processMultipleDocuments(
    files: DocumentInfo[],
    baseMetadata: Omit<DocumentMetadata, 'id' | 'uploadedAt' | 'content' | 'filePath' | 'title'>
  ): Promise<ProcessingResult[]> {
    console.log(`📚 Processando ${files.length} documentos...`);

    const results: ProcessingResult[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`📄 Processando ${i + 1}/${files.length}: ${file.originalName}`);

      const metadata = {
        ...baseMetadata,
        title: file.originalName
      };

      const result = await this.processDocument(file, metadata);
      results.push(result);

      // Delay entre processamentos para evitar sobrecarga
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successful = results.filter(r => r.success).length;
    console.log(`✅ Processamento completo: ${successful}/${files.length} documentos processados`);

    return results;
  }

  /**
   * Remover documento do sistema
   */
  async removeDocument(documentId: string): Promise<boolean> {
    try {
      // Remover da base de conhecimento
      const removed = await vectorDatabase.removeDocument(documentId);

      if (removed) {
        console.log(`🗑️ Documento removido: ${documentId}`);
      }

      return removed;
    } catch (error) {
      console.error('❌ Erro ao remover documento:', error);
      return false;
    }
  }

  /**
   * Obter informações sobre tipos de arquivo suportados
   */
  getSupportedTypes(): Array<{
    mimetype: string;
    extension: string;
    description: string;
  }> {
    return [
      {
        mimetype: 'application/pdf',
        extension: '.pdf',
        description: 'Documento PDF'
      },
      {
        mimetype: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        extension: '.docx',
        description: 'Documento Word (novo formato)'
      },
      {
        mimetype: 'application/msword',
        extension: '.doc',
        description: 'Documento Word (formato antigo)'
      },
      {
        mimetype: 'text/plain',
        extension: '.txt',
        description: 'Arquivo de texto'
      },
      {
        mimetype: 'text/html',
        extension: '.html',
        description: 'Documento HTML'
      }
    ];
  }

  /**
   * Validar arquivo antes do processamento
   */
  validateFile(file: DocumentInfo): { valid: boolean; error?: string } {
    // Verificar tamanho (máximo 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `Arquivo muito grande. Máximo permitido: ${maxSize / 1024 / 1024}MB`
      };
    }

    // Verificar tipo
    if (!this.isSupportedFileType(file.mimetype)) {
      return {
        valid: false,
        error: `Tipo de arquivo não suportado: ${file.mimetype}`
      };
    }

    // Verificar se arquivo existe
    if (!fs.existsSync(file.path)) {
      return {
        valid: false,
        error: 'Arquivo não encontrado'
      };
    }

    return { valid: true };
  }
}

// Instância singleton
export const documentProcessor = new DocumentProcessor();
