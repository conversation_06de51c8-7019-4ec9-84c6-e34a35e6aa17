/**
 * <PERSON>ript para criar usuário padrão no sistema
 */

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcrypt');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

async function createDefaultUser() {
  try {
    console.log('🧪 Criando usuário padrão...');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Credenciais do Supabase não configuradas');
      console.log('URL:', supabaseUrl ? 'Configurada' : 'Não configurada');
      console.log('Key:', supabaseServiceKey ? 'Configurada' : 'Não configurada');
      return;
    }

    // Criar cliente Supabase com service role
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Dados do usuário padrão
    const defaultUser = {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Administrador',
      role: 'admin'
    };

    console.log('📧 Email:', defaultUser.email);
    console.log('🔑 Senha:', defaultUser.password);

    // Verificar se usuário já existe
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', defaultUser.email)
      .single();

    if (existingUser) {
      console.log('ℹ️ Usuário já existe:', existingUser.email);
      console.log('🎉 Você pode fazer login com:');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Senha: admin123');
      return;
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(defaultUser.password, 12);

    // Criar usuário na tabela users
    const { data: newUser, error: insertError } = await supabase
      .from('users')
      .insert([
        {
          email: defaultUser.email,
          password: hashedPassword,
          name: defaultUser.name,
          role: defaultUser.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ])
      .select()
      .single();

    if (insertError) {
      console.error('❌ Erro ao criar usuário:', insertError.message);
      console.error('❌ Detalhes:', insertError);
      return;
    }

    console.log('✅ Usuário criado com sucesso!');
    console.log('📊 ID:', newUser.id);
    console.log('📧 Email:', newUser.email);
    console.log('👤 Nome:', newUser.name);
    console.log('🔐 Role:', newUser.role);
    
    console.log('\n🎉 Agora você pode fazer login com:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Senha: admin123');

  } catch (error) {
    console.error('❌ Erro:', error.message);
    console.error('❌ Stack:', error.stack);
  }
}

createDefaultUser()
  .then(() => {
    console.log('✅ Script concluído');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
