/**
 * Serviço de Expansão de Query para Sistema RAG
 * Melhora a busca expandindo e reformulando queries
 */

import { geminiService } from './gemini';

export interface ExpandedQuery {
  original: string;
  expanded: string[];
  synonyms: string[];
  relatedTerms: string[];
  reformulated: string;
  intent: string;
  confidence: number;
}

export interface QueryAnalysis {
  type: 'question' | 'request' | 'search' | 'command';
  domain: 'legislation' | 'services' | 'projects' | 'general';
  entities: string[];
  keywords: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
}

export class QueryExpansionService {
  
  /**
   * Expandir query principal
   */
  async expandQuery(query: string): Promise<ExpandedQuery> {
    try {
      console.log(`🔍 Expandindo query: "${query}"`);
      
      // Análise paralela
      const [
        expandedTerms,
        synonyms,
        relatedTerms,
        reformulated,
        analysis
      ] = await Promise.all([
        this.generateExpandedTerms(query),
        this.generateSynonyms(query),
        this.generateRelatedTerms(query),
        this.reformulateQuery(query),
        this.analyzeQuery(query)
      ]);

      const result: ExpandedQuery = {
        original: query,
        expanded: expandedTerms,
        synonyms,
        relatedTerms,
        reformulated,
        intent: analysis.domain,
        confidence: 0.8
      };

      console.log(`✅ Query expandida: ${expandedTerms.length} variações geradas`);
      return result;

    } catch (error) {
      console.error('❌ Erro na expansão de query:', error);
      
      // Fallback simples
      return {
        original: query,
        expanded: [query],
        synonyms: [],
        relatedTerms: [],
        reformulated: query,
        intent: 'general',
        confidence: 0.3
      };
    }
  }

  /**
   * Gerar termos expandidos
   */
  private async generateExpandedTerms(query: string): Promise<string[]> {
    const prompt = `
Gere 3-5 variações da seguinte pergunta mantendo o mesmo significado:

Pergunta original: "${query}"

Variações (uma por linha):`;

    try {
      const response = await geminiService.generateRAGResponse(prompt);
      const variations = response
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && !line.includes(':'))
        .slice(0, 5);

      return variations.length > 0 ? variations : [query];
    } catch (error) {
      return [query];
    }
  }

  /**
   * Gerar sinônimos
   */
  private async generateSynonyms(query: string): Promise<string[]> {
    const prompt = `
Extraia as palavras-chave principais da pergunta e forneça sinônimos:

Pergunta: "${query}"

Sinônimos das palavras-chave (formato: palavra -> sinônimo1, sinônimo2):`;

    try {
      const response = await geminiService.generateRAGResponse(prompt);
      const synonyms: string[] = [];
      
      const lines = response.split('\n');
      for (const line of lines) {
        if (line.includes('->')) {
          const parts = line.split('->')[1];
          if (parts) {
            const syns = parts.split(',').map(s => s.trim()).filter(s => s.length > 0);
            synonyms.push(...syns);
          }
        }
      }

      return synonyms.slice(0, 10);
    } catch (error) {
      return [];
    }
  }

  /**
   * Gerar termos relacionados
   */
  private async generateRelatedTerms(query: string): Promise<string[]> {
    const prompt = `
Para a seguinte pergunta sobre gabinete de vereadora, liste termos relacionados que podem ajudar na busca:

Pergunta: "${query}"

Termos relacionados (uma palavra/frase por linha):`;

    try {
      const response = await geminiService.generateRAGResponse(prompt);
      const terms = response
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && line.length < 50)
        .slice(0, 8);

      return terms;
    } catch (error) {
      return [];
    }
  }

  /**
   * Reformular query para melhor busca
   */
  private async reformulateQuery(query: string): Promise<string> {
    const prompt = `
Reformule a seguinte pergunta para ser mais específica e adequada para busca em documentos de gabinete:

Pergunta original: "${query}"

Pergunta reformulada:`;

    try {
      const response = await geminiService.generateRAGResponse(prompt);
      const reformulated = response.trim();
      
      return reformulated.length > 0 && reformulated.length < 200 ? reformulated : query;
    } catch (error) {
      return query;
    }
  }

  /**
   * Analisar query para entender intenção
   */
  private async analyzeQuery(query: string): Promise<QueryAnalysis> {
    const prompt = `
Analise a seguinte pergunta e classifique:

Pergunta: "${query}"

Responda no formato:
Tipo: [question/request/search/command]
Domínio: [legislation/services/projects/general]
Entidades: [lista separada por vírgulas]
Palavras-chave: [lista separada por vírgulas]
Sentimento: [positive/neutral/negative]`;

    try {
      const response = await geminiService.generateRAGResponse(prompt);
      const lines = response.split('\n');
      
      const analysis: QueryAnalysis = {
        type: 'question',
        domain: 'general',
        entities: [],
        keywords: [],
        sentiment: 'neutral'
      };

      for (const line of lines) {
        const [key, value] = line.split(':').map(s => s.trim());
        
        switch (key?.toLowerCase()) {
          case 'tipo':
            if (['question', 'request', 'search', 'command'].includes(value)) {
              analysis.type = value as any;
            }
            break;
          case 'domínio':
            if (['legislation', 'services', 'projects', 'general'].includes(value)) {
              analysis.domain = value as any;
            }
            break;
          case 'entidades':
            analysis.entities = value ? value.split(',').map(s => s.trim()) : [];
            break;
          case 'palavras-chave':
            analysis.keywords = value ? value.split(',').map(s => s.trim()) : [];
            break;
          case 'sentimento':
            if (['positive', 'neutral', 'negative'].includes(value)) {
              analysis.sentiment = value as any;
            }
            break;
        }
      }

      return analysis;
    } catch (error) {
      return {
        type: 'question',
        domain: 'general',
        entities: [],
        keywords: this.extractSimpleKeywords(query),
        sentiment: 'neutral'
      };
    }
  }

  /**
   * Extrair palavras-chave simples (fallback)
   */
  private extractSimpleKeywords(query: string): string[] {
    const stopWords = new Set([
      'o', 'a', 'os', 'as', 'um', 'uma', 'uns', 'umas',
      'de', 'do', 'da', 'dos', 'das', 'em', 'no', 'na', 'nos', 'nas',
      'para', 'por', 'com', 'sem', 'sobre', 'entre', 'até', 'desde',
      'que', 'qual', 'quais', 'quando', 'onde', 'como', 'por que', 'porque',
      'é', 'são', 'foi', 'foram', 'ser', 'estar', 'ter', 'haver',
      'e', 'ou', 'mas', 'se', 'então', 'assim', 'também', 'já', 'ainda'
    ]);

    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 5);
  }

  /**
   * Busca híbrida com múltiplas queries
   */
  async generateSearchQueries(originalQuery: string): Promise<string[]> {
    const expanded = await this.expandQuery(originalQuery);
    
    const searchQueries = [
      originalQuery,
      expanded.reformulated,
      ...expanded.expanded.slice(0, 2),
      ...expanded.synonyms.slice(0, 2).map(syn => 
        originalQuery.replace(/\b\w+\b/, syn)
      )
    ];

    // Remover duplicatas e queries muito similares
    const uniqueQueries = Array.from(new Set(searchQueries))
      .filter(q => q.length > 5)
      .slice(0, 5);

    console.log(`🔍 Geradas ${uniqueQueries.length} queries para busca híbrida`);
    return uniqueQueries;
  }

  /**
   * Detectar intenção específica do gabinete
   */
  detectGabineteIntent(query: string): {
    intent: string;
    confidence: number;
    suggestedFilters: Record<string, any>;
  } {
    const queryLower = query.toLowerCase();
    
    // Padrões específicos do gabinete
    const patterns = [
      {
        intent: 'projeto_lei',
        keywords: ['projeto', 'lei', 'proposta', 'legislação'],
        filters: { documentType: 'project' },
        confidence: 0.9
      },
      {
        intent: 'servico_publico',
        keywords: ['serviço', 'atendimento', 'como', 'solicitar'],
        filters: { documentType: 'service' },
        confidence: 0.8
      },
      {
        intent: 'agenda_eventos',
        keywords: ['agenda', 'evento', 'quando', 'data', 'horário'],
        filters: { documentType: 'agenda' },
        confidence: 0.8
      },
      {
        intent: 'faq',
        keywords: ['dúvida', 'pergunta', 'como', 'posso', 'preciso'],
        filters: { documentType: 'faq' },
        confidence: 0.7
      }
    ];

    for (const pattern of patterns) {
      const matches = pattern.keywords.filter(keyword => 
        queryLower.includes(keyword)
      ).length;
      
      if (matches > 0) {
        const confidence = (matches / pattern.keywords.length) * pattern.confidence;
        
        return {
          intent: pattern.intent,
          confidence,
          suggestedFilters: pattern.filters
        };
      }
    }

    return {
      intent: 'general',
      confidence: 0.5,
      suggestedFilters: {}
    };
  }
}

// Instância singleton
export const queryExpansionService = new QueryExpansionService();
