import React, { useState } from 'react';
import { XMarkIcon, ChatBubbleLeftRightIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline';
import { AudioRecorder } from './AudioRecorder';
import { RealTimeTranscription } from './RealTimeTranscription';
import { apiService, type Contact } from '../../src/services/api';
import { toast } from 'react-toastify';

interface AdvancedAudioChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  contact: Contact;
}

type ChatMode = 'audio' | 'transcription' | 'hybrid';
type Step = 'mode' | 'record' | 'message' | 'processing' | 'response';

export const AdvancedAudioChatModal: React.FC<AdvancedAudioChatModalProps> = ({
  isOpen,
  onClose,
  contact
}) => {
  const [step, setStep] = useState<Step>('mode');
  const [chatMode, setChatMode] = useState<ChatMode>('hybrid');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [textMessage, setTextMessage] = useState('');
  const [transcription, setTranscription] = useState('');
  const [aiResponse, setAiResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  if (!isOpen) return null;

  // Seleção do modo de chat
  const handleModeSelection = (mode: ChatMode) => {
    setChatMode(mode);
    setStep('record');
  };

  // Quando o áudio estiver pronto
  const handleAudioReady = (file: File) => {
    setAudioFile(file);
    if (chatMode === 'audio') {
      setStep('message');
    } else {
      // Para hybrid, continuar com transcrição
      setStep('message');
    }
  };

  // Atualização da transcrição em tempo real
  const handleTranscriptionUpdate = (text: string) => {
    setTranscription(text);
  };

  // Transcrição final
  const handleFinalTranscription = (text: string) => {
    setTranscription(text);
    if (chatMode === 'transcription') {
      setTextMessage(text);
    }
  };

  // Voltar para seleção de modo
  const handleBackToMode = () => {
    setAudioFile(null);
    setTextMessage('');
    setTranscription('');
    setStep('mode');
  };

  // Processar mensagem com IA
  const handleProcessMessage = async () => {
    setIsLoading(true);
    setStep('processing');

    try {
      const contactId = contact.id || contact._id;
      if (!contactId) {
        throw new Error('ID do contato não encontrado');
      }

      let finalMessage = textMessage;
      
      // Combinar transcrição com texto adicional para modo híbrido
      if (chatMode === 'hybrid' && transcription) {
        finalMessage = transcription + (textMessage ? `\n\nTexto adicional: ${textMessage}` : '');
      } else if (chatMode === 'transcription') {
        finalMessage = transcription || textMessage;
      }

      console.log('🎵 Enviando para processamento:', {
        contactId,
        mode: chatMode,
        hasAudio: !!audioFile,
        hasTranscription: !!transcription,
        finalMessage
      });

      let result;
      
      if (chatMode === 'audio' || (chatMode === 'hybrid' && audioFile)) {
        // Processar com áudio
        result = await apiService.processAudioMessage(
          contactId,
          audioFile!,
          finalMessage || undefined
        );
      } else {
        // Processar apenas texto/transcrição
        result = await apiService.sendMessage(contactId, finalMessage);
      }

      console.log('✅ Resposta da IA recebida:', result);

      setAiResponse(result.response);
      setStep('response');
      
      toast.success(`Mensagem processada com sucesso! 🎵`);
    } catch (error: any) {
      console.error('❌ Erro ao processar mensagem:', error);
      toast.error('Erro ao processar mensagem: ' + (error.message || 'Erro desconhecido'));
      setStep('message');
    } finally {
      setIsLoading(false);
    }
  };

  // Fechar modal e resetar
  const handleClose = () => {
    setStep('mode');
    setChatMode('hybrid');
    setAudioFile(null);
    setTextMessage('');
    setTranscription('');
    setAiResponse('');
    setIsLoading(false);
    onClose();
  };

  // Iniciar nova conversa
  const handleNewConversation = () => {
    setStep('mode');
    setChatMode('hybrid');
    setAudioFile(null);
    setTextMessage('');
    setTranscription('');
    setAiResponse('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <ChatBubbleLeftRightIcon className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Chat Avançado com {contact.name}
              </h2>
              <p className="text-sm text-gray-500">
                Áudio, transcrição em tempo real e modo híbrido
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Etapa 1: Seleção do Modo */}
          {step === 'mode' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Escolha o modo de comunicação
                </h3>
                <p className="text-gray-600">
                  Selecione como você quer se comunicar com a Rafaela
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Modo Áudio */}
                <button
                  onClick={() => handleModeSelection('audio')}
                  className="p-6 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all group"
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-red-200">
                      <span className="text-2xl">🎤</span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">Apenas Áudio</h4>
                    <p className="text-sm text-gray-600">
                      Grave sua mensagem e envie o áudio diretamente para a IA processar
                    </p>
                  </div>
                </button>

                {/* Modo Transcrição */}
                <button
                  onClick={() => handleModeSelection('transcription')}
                  className="p-6 border-2 border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-all group"
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200">
                      <SpeakerWaveIcon className="w-6 h-6 text-green-600" />
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">Transcrição</h4>
                    <p className="text-sm text-gray-600">
                      Fale e veja sua voz sendo convertida em texto em tempo real
                    </p>
                  </div>
                </button>

                {/* Modo Híbrido */}
                <button
                  onClick={() => handleModeSelection('hybrid')}
                  className="p-6 border-2 border-purple-200 bg-purple-50 rounded-lg hover:border-purple-500 hover:bg-purple-100 transition-all group"
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-purple-200">
                      <span className="text-2xl">🎵</span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">Híbrido ⭐</h4>
                    <p className="text-sm text-gray-600">
                      Grave áudio + transcrição em tempo real para máxima precisão
                    </p>
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Etapa 2: Gravação/Transcrição */}
          {step === 'record' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {chatMode === 'audio' && '🎤 Grave sua mensagem'}
                  {chatMode === 'transcription' && '🎙️ Fale para transcrever'}
                  {chatMode === 'hybrid' && '🎵 Grave e transcreva simultaneamente'}
                </h3>
                <p className="text-gray-600">
                  {chatMode === 'audio' && 'Sua voz será enviada diretamente para a IA'}
                  {chatMode === 'transcription' && 'Sua fala será convertida em texto automaticamente'}
                  {chatMode === 'hybrid' && 'Áudio + transcrição para melhor compreensão'}
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Gravação de Áudio */}
                {(chatMode === 'audio' || chatMode === 'hybrid') && (
                  <div>
                    <AudioRecorder
                      onAudioReady={handleAudioReady}
                      onCancel={handleBackToMode}
                      maxDuration={300}
                    />
                  </div>
                )}

                {/* Transcrição em Tempo Real */}
                {(chatMode === 'transcription' || chatMode === 'hybrid') && (
                  <div>
                    <RealTimeTranscription
                      onTranscriptionUpdate={handleTranscriptionUpdate}
                      onFinalTranscription={handleFinalTranscription}
                      language="pt-BR"
                    />
                  </div>
                )}
              </div>

              {/* Botões de controle */}
              <div className="flex justify-between">
                <button
                  onClick={handleBackToMode}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  ← Voltar aos Modos
                </button>

                {((chatMode === 'transcription' && transcription) || 
                  (chatMode === 'hybrid' && (audioFile || transcription))) && (
                  <button
                    onClick={() => setStep('message')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Continuar →
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Etapa 3: Mensagem complementar */}
          {step === 'message' && (
            <div className="space-y-4">
              {/* Status do que foi capturado */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {audioFile && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 text-red-800">
                      <span>🎤</span>
                      <span className="font-medium">Áudio gravado</span>
                    </div>
                    <p className="text-sm text-red-700 mt-1">
                      {audioFile.name} ({(audioFile.size / 1024).toFixed(1)} KB)
                    </p>
                  </div>
                )}

                {transcription && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2 text-green-800">
                      <SpeakerWaveIcon className="w-5 h-5" />
                      <span className="font-medium">Transcrição capturada</span>
                    </div>
                    <p className="text-sm text-green-700 mt-1 max-h-20 overflow-y-auto">
                      "{transcription.substring(0, 100)}{transcription.length > 100 ? '...' : ''}"
                    </p>
                  </div>
                )}
              </div>

              {/* Texto adicional */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {chatMode === 'transcription' 
                    ? 'Editar transcrição (opcional)'
                    : 'Texto complementar (opcional)'
                  }
                </label>
                <textarea
                  value={chatMode === 'transcription' ? transcription : textMessage}
                  onChange={(e) => {
                    if (chatMode === 'transcription') {
                      setTranscription(e.target.value);
                    } else {
                      setTextMessage(e.target.value);
                    }
                  }}
                  placeholder={
                    chatMode === 'transcription'
                      ? 'Edite a transcrição se necessário...'
                      : 'Adicione informações extras por escrito...'
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={4}
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setStep('record')}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  ← Voltar
                </button>
                <button
                  onClick={handleProcessMessage}
                  disabled={isLoading || (!audioFile && !transcription && !textMessage)}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                >
                  Enviar para Rafaela 🧡
                </button>
              </div>
            </div>
          )}

          {/* Etapa 4: Processando */}
          {step === 'processing' && (
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center space-x-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="text-lg font-medium text-gray-900">
                  Processando mensagem...
                </span>
              </div>
              <p className="text-gray-600">
                A Rafaela está analisando sua mensagem e preparando uma resposta personalizada
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                  🤖 Modo: {chatMode === 'audio' ? 'Áudio' : chatMode === 'transcription' ? 'Transcrição' : 'Híbrido'} | 
                  Gemini 1.5 Flash Multimodal
                </p>
              </div>
            </div>
          )}

          {/* Etapa 5: Resposta */}
          {step === 'response' && aiResponse && (
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  💬 Resposta da Rafaela
                </h3>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-semibold">R</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-orange-900 font-medium mb-1">Rafaela</p>
                    <p className="text-orange-800 leading-relaxed">
                      {aiResponse}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleNewConversation}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  🎤 Nova Mensagem
                </button>
                <button
                  onClick={handleClose}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  ✅ Finalizar
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>🧡 Powered by Rafaela AI</span>
            <span>
              Modo: {chatMode === 'audio' ? 'Áudio' : chatMode === 'transcription' ? 'Transcrição' : 'Híbrido'} | 
              Gemini 1.5 Flash
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
