/**
 * <PERSON><PERSON><PERSON> da API para Gestão da Base de Conhecimento
 * Upload, processamento e busca de documentos
 */

import express from 'express';
import multer from 'multer';
import path from 'path';
import { documentProcessor, DocumentInfo } from '../services/documentProcessor';
import { simpleVectorDatabase, DocumentMetadata } from '../services/simpleVectorDB';
import { ragService } from '../services/ragService';

const router = express.Router();

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'documents');
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `doc-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10 // Máximo 10 arquivos por vez
  },
  fileFilter: (req, file, cb) => {
    if (documentProcessor.isSupportedFileType(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Tipo de arquivo não suportado: ${file.mimetype}`));
    }
  }
});

/**
 * POST /api/knowledge-base/upload
 * Upload e processamento de documentos
 */
router.post('/upload', upload.array('documents', 10), async (req, res) => {
  try {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Nenhum arquivo foi enviado'
      });
    }

    // Extrair metadados do corpo da requisição
    const {
      documentType = 'admin',
      category = 'other',
      source = 'upload',
      tags = []
    } = req.body;

    console.log(`📤 Upload recebido: ${files.length} arquivo(s)`);

    // Converter arquivos para formato esperado
    const documentInfos: DocumentInfo[] = files.map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      path: file.path
    }));

    // Validar arquivos
    const validationErrors: string[] = [];
    for (const docInfo of documentInfos) {
      const validation = documentProcessor.validateFile(docInfo);
      if (!validation.valid) {
        validationErrors.push(`${docInfo.originalName}: ${validation.error}`);
      }
    }

    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Arquivos inválidos',
        details: validationErrors
      });
    }

    // Processar documentos
    const baseMetadata = {
      documentType: documentType as DocumentMetadata['documentType'],
      category: category as DocumentMetadata['category'],
      source,
      tags: Array.isArray(tags) ? tags : tags.split(',').map((t: string) => t.trim())
    };

    const results = await documentProcessor.processMultipleDocuments(
      documentInfos,
      baseMetadata
    );

    // Compilar resposta
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    res.json({
      success: true,
      message: `${successful.length}/${results.length} documentos processados com sucesso`,
      results: {
        successful: successful.length,
        failed: failed.length,
        details: results
      }
    });

  } catch (error) {
    console.error('❌ Erro no upload:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/search
 * Busca semântica na base de conhecimento
 */
router.get('/search', async (req, res) => {
  try {
    const {
      q: query,
      limit = 10,
      threshold = 0.7,
      category,
      documentType,
      includeMetadata = true
    } = req.query;

    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Parâmetro de busca "q" é obrigatório'
      });
    }

    console.log(`🔍 Busca: "${query}"`);

    // Construir filtros
    const filters: Record<string, any> = {};
    if (category) filters.category = category;
    if (documentType) filters.documentType = documentType;

    // Realizar busca
    const results = await simpleVectorDatabase.search(query, {
      limit: parseInt(limit as string),
      threshold: parseFloat(threshold as string),
      filter: filters,
      includeMetadata: includeMetadata === 'true'
    });

    res.json({
      success: true,
      query,
      results: results.length,
      data: results
    });

  } catch (error) {
    console.error('❌ Erro na busca:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/knowledge-base/document/:id
 * Obter documento específico
 */
router.get('/document/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const document = await simpleVectorDatabase.getDocument(id);
    
    if (!document) {
      return res.status(404).json({
        success: false,
        error: 'Documento não encontrado'
      });
    }

    res.json({
      success: true,
      document
    });

  } catch (error) {
    console.error('❌ Erro ao obter documento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter documento'
    });
  }
});

/**
 * DELETE /api/knowledge-base/document/:id
 * Remover documento
 */
router.delete('/document/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const removed = await documentProcessor.removeDocument(id);
    
    if (removed) {
      res.json({
        success: true,
        message: 'Documento removido com sucesso'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Documento não encontrado'
      });
    }

  } catch (error) {
    console.error('❌ Erro ao remover documento:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao remover documento'
    });
  }
});

/**
 * GET /api/knowledge-base/stats
 * Estatísticas da base de conhecimento
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await simpleVectorDatabase.getStats();
    
    res.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('❌ Erro ao obter estatísticas:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas'
    });
  }
});

/**
 * GET /api/knowledge-base/supported-types
 * Tipos de arquivo suportados
 */
router.get('/supported-types', (req, res) => {
  try {
    const supportedTypes = documentProcessor.getSupportedTypes();
    
    res.json({
      success: true,
      supportedTypes
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao obter tipos suportados'
    });
  }
});

/**
 * POST /api/knowledge-base/search-by-category
 * Busca por categoria específica
 */
router.post('/search-by-category', async (req, res) => {
  try {
    const { query, category, limit = 5 } = req.body;

    if (!query || !category) {
      return res.status(400).json({
        success: false,
        error: 'Query e category são obrigatórios'
      });
    }

    const results = await simpleVectorDatabase.searchByCategory(
      query,
      category,
      parseInt(limit)
    );

    res.json({
      success: true,
      query,
      category,
      results: results.length,
      data: results
    });

  } catch (error) {
    console.error('❌ Erro na busca por categoria:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca por categoria'
    });
  }
});

/**
 * POST /api/knowledge-base/initialize
 * Inicializar/reinicializar a base de conhecimento
 */
router.post('/initialize', async (req, res) => {
  try {
    await simpleVectorDatabase.initialize();
    
    res.json({
      success: true,
      message: 'Base de conhecimento inicializada com sucesso'
    });

  } catch (error) {
    console.error('❌ Erro ao inicializar:', error);
    res.status(500).json({
      success: false,
      error: 'Erro ao inicializar base de conhecimento'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/query
 * Consulta RAG completa com contexto
 */
router.post('/rag/query', async (req, res) => {
  try {
    const {
      question,
      context,
      category,
      documentType,
      maxResults = 5,
      threshold = 0.7
    } = req.body;

    if (!question || typeof question !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    console.log(`🤖 Consulta RAG: "${question}"`);

    const response = await ragService.processQuery({
      question,
      context,
      category,
      documentType,
      maxResults: parseInt(maxResults),
      threshold: parseFloat(threshold)
    });

    res.json({
      success: true,
      query: question,
      response
    });

  } catch (error) {
    console.error('❌ Erro na consulta RAG:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na consulta RAG',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/legislation
 * Busca específica sobre legislação
 */
router.post('/rag/legislation', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchLegislation(question);

    res.json({
      success: true,
      query: question,
      type: 'legislation',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca de legislação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca de legislação'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/services
 * Busca específica sobre serviços
 */
router.post('/rag/services', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchServices(question);

    res.json({
      success: true,
      query: question,
      type: 'services',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca de serviços:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca de serviços'
    });
  }
});

/**
 * POST /api/knowledge-base/rag/faq
 * Busca específica no FAQ
 */
router.post('/rag/faq', async (req, res) => {
  try {
    const { question } = req.body;

    if (!question) {
      return res.status(400).json({
        success: false,
        error: 'Campo "question" é obrigatório'
      });
    }

    const response = await ragService.searchFAQ(question);

    res.json({
      success: true,
      query: question,
      type: 'faq',
      response
    });

  } catch (error) {
    console.error('❌ Erro na busca FAQ:', error);
    res.status(500).json({
      success: false,
      error: 'Erro na busca FAQ'
    });
  }
});

export default router;
