import React, { cloneElement } from 'react';
import Button from '../shared/Button';
import { WhatsAppConnectionStatus } from '../../types';
import { ICONS } from '../../constants';
import Spinner from '../shared/Spinner';

interface WhatsAppConnectCardProps {
  status: WhatsAppConnectionStatus;
  qrCodeUrl: string | null;
  onConnect: () => void;
  onDisconnect: () => void;
  onReconnect?: () => void;
  onCleanTokens?: () => void;
  isReconnecting?: boolean;
  isCleaningTokens?: boolean;
}

const WhatsAppConnectCard: React.FC<WhatsAppConnectCardProps> = ({
  status,
  qrCodeUrl,
  onConnect,
  onDisconnect,
  onReconnect,
  onCleanTokens,
  isReconnecting = false,
  isCleaningTokens = false
}) => {
  
  const getStatusIndicator = () => {
    switch (status) {
      case WhatsAppConnectionStatus.CONNECTED:
        return <span className="flex items-center text-sm text-green-600">{cloneElement(ICONS.checkCircle, {className: "w-5 h-5 mr-1"})} Conectado</span>;
      case WhatsAppConnectionStatus.CONNECTING:
        return <span className="flex items-center text-sm text-yellow-600">{cloneElement(ICONS.warning, {className: "w-5 h-5 mr-1"})} Conectando...</span>;
      case WhatsAppConnectionStatus.QR_CODE:
        return <span className="flex items-center text-sm text-blue-600">{cloneElement(ICONS.info, {className: "w-5 h-5 mr-1"})} Escaneie o QR Code</span>;
      case WhatsAppConnectionStatus.ERROR:
        return <span className="flex items-center text-sm text-red-600">{cloneElement(ICONS.xCircle, {className: "w-5 h-5 mr-1"})} Erro</span>;
      default: // DISCONNECTED
        return <span className="flex items-center text-sm text-gray-500">{cloneElement(ICONS.xCircle, {className: "w-5 h-5 mr-1"})} Desconectado</span>;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-neutral-dark">Conexão WhatsApp</h3>
        {getStatusIndicator()}
      </div>

      {/* Loading States */}
      {(status === WhatsAppConnectionStatus.CONNECTING || isReconnecting) && (
        <div className="text-center py-8">
          <Spinner size="lg" text={isReconnecting ? "Reconectando..." : "Iniciando conexão com o WhatsApp..."} />
        </div>
      )}

      {/* QR Code */}
      {status === WhatsAppConnectionStatus.QR_CODE && qrCodeUrl && (
        <div className="text-center py-4">
          <div className="bg-white p-4 rounded-lg border-2 border-dashed border-gray-300 mb-4 inline-block">
            <img src={qrCodeUrl} alt="WhatsApp QR Code" className="mx-auto w-48 h-48 object-contain" />
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm text-blue-800 font-medium mb-2">📱 Como conectar:</p>
            <ol className="text-xs text-blue-700 text-left space-y-1">
              <li>1. Abra o WhatsApp no seu celular</li>
              <li>2. Vá em <strong>Aparelhos Conectados</strong></li>
              <li>3. Toque em <strong>Conectar um aparelho</strong></li>
              <li>4. Escaneie este código QR</li>
            </ol>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        {(status === WhatsAppConnectionStatus.DISCONNECTED || status === WhatsAppConnectionStatus.ERROR) && (
          <>
            <Button
              onClick={onConnect}
              variant="primary"
              className="w-full"
              disabled={isReconnecting}
            >
              {isReconnecting ? 'Conectando...' : 'Conectar ao WhatsApp'}
            </Button>

            {onReconnect && status === WhatsAppConnectionStatus.ERROR && (
              <Button
                onClick={onReconnect}
                variant="secondary"
                className="w-full"
                disabled={isReconnecting || isCleaningTokens}
              >
                🔄 Tentar Reconectar
              </Button>
            )}

            {onCleanTokens && (status === WhatsAppConnectionStatus.ERROR || status === WhatsAppConnectionStatus.DISCONNECTED) && (
              <Button
                onClick={onCleanTokens}
                variant="outline"
                className="w-full"
                disabled={isReconnecting || isCleaningTokens}
              >
                {isCleaningTokens ? '🧹 Limpando...' : '🧹 Limpar Tokens e Reconectar'}
              </Button>
            )}
          </>
        )}

        {status === WhatsAppConnectionStatus.CONNECTED && (
          <div className="space-y-2">
            <div className="bg-green-50 p-3 rounded-lg border border-green-200">
              <p className="text-sm text-green-800 font-medium flex items-center">
                ✅ WhatsApp conectado e funcionando!
              </p>
            </div>
            <Button onClick={onDisconnect} variant="danger" className="w-full">
              Desconectar WhatsApp
            </Button>
          </div>
        )}
      </div>
      
      {status !== WhatsAppConnectionStatus.CONNECTING && status !== WhatsAppConnectionStatus.QR_CODE && !qrCodeUrl &&
       (status === WhatsAppConnectionStatus.DISCONNECTED || status === WhatsAppConnectionStatus.ERROR) && (
        <p className="text-xs text-gray-400 mt-4 text-center">
          Mantenha seu celular conectado à internet para o funcionamento do serviço.
        </p>
      )}
    </div>
  );
};

export default WhatsAppConnectCard;