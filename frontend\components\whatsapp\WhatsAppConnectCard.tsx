import React, { cloneElement } from 'react';
import Button from '../shared/Button';
import { WhatsAppConnectionStatus } from '../../src/types';
import { ICONS } from '../../src/constants';
import Spinner from '../shared/Spinner';

interface WhatsAppConnectCardProps {
  status: WhatsAppConnectionStatus;
  qrCodeUrl: string | null;
  onConnect: () => void;
  onDisconnect: () => void;
}

const WhatsAppConnectCard: React.FC<WhatsAppConnectCardProps> = ({ status, qrCodeUrl, onConnect, onDisconnect }) => {
  
  const getStatusIndicator = () => {
    switch (status) {
      case WhatsAppConnectionStatus.CONNECTED:
        return <span className="flex items-center text-sm text-green-600">{cloneElement(ICONS.checkCircle, {className: "w-5 h-5 mr-1"})} Conectado</span>;
      case WhatsAppConnectionStatus.CONNECTING:
        return <span className="flex items-center text-sm text-yellow-600">{cloneElement(ICONS.warning, {className: "w-5 h-5 mr-1"})} Conectando...</span>;
      case WhatsAppConnectionStatus.QR_CODE:
        return <span className="flex items-center text-sm text-blue-600">{cloneElement(ICONS.info, {className: "w-5 h-5 mr-1"})} Escaneie o QR Code</span>;
      case WhatsAppConnectionStatus.ERROR:
        return <span className="flex items-center text-sm text-red-600">{cloneElement(ICONS.xCircle, {className: "w-5 h-5 mr-1"})} Erro</span>;
      default: // DISCONNECTED
        return <span className="flex items-center text-sm text-gray-500">{cloneElement(ICONS.xCircle, {className: "w-5 h-5 mr-1"})} Desconectado</span>;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-neutral-dark">Conexão WhatsApp</h3>
        {getStatusIndicator()}
      </div>

      {status === WhatsAppConnectionStatus.CONNECTING && (
        <div className="text-center py-8">
          <Spinner />
          <p className="mt-2 text-sm text-gray-500">Iniciando conexão com o WhatsApp...</p>
        </div>
      )}

      {status === WhatsAppConnectionStatus.QR_CODE && qrCodeUrl && (
        <div className="text-center py-4">
          <img src={qrCodeUrl} alt="WhatsApp QR Code" className="mx-auto mb-4 border rounded-md" />
          <p className="text-sm text-gray-500">Abra o WhatsApp no seu celular, vá em Aparelhos Conectados e escaneie este código.</p>
        </div>
      )}
      
      {(status === WhatsAppConnectionStatus.DISCONNECTED || status === WhatsAppConnectionStatus.ERROR) && (
        <Button onClick={onConnect} variant="primary" className="w-full">
          Conectar ao WhatsApp
        </Button>
      )}

      {status === WhatsAppConnectionStatus.CONNECTED && (
        <Button onClick={onDisconnect} variant="danger" className="w-full">
          Desconectar WhatsApp
        </Button>
      )}
      
      {status !== WhatsAppConnectionStatus.CONNECTING && status !== WhatsAppConnectionStatus.QR_CODE && !qrCodeUrl &&
       (status === WhatsAppConnectionStatus.DISCONNECTED || status === WhatsAppConnectionStatus.ERROR) && (
        <p className="text-xs text-gray-400 mt-4 text-center">
          Mantenha seu celular conectado à internet para o funcionamento do serviço.
        </p>
      )}
    </div>
  );
};

export default WhatsAppConnectCard;