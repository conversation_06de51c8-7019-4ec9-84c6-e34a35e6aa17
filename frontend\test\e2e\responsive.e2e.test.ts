import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5173';

// Configurações de dispositivos para teste
const devices = [
  { name: 'Desktop', width: 1920, height: 1080 },
  { name: 'Tablet', width: 768, height: 1024 },
  { name: 'Mobile Large', width: 414, height: 896 },
  { name: 'Mobile Small', width: 375, height: 667 }
];

test.describe('Testes de Responsividade', () => {
  
  test.beforeEach(async ({ page }) => {
    // Fazer login antes de cada teste
    await page.goto(`${BASE_URL}/login`);
    
    // Preencher credenciais de teste
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'admin123');
    await page.click('[data-testid="login-button"]');
    
    // Aguardar redirecionamento
    await page.waitForURL('**/dashboard');
  });

  devices.forEach(device => {
    test(`Dashboard deve ser responsivo em ${device.name}`, async ({ page }) => {
      // Configurar viewport
      await page.setViewportSize({ width: device.width, height: device.height });
      
      await page.goto(`${BASE_URL}/dashboard`);
      
      // Verificar se o título está visível
      await expect(page.locator('h1')).toBeVisible();
      
      // Verificar se os cards de métricas estão visíveis
      const metricCards = page.locator('[data-testid="metric-card"]');
      await expect(metricCards.first()).toBeVisible();
      
      // Em mobile, verificar se o layout se adapta
      if (device.width < 768) {
        // Cards devem estar em coluna única
        const firstCard = metricCards.first();
        const secondCard = metricCards.nth(1);
        
        if (await secondCard.isVisible()) {
          const firstCardBox = await firstCard.boundingBox();
          const secondCardBox = await secondCard.boundingBox();
          
          // Em mobile, o segundo card deve estar abaixo do primeiro
          if (firstCardBox && secondCardBox) {
            expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 50);
          }
        }
      }
      
      console.log(`✅ Dashboard responsivo em ${device.name} (${device.width}x${device.height})`);
    });

    test(`Sidebar deve funcionar em ${device.name}`, async ({ page }) => {
      await page.setViewportSize({ width: device.width, height: device.height });
      
      await page.goto(`${BASE_URL}/dashboard`);
      
      // Verificar se a sidebar está presente
      const sidebar = page.locator('[data-testid="sidebar"]');
      
      if (device.width >= 768) {
        // Desktop/Tablet: sidebar deve estar visível
        await expect(sidebar).toBeVisible();
        
        // Testar toggle da sidebar
        const toggleButton = page.locator('[data-testid="sidebar-toggle"]');
        if (await toggleButton.isVisible()) {
          await toggleButton.click();
          // Sidebar pode colapsar mas ainda estar visível
          await expect(sidebar).toBeVisible();
        }
      } else {
        // Mobile: sidebar pode estar oculta ou colapsada
        // Verificar se existe botão de menu
        const menuButton = page.locator('button').filter({ hasText: /menu|☰/ }).first();
        if (await menuButton.isVisible()) {
          await menuButton.click();
          // Após clicar, sidebar deve aparecer
          await expect(sidebar).toBeVisible();
        }
      }
      
      console.log(`✅ Sidebar funcional em ${device.name}`);
    });

    test(`Lista de gestantes deve ser responsiva em ${device.name}`, async ({ page }) => {
      await page.setViewportSize({ width: device.width, height: device.height });
      
      await page.goto(`${BASE_URL}/gestantes`);
      
      // Verificar se a página carregou
      await expect(page.locator('h1')).toBeVisible();
      
      // Verificar se o botão de nova gestante está visível
      const newButton = page.locator('[data-testid="new-pregnant-button"]');
      await expect(newButton).toBeVisible();
      
      // Verificar se a tabela/lista está presente
      const table = page.locator('[data-testid="pregnant-table"], [data-testid="pregnant-list"]');
      await expect(table).toBeVisible();
      
      // Em mobile, verificar se a tabela é scrollável horizontalmente
      if (device.width < 768) {
        const tableContainer = page.locator('.overflow-x-auto, .table-container').first();
        if (await tableContainer.isVisible()) {
          // Verificar se tem scroll horizontal
          const scrollWidth = await tableContainer.evaluate(el => el.scrollWidth);
          const clientWidth = await tableContainer.evaluate(el => el.clientWidth);
          
          if (scrollWidth > clientWidth) {
            console.log(`📱 Tabela com scroll horizontal em ${device.name}`);
          }
        }
      }
      
      console.log(`✅ Lista de gestantes responsiva em ${device.name}`);
    });

    test(`Modal de criação deve funcionar em ${device.name}`, async ({ page }) => {
      await page.setViewportSize({ width: device.width, height: device.height });
      
      await page.goto(`${BASE_URL}/gestantes`);
      
      // Clicar no botão de nova gestante
      await page.click('[data-testid="new-pregnant-button"]');
      
      // Aguardar modal aparecer
      await page.waitForSelector('[data-testid="pregnant-form-modal"]');
      
      const modal = page.locator('[data-testid="pregnant-form-modal"]');
      await expect(modal).toBeVisible();
      
      // Verificar se os campos estão visíveis e acessíveis
      await expect(page.locator('[aria-label="Nome"]')).toBeVisible();
      await expect(page.locator('[aria-label="Telefone"]')).toBeVisible();
      
      // Em mobile, verificar se o modal ocupa a tela adequadamente
      if (device.width < 768) {
        const modalBox = await modal.boundingBox();
        if (modalBox) {
          // Modal deve ocupar a maior parte da largura em mobile
          expect(modalBox.width).toBeGreaterThan(device.width * 0.8);
        }
      }
      
      // Fechar modal
      const closeButton = page.locator('[data-testid="modal-close"], [aria-label="Fechar"]').first();
      if (await closeButton.isVisible()) {
        await closeButton.click();
      } else {
        await page.keyboard.press('Escape');
      }
      
      console.log(`✅ Modal responsivo em ${device.name}`);
    });

    test(`WhatsApp page deve ser responsiva em ${device.name}`, async ({ page }) => {
      await page.setViewportSize({ width: device.width, height: device.height });
      
      await page.goto(`${BASE_URL}/whatsapp`);
      
      // Verificar se a página carregou
      await expect(page.locator('h1')).toBeVisible();
      
      // Verificar se os componentes principais estão visíveis
      const statusCard = page.locator('[data-testid="whatsapp-status"]').first();
      if (await statusCard.isVisible()) {
        await expect(statusCard).toBeVisible();
      }
      
      // Verificar se há área de mensagens
      const messageArea = page.locator('[data-testid="message-area"], .message-container').first();
      if (await messageArea.isVisible()) {
        await expect(messageArea).toBeVisible();
      }
      
      console.log(`✅ WhatsApp page responsiva em ${device.name}`);
    });
  });

  test('Navegação entre páginas deve funcionar em mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    const pages = [
      { path: '/dashboard', name: 'Dashboard' },
      { path: '/gestantes', name: 'Gestantes' },
      { path: '/whatsapp', name: 'WhatsApp' },
      { path: '/configuracoes', name: 'Configurações' }
    ];
    
    for (const testPage of pages) {
      await page.goto(`${BASE_URL}${testPage.path}`);
      
      // Verificar se a página carregou
      await expect(page.locator('h1')).toBeVisible();
      
      console.log(`✅ ${testPage.name} carregou corretamente em mobile`);
      
      // Pequena pausa entre navegações
      await page.waitForTimeout(500);
    }
  });

  test('Elementos interativos devem ter tamanho adequado para touch', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto(`${BASE_URL}/gestantes`);
    
    // Verificar botões principais
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    
    for (let i = 0; i < Math.min(buttonCount, 5); i++) {
      const button = buttons.nth(i);
      if (await button.isVisible()) {
        const box = await button.boundingBox();
        if (box) {
          // Botões devem ter pelo menos 44px de altura (recomendação Apple/Google)
          expect(box.height).toBeGreaterThanOrEqual(40);
          console.log(`✅ Botão ${i + 1} tem tamanho adequado: ${box.width}x${box.height}`);
        }
      }
    }
  });

  test('Texto deve ser legível em diferentes tamanhos de tela', async ({ page }) => {
    const testSizes = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];
    
    for (const size of testSizes) {
      await page.setViewportSize({ width: size.width, height: size.height });
      await page.goto(`${BASE_URL}/dashboard`);
      
      // Verificar se o texto principal está visível
      const mainHeading = page.locator('h1').first();
      if (await mainHeading.isVisible()) {
        const fontSize = await mainHeading.evaluate(el => 
          window.getComputedStyle(el).fontSize
        );
        
        console.log(`📱 ${size.name}: Título com fonte ${fontSize}`);
        
        // Fonte deve ser pelo menos 16px em mobile
        const fontSizeNum = parseInt(fontSize);
        if (size.width < 768) {
          expect(fontSizeNum).toBeGreaterThanOrEqual(16);
        }
      }
    }
  });

  test('Imagens devem se adaptar ao tamanho da tela', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto(`${BASE_URL}/dashboard`);
    
    // Verificar se há imagens na página
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < Math.min(imageCount, 3); i++) {
      const img = images.nth(i);
      if (await img.isVisible()) {
        const box = await img.boundingBox();
        if (box) {
          // Imagens não devem ultrapassar a largura da tela
          expect(box.width).toBeLessThanOrEqual(375);
          console.log(`✅ Imagem ${i + 1} responsiva: ${box.width}x${box.height}`);
        }
      }
    }
  });
});
