-- =====================================================
-- ADICIONAR COLUNA TRANSCRIPTION À TABELA MESSAGES
-- =====================================================
-- Este script adiciona a coluna transcription para armazenar
-- transcrições de áudios processados pela IA

-- Adicionar coluna transcription se não existir
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'messages' 
        AND column_name = 'transcription'
    ) THEN
        ALTER TABLE messages ADD COLUMN transcription TEXT;
        RAISE NOTICE 'Coluna transcription adicionada à tabela messages';
    ELSE
        RAISE NOTICE 'Coluna transcription já existe na tabela messages';
    END IF;
END $$;

-- Adicionar coment<PERSON>rio à coluna
COMMENT ON COLUMN messages.transcription IS 'Transcrição do áudio quando type = audio';

-- Criar índice para busca de transcrições
CREATE INDEX IF NOT EXISTS idx_messages_transcription 
ON messages(type, transcription) 
WHERE type = 'audio' AND transcription IS NOT NULL;

-- Verificar se a coluna foi criada
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'messages' 
AND column_name = 'transcription';
