
import React, { useState } from 'react';
import Button from '../shared/Button';
import { ICONS } from '../../constants';

interface MessageInputProps {
  onSend: (text: string) => void;
  disabled?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSend, disabled }) => {
  const [text, setText] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      onSend(text.trim());
      setText('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-center space-x-2">
      <input
        type="text"
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Digite sua mensagem..."
        className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary text-sm"
        disabled={disabled}
      />
      <Button type="submit" variant="primary" size="md" disabled={disabled || !text.trim()}>
        {ICONS.send}
        <span className="ml-2 hidden sm:inline">Enviar</span>
      </Button>
    </form>
  );
};

export default MessageInput;
    