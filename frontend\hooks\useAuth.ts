import { useState, useEffect } from 'react';
import { authService } from '../services/authService';

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(() => authService.isAuthenticated());
  const [user, setUser] = useState(() => authService.getUser());

  useEffect(() => {
    // Listener para mudanças no localStorage (outras abas)
    const handleStorageChange = () => {
      setIsAuthenticated(authService.isAuthenticated());
      setUser(authService.getUser());
    };

    // Escutar mudanças no localStorage
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []); // ✅ Array vazio - executa apenas uma vez

  return {
    isAuthenticated,
    user,
    login: async (credentials: { email: string; password: string }) => {
      await authService.login(credentials);
      // Forçar atualização do estado
      setIsAuthenticated(authService.isAuthenticated());
      setUser(authService.getUser());
    },
    logout: () => {
      authService.logout();
      // Forçar atualização do estado
      setIsAuthenticated(authService.isAuthenticated());
      setUser(authService.getUser());
    }
  };
};
