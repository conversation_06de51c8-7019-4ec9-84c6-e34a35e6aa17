/**
 * Serviço de Vector Database usando ChromaDB
 * Armazena e busca embeddings para o sistema RAG
 */

import { ChromaClient } from 'chromadb';
import { embeddingService, EmbeddingResult } from './embeddingService';

export interface DocumentMetadata {
  id: string;
  title: string;
  content: string;
  documentType: 'legislation' | 'project' | 'service' | 'faq' | 'agenda' | 'admin';
  category: 'health' | 'education' | 'infrastructure' | 'transparency' | 'legislation' | 'other';
  filePath?: string;
  uploadedAt: string;
  chunkIndex?: number;
  totalChunks?: number;
  source?: string;
  tags?: string[];
}

export interface SearchResult {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  similarity: number;
  distance: number;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
}

export class VectorDatabase {
  private client: ChromaClient;
  private collection: any = null;
  private collectionName = 'gabinete_knowledge_base';

  constructor() {
    // Inicializar ChromaDB client
    this.client = new ChromaClient({
      path: process.env.CHROMA_URL || 'http://localhost:8000'
    });
  }

  /**
   * Inicializar a coleção
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔧 Inicializando Vector Database...');

      // Tentar obter coleção existente ou criar nova
      try {
        this.collection = await this.client.getCollection({
          name: this.collectionName
        });
        console.log('✅ Coleção existente encontrada');
      } catch (error) {
        console.log('📝 Criando nova coleção...');
        this.collection = await this.client.createCollection({
          name: this.collectionName,
          metadata: {
            description: 'Base de conhecimento do gabinete da vereadora',
            version: '1.0.0',
            createdAt: new Date().toISOString()
          }
        });
        console.log('✅ Nova coleção criada');
      }

      // Verificar status da coleção
      const count = await this.collection.count();
      console.log(`📊 Vector Database inicializado: ${count} documentos indexados`);

    } catch (error) {
      console.error('❌ Erro ao inicializar Vector Database:', error);
      throw new Error(`Falha na inicialização: ${error}`);
    }
  }

  /**
   * Adicionar documento à base de conhecimento
   */
  async addDocument(
    content: string,
    metadata: Omit<DocumentMetadata, 'id' | 'uploadedAt'>
  ): Promise<string> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      console.log(`📝 Adicionando documento: ${metadata.title}`);

      // Dividir em chunks se necessário
      const chunks = embeddingService.splitIntoChunks(content, 1000, 200);
      console.log(`📄 Documento dividido em ${chunks.length} chunks`);

      // Gerar embeddings para cada chunk
      const embeddings = await embeddingService.generateEmbeddingBatch(chunks);

      // Preparar dados para inserção
      const ids: string[] = [];
      const embeddingVectors: number[][] = [];
      const metadatas: DocumentMetadata[] = [];
      const documents: string[] = [];

      embeddings.embeddings.forEach((embResult, index) => {
        const chunkId = chunks.length > 1 ? `${documentId}_chunk_${index}` : documentId;
        
        ids.push(chunkId);
        embeddingVectors.push(embResult.embedding);
        documents.push(embResult.text);
        metadatas.push({
          ...metadata,
          id: chunkId,
          content: embResult.text,
          uploadedAt: new Date().toISOString(),
          chunkIndex: index,
          totalChunks: chunks.length
        });
      });

      // Inserir na coleção
      await this.collection!.add({
        ids,
        embeddings: embeddingVectors,
        metadatas: metadatas as any,
        documents
      });

      console.log(`✅ Documento adicionado: ${ids.length} chunks indexados`);
      return documentId;

    } catch (error) {
      console.error('❌ Erro ao adicionar documento:', error);
      throw new Error(`Falha ao adicionar documento: ${error}`);
    }
  }

  /**
   * Buscar documentos similares
   */
  async search(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      const {
        limit = 10,
        threshold = 0.7,
        filter = {},
        includeMetadata = true
      } = options;

      console.log(`🔍 Buscando: "${query}" (limite: ${limit})`);

      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);

      // Buscar na coleção
      const results = await this.collection!.query({
        queryEmbeddings: [queryEmbedding.embedding],
        nResults: limit,
        where: Object.keys(filter).length > 0 ? filter : undefined
      });

      // Processar resultados
      const searchResults: SearchResult[] = [];

      if (results.ids && results.ids[0]) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance; // Converter distância para similaridade

          // Filtrar por threshold
          if (similarity >= threshold) {
            searchResults.push({
              id: results.ids[0][i],
              content: results.documents?.[0]?.[i] || '',
              metadata: results.metadatas?.[0]?.[i] as any,
              similarity,
              distance
            });
          }
        }
      }

      console.log(`📊 Encontrados ${searchResults.length} resultados relevantes`);
      return searchResults;

    } catch (error) {
      console.error('❌ Erro na busca:', error);
      throw new Error(`Falha na busca: ${error}`);
    }
  }

  /**
   * Buscar por categoria
   */
  async searchByCategory(
    query: string,
    category: DocumentMetadata['category'],
    limit: number = 5
  ): Promise<SearchResult[]> {
    return this.search(query, {
      limit,
      filter: { category }
    });
  }

  /**
   * Buscar por tipo de documento
   */
  async searchByDocumentType(
    query: string,
    documentType: DocumentMetadata['documentType'],
    limit: number = 5
  ): Promise<SearchResult[]> {
    return this.search(query, {
      limit,
      filter: { documentType }
    });
  }

  /**
   * Obter documento por ID
   */
  async getDocument(id: string): Promise<SearchResult | null> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      const results = await this.collection!.get({
        ids: [id],
        include: ['metadatas', 'documents']
      });

      if (results.ids && results.ids.length > 0) {
        return {
          id: results.ids[0],
          content: results.documents?.[0] || '',
          metadata: results.metadatas?.[0] as DocumentMetadata,
          similarity: 1,
          distance: 0
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Erro ao obter documento:', error);
      return null;
    }
  }

  /**
   * Remover documento
   */
  async removeDocument(id: string): Promise<boolean> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      // Buscar todos os chunks do documento
      const results = await this.collection!.get({
        where: {
          $or: [
            { id: { $eq: id } },
            { id: { $like: `${id}_chunk_%` } }
          ]
        }
      });

      if (results.ids && results.ids.length > 0) {
        await this.collection!.delete({
          ids: results.ids
        });
        console.log(`🗑️ Documento removido: ${results.ids.length} chunks`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Erro ao remover documento:', error);
      return false;
    }
  }

  /**
   * Obter estatísticas da coleção
   */
  async getStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
  }> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      const totalDocuments = await this.collection!.count();
      
      // Obter todos os metadados para estatísticas
      const allDocs = await this.collection!.get({
        include: ['metadatas']
      });

      const documentsByType: Record<string, number> = {};
      const documentsByCategory: Record<string, number> = {};

      if (allDocs.metadatas) {
        allDocs.metadatas.forEach((metadata: any) => {
          const docType = metadata.documentType || 'unknown';
          const category = metadata.category || 'unknown';

          documentsByType[docType] = (documentsByType[docType] || 0) + 1;
          documentsByCategory[category] = (documentsByCategory[category] || 0) + 1;
        });
      }

      return {
        totalDocuments,
        documentsByType,
        documentsByCategory
      };
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas:', error);
      return {
        totalDocuments: 0,
        documentsByType: {},
        documentsByCategory: {}
      };
    }
  }

  /**
   * Limpar toda a coleção
   */
  async clearCollection(): Promise<void> {
    if (!this.collection) {
      await this.initialize();
    }

    try {
      await this.client.deleteCollection({ name: this.collectionName });
      this.collection = null;
      console.log('🗑️ Coleção limpa completamente');
    } catch (error) {
      console.error('❌ Erro ao limpar coleção:', error);
      throw error;
    }
  }
}

// Instância singleton
export const vectorDatabase = new VectorDatabase();
