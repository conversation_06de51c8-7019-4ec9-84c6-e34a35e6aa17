import { GoogleGenerativeAI } from '@google/generative-ai';
import { IMessage } from '../models/Message';
import { IContact } from '../models/Contact';
import { ragService } from './ragService';
import { intentClassifier } from './intentClassifier';

interface AudioData {
  buffer: Buffer;
  mimetype: string;
}

// =========================================================
// SISTEMA DE CACHE INTELIGENTE
// =========================================================
class IntelligentCache {
  private static cache = new Map<string, { data: any; timestamp: number; hits: number }>();
  private static readonly TTL = 30 * 60 * 1000; // 30 minutos
  private static readonly MAX_SIZE = 1000;
  private static cleanupInProgress = false;

  static set(key: string, data: any): void {
    const now = Date.now();
    this.cache.set(key, { data, timestamp: now, hits: 0 });
    
    if (this.cache.size > this.MAX_SIZE && !this.cleanupInProgress) {
      this.performCleanup();
    }
  }

  static get(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    const now = Date.now();
    if (now - entry.timestamp > this.TTL) {
      this.cache.delete(key);
      return null;
    }
    
    entry.hits++;
    return entry.data;
  }

  private static performCleanup(): void {
    this.cleanupInProgress = true;
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    // Remove itens expirados
    entries.forEach(([key, entry]) => {
      if (now - entry.timestamp > this.TTL) {
        this.cache.delete(key);
      }
    });
    
    // Se ainda estiver cheio, remove menos usados
    if (this.cache.size > this.MAX_SIZE * 0.8) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort(([,a], [,b]) => a.hits - b.hits)
        .slice(0, Math.floor(this.MAX_SIZE * 0.2));
      
      sortedEntries.forEach(([key]) => {
        this.cache.delete(key);
      });
    }
    
    this.cleanupInProgress = false;
  }

  static getStats() {
    return {
      size: this.cache.size,
      maxSize: this.MAX_SIZE,
      hitRate: Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.hits, 0)
    };
  }
}

// =========================================================
// RATE LIMITER INTELIGENTE
// =========================================================
class AdaptiveRateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number; lastRequest: number }>();
  private static readonly WINDOW_MS = 60 * 1000; // 1 minuto
  private static baseLimit = 30; // 30 req/min por padrão

  static async checkLimit(key: string, priority: 'high' | 'medium' | 'low' = 'medium'): Promise<{ allowed: boolean; retryAfter?: number }> {
    const now = Date.now();
    const entry = this.requests.get(key) || { count: 0, resetTime: now + this.WINDOW_MS, lastRequest: 0 };
    
    // Reset window se necessário
    if (now >= entry.resetTime) {
      entry.count = 0;
      entry.resetTime = now + this.WINDOW_MS;
    }
    
    // Limites adaptativos baseados na prioridade
    const limit = this.getAdaptiveLimit(key, priority, entry.lastRequest, now);
    
    if (entry.count >= limit) {
      return {
        allowed: false,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000)
      };
    }
    
    entry.count++;
    entry.lastRequest = now;
    this.requests.set(key, entry);

    return { allowed: true };
  }

  private static getAdaptiveLimit(key: string, priority: 'high' | 'medium' | 'low', lastRequest: number, now: number): number {
    let multiplier = 1;
    
    // Ajusta baseado na prioridade
    switch (priority) {
      case 'high': multiplier = 1.5; break;
      case 'low': multiplier = 0.7; break;
    }
    
    // Reduz limite se requests muito frequentes
    const timeSinceLastRequest = now - lastRequest;
    if (timeSinceLastRequest < 1000) { // < 1 segundo
      multiplier *= 0.5;
    }
    
    return Math.floor(this.baseLimit * multiplier);
  }
}

// =========================================================
// SISTEMA DE RETRY INTELIGENTE
// =========================================================
class IntelligentRetry {
  private static readonly MAX_RETRIES = 3;
  private static readonly BASE_DELAY = 1000;
  private static retryStats = new Map<string, { attempts: number; lastSuccess: number }>();

  static async execute<T>(
    operation: () => Promise<T>,
    context: string,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): Promise<T> {
    const stats = this.retryStats.get(context) || { attempts: 0, lastSuccess: Date.now() };
    
    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      try {
        const result = await operation();
        stats.lastSuccess = Date.now();
        stats.attempts = 0;
        this.retryStats.set(context, stats);
        return result;
      } catch (error: any) {
        stats.attempts++;
        
        if (attempt === this.MAX_RETRIES - 1) {
          this.retryStats.set(context, stats);
          throw error;
        }
        
        const delay = this.calculateDelay(attempt, priority, stats);
        console.log(`🔄 Tentativa ${attempt + 1}/${this.MAX_RETRIES} falhou para ${context}. Retry em ${delay}ms`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw new Error('Máximo de tentativas excedido');
  }

  private static calculateDelay(attempt: number, priority: string, stats: { attempts: number; lastSuccess: number }): number {
    let delay = this.BASE_DELAY * Math.pow(2, attempt); // Exponential backoff
    
    // Ajusta baseado na prioridade
    switch (priority) {
      case 'high': delay *= 0.5; break;
      case 'low': delay *= 1.5; break;
    }
    
    // Adiciona jitter para evitar thundering herd
    delay += Math.random() * 500;
    
    // Limita delay máximo
    return Math.min(delay, 30000);
  }
}

// =========================================================
// MONITOR DE PERFORMANCE ADAPTATIVO
// =========================================================
class PerformanceMonitor {
  private static metrics = {
    requests: 0,
    errors: 0,
    totalTime: 0,
    avgTime: 0,
    cacheHits: 0,
    templateUsage: 0
  };

  static recordRequest(duration: number, method: 'template' | 'ai' | 'cache'): void {
    this.metrics.requests++;
    this.metrics.totalTime += duration;
    this.metrics.avgTime = this.metrics.totalTime / this.metrics.requests;
    
    switch (method) {
      case 'cache': this.metrics.cacheHits++; break;
      case 'template': this.metrics.templateUsage++; break;
    }
  }

  static recordError(): void {
    this.metrics.errors++;
  }

  static getMetrics() {
    return {
      ...this.metrics,
      errorRate: this.metrics.errors / this.metrics.requests || 0,
      cacheHitRate: this.metrics.cacheHits / this.metrics.requests || 0
    };
  }

  static shouldUseTemplate(): boolean {
    const metrics = this.getMetrics();
    // Usa template se taxa de erro alta ou latência alta
    return metrics.errorRate > 0.1 || metrics.avgTime > 5000;
  }
}

// =========================================================
// SISTEMA DE TEMPLATES ADAPTATIVOS MELHORADO
// =========================================================
class AdaptiveResponseSystem {
  private static templates = {
    saudacao: [
      "Oi {nome}! Como você e o {bebe} estão hoje? Seguimos firmes na nossa jornada! 🧡",
      "Que bom te ouvir, {nome}! Como anda nossa caminhada com o {bebe}? Deus abençoa vocês! 💪🏽",
      "Oi minha querida {nome}! E aí, como estão as coisas por aí? O {bebe} mexendo bastante? 😊"
    ],
    
    dor_normal: [
      "Entendo sua preocupação, {nome}. Essas dores são comuns no {trimestre}. O {bebe} está crescendo! Seguimos firmes. Mas se intensificar, procure seu médico.",
      "Minha querida, no {trimestre} é normal sentir isso. O {bebe} está se desenvolvendo bem! Relaxa, respira fundo. Deus cuida de vocês."
    ],
    
    exames: [
      "Que bom que está acompanhando direitinho, {nome}! Os exames do {trimestre} são importantes para você e o {bebe}. Seguimos firmes no cuidado!",
      "Parabéns por ser tão cuidadosa! No {trimestre}, esses exames mostram como o {bebe} está crescendo. Nossa gente precisa desse carinho todo."
    ],

    movimento_bebe: [
      "Que alegria, {nome}! O {bebe} mexendo é sinal de que está bem ativo e saudável! Seguimos firmes com essa benção. Como tem sido essa experiência?",
      "Ai que amor, {nome}! Sentir o {bebe} se mexer é puro amor divino. Deus abençoa essa conexão especial entre vocês duas!"
    ],

    alimentacao: [
      "Muito bem, {nome}! Cuidar da alimentação é cuidar do {bebe} também. Frutas, verduras e muito amor. Seguimos firmes na alimentação saudável!",
      "Perfeito, minha querida! O que você come, o {bebe} também recebe. Essa dedicação toda é linda de ver. Nossa gente cuidando direito!"
    ]
  };

  private static messagePatterns = new Map([
    ['saudacao', [/^(oi|olá|bom dia|boa tarde|boa noite|tudo bem|como vai)/i]],
    ['dor_normal', [/dor/i, /(?!forte|intensa|muito|muita)/i]],
    ['exames', [/(exame|ultrassom|médico|consulta)/i]],
    ['movimento_bebe', [/(mexendo|movimento|mexe|chutando|chute)/i]],
    ['alimentacao', [/(comer|comida|alimentação|fruta|verdura)/i]],
    ['emergencia', [/(sangramento|dor forte|emergência|hospital|urgente|sangue|contração)/i]]
  ]);

  public static classifyMessage(message: string): 'saudacao' | 'dor_normal' | 'exames' | 'movimento_bebe' | 'alimentacao' | 'emergencia' | 'complexa' {
    const cacheKey = `classify_${message.substring(0, 50)}`;
    const cached = IntelligentCache.get(cacheKey);
    if (cached) return cached;

    const msg = message.toLowerCase();
    let classification: string = 'complexa';

    // Verifica emergência primeiro
    if (this.messagePatterns.get('emergencia')?.[0]?.test(msg)) {
      classification = 'emergencia';
    } else {
      // Verifica outros padrões em ordem de prioridade
      for (const [type, patterns] of this.messagePatterns.entries()) {
        if (type === 'emergencia') continue;
        if (patterns.some(pattern => pattern.test(msg))) {
          classification = type;
          break;
        }
      }
    }

    IntelligentCache.set(cacheKey, classification);
    return classification as any;
  }

  public static applyTemplate(contact: IContact, type: keyof typeof AdaptiveResponseSystem.templates): string {
    const templates = this.templates[type];
    const template = templates[Math.floor(Math.random() * templates.length)];
    
    const babyGender = contact.babyGender === 'male' ? 'bebezinho' : 
                      contact.babyGender === 'female' ? 'bebezinha' : 'bebê';
    
    return template
      .replace('{nome}', contact.name)
      .replace('{bebe}', babyGender)
      .replace('{trimestre}', 'seu trimestre');
  }

  public static detectUrgency(message: string): boolean {
    const urgentKeywords = ['sangramento', 'dor forte', 'emergência', 'hospital', 'urgente', 'sangue', 'contracao'];
    return urgentKeywords.some(keyword => message.toLowerCase().includes(keyword));
  }
}

// =========================================================
// OTIMIZADOR DE PROMPTS MELHORADO
// =========================================================
class PromptOptimizer {
  private static basePersonality = `Você é "Rafaela" - vereadora jovem de Parnamirim, fonoaudióloga cristã.
ESTILO: "seguimos firmes", "minha querida" | FÉ: "Deus abençoa" | TOM: maternal, próxima
RESPOSTA: 40-60 palavras, concisa, carinhosa, informativa`;

  private static contextCache = new Map<string, { data: string; timestamp: number }>();
  private static readonly CONTEXT_TTL = 10 * 60 * 1000; // 10 minutos

  public static generateOptimizedPrompt(
    contact: IContact, 
    message: string, 
    hasAudio: boolean = false,
    conversationSummary?: string
  ): string {
    const cacheKey = `${contact.phone}_${contact.babyGender}`;
    const now = Date.now();
    
    // Verifica cache com TTL
    let cached = this.contextCache.get(cacheKey);
    if (cached && now - cached.timestamp < this.CONTEXT_TTL) {
      return this.buildPrompt(cached.data, message, hasAudio, conversationSummary);
    }
    
    // Gera novo contexto
    const contactContext = `${contact.name} | ${contact.babyGender === 'male' ? 'menino' : 
                            contact.babyGender === 'female' ? 'menina' : 'bebê'}`;
    this.contextCache.set(cacheKey, { data: contactContext, timestamp: now });
    
    return this.buildPrompt(contactContext, message, hasAudio, conversationSummary);
  }

  private static buildPrompt(contactContext: string, message: string, hasAudio: boolean, conversationSummary?: string): string {
    return `${this.basePersonality}

GESTANTE: ${contactContext}
${conversationSummary ? `HISTÓRICO: ${conversationSummary}` : ''}
MENSAGEM: ${message}${hasAudio ? ' (+ ÁUDIO)' : ''}

RESPONDA como Rafaela:`;
  }

  public static generateHybridPrompt(
    contact: IContact, 
    message: string, 
    hasAudio: boolean = false,
    conversationSummary?: string
  ): string {
    const basePrompt = this.generateOptimizedPrompt(contact, message, hasAudio, conversationSummary);
    
    return `${basePrompt}

FORMATO DE RESPOSTA:
1. RESPOSTA_RAFAELA: [sua resposta natural de 40-60 palavras]
2. ANÁLISE_JSON: {"sentiment":"positive/negative/neutral","urgency":"alta/media/baixa","needs":["lista"],"medical":true/false}

EXEMPLO:
RESPOSTA_RAFAELA: Oi minha querida! Que bom saber de você. Essas dores são normais no segundo trimestre, mas seguimos firmes! Deus abençoa nossa caminhada. Como está se sentindo hoje?

ANÁLISE_JSON: {"sentiment":"neutral","urgency":"media","needs":["informação sobre dores","acompanhamento"],"medical":false}`;
  }

  public static summarizeConversation(messages: IMessage[]): string {
    if (messages.length === 0) return '';
    
    const recentMessages = messages.slice(-3);
    const topics = new Set<string>();
    
    recentMessages.forEach(msg => {
      const content = msg.content.toLowerCase();
      if (content.includes('dor')) topics.add('dor');
      if (content.includes('exame')) topics.add('exames');
      if (content.includes('bebê') || content.includes('bebe')) topics.add('bebê');
      if (content.includes('médico')) topics.add('médico');
      if (content.includes('preocupa')) topics.add('preocupação');
      if (content.includes('mexendo')) topics.add('movimento');
      if (content.includes('alimenta')) topics.add('alimentação');
    });
    
    return topics.size > 0 ? `Tópicos: ${Array.from(topics).join(', ')}` : '';
  }
}

// =========================================================
// FUNÇÕES AUXILIARES MANTIDAS E OTIMIZADAS
// =========================================================
function cleanGeminiJSON(text: string, attempt: number = 1): string {
  try {
    console.log(`🧹 Tentativa ${attempt} - Limpando JSON do Gemini:`, text.substring(0, 100) + '...');

    let cleaned = text;
    cleaned = cleaned.replace(/```json\s*/gi, '').replace(/```\s*/g, '');
    cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    cleaned = cleaned.trim();

    const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      cleaned = jsonMatch[0];
    }

    if (!cleaned.startsWith('{') && cleaned.includes('[')) {
      const arrayMatch = cleaned.match(/\[[\s\S]*\]/);
      if (arrayMatch) {
        cleaned = arrayMatch[0];
      }
    }

    cleaned = cleaned
      .replace(/,\s*}/g, '}')
      .replace(/,\s*]/g, ']')
      .replace(/\n\s*\n/g, '\n')
      .replace(/\t/g, ' ')
      .replace(/\r/g, '');

    console.log(`✅ Tentativa ${attempt} - JSON limpo:`, cleaned.substring(0, 100) + '...');
    return cleaned;
  } catch (error) {
    console.error(`❌ Erro na tentativa ${attempt} ao limpar JSON do Gemini:`, error);
    return text;
  }
}

function parseJSONWithFallback(text: string): any {
  const maxAttempts = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const cleaned = cleanGeminiJSON(text, attempt);
      const parsed = JSON.parse(cleaned);
      console.log(`✅ JSON parseado com sucesso na tentativa ${attempt}`);
      return parsed;
    } catch (error) {
      lastError = error as Error;
      console.warn(`⚠️ Tentativa ${attempt} falhou:`, error);

      if (attempt < maxAttempts) {
        if (attempt === 2) {
          text = text.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
        } else if (attempt === 3) {
          const simpleMatch = text.match(/\{[^{}]*\}/);
          if (simpleMatch) {
            text = simpleMatch[0];
          }
        }
      }
    }
  }

  console.error('❌ Todas as tentativas de parsing falharam');
  console.error('📄 Texto original completo:', text);
  console.error('📄 Último erro:', lastError?.message);

  throw new Error(`Falha ao fazer parse do JSON após ${maxAttempts} tentativas: ${lastError?.message}`);
}

function validateResponseLength(response: string, minWords: number = 15, maxWords: number = 60): string {
  const words = response.trim().split(/\s+/);
  const wordCount = words.length;

  console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);

  if (wordCount >= minWords && wordCount <= maxWords) {
    console.log('✅ Tamanho da resposta adequado');
    return response;
  }

  if (wordCount > maxWords) {
    const text = response.trim();
    const sentences = text.split(/[.!?]+/);
    let truncated = '';
    let currentWords = 0;

    for (const sentence of sentences) {
      const sentenceWords = sentence.trim().split(/\s+/).length;
      if (currentWords + sentenceWords <= maxWords && sentence.trim()) {
        truncated += (truncated ? '. ' : '') + sentence.trim();
        currentWords += sentenceWords;
      } else {
        break;
      }
    }

    if (truncated && currentWords >= minWords) {
      console.log(`✂️ Resposta truncada inteligentemente de ${wordCount} para ${currentWords} palavras (frases completas)`);
      return truncated + (truncated.endsWith('.') || truncated.endsWith('!') || truncated.endsWith('?') ? '' : '.');
    }

    const wordsTruncated = words.slice(0, maxWords);
    let result = wordsTruncated.join(' ');

    if (result.endsWith(',') || result.endsWith(';')) {
      const lastSpaceIndex = result.lastIndexOf(' ');
      if (lastSpaceIndex > 0) {
        result = result.substring(0, lastSpaceIndex);
      }
    }

    console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras (fallback)`);
    return result + (result.endsWith('.') || result.endsWith('!') || result.endsWith('?') ? '' : '...');
  }

  if (wordCount < minWords) {
    console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
    return response;
  }

  return response;
}

// =========================================================
// CLASSE PRINCIPAL OTIMIZADA E INTELIGENTE
// =========================================================
export class GeminiAIService {
  private ai: GoogleGenerativeAI | null = null;
  private model: any = null;
  private isInitialized: boolean = false;
  private static cleanupStarted: boolean = false;
  private config = {
    maxRetries: 3,
    timeout: 30000,
    temperature: 0.7,
    maxTokens: 200
  };

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;

    if (!apiKey) {
      console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
      this.isInitialized = false;
      return;
    }

    try {
      this.ai = new GoogleGenerativeAI(apiKey);
      this.model = this.ai.getGenerativeModel({ model: "gemini-1.5-flash" });
      this.isInitialized = true;
      console.log('✅ Gemini AI inicializado com sucesso (modelo: gemini-1.5-flash)');

      // Iniciar limpeza apenas uma vez globalmente
      if (!GeminiAIService.cleanupStarted) {
        this.startAutoCleanup();
        GeminiAIService.cleanupStarted = true;
      }
    } catch (error) {
      console.error('❌ Erro ao inicializar Gemini AI:', error);
      this.isInitialized = false;
    }
  }

  private startAutoCleanup(): void {
    // Limpeza a cada 6 horas (menos frequente)
    setInterval(async () => {
      await this.cleanupOldFiles();
    }, 6 * 60 * 60 * 1000);

    console.log('🧹 Limpeza automática de arquivos configurada (a cada 6 horas)');
  }

  public async generateContent(promptParts: string | any[]): Promise<string> {
    if (!this.isInitialized || !this.model) {
      throw new Error('Gemini AI não está inicializado');
    }

    return IntelligentRetry.execute(async () => {
      console.log('🤖 Enviando requisição para Gemini...');

      if (Array.isArray(promptParts)) {
        console.log(`📋 Partes da requisição: ${promptParts.length}`);
        promptParts.forEach((part, index) => {
          if (typeof part === 'string') {
            console.log(`   ${index + 1}. Texto: ${part.substring(0, 100)}...`);
          } else if (part.inlineData) {
            console.log(`   ${index + 1}. Mídia: ${part.inlineData.mimeType}, ${(part.inlineData.data.length / 1024).toFixed(1)}KB`);
          }
        });
      }

      // Timeout configurável
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Timeout na requisição Gemini')), this.config.timeout);
      });

      const result = await Promise.race([
        this.model.generateContent(promptParts),
        timeoutPromise
      ]);

      const response = await result.response;
      const text = response.text() || '';

      console.log('✅ Resposta recebida do Gemini');
      return text;

    }, 'gemini_generation', 'medium');
  }

  // =========================================================
  // MÉTODOS DE DIAGNÓSTICO E MONITORAMENTO
  // =========================================================
  public getSystemHealth() {
    return {
      initialized: this.isInitialized,
      cacheStats: IntelligentCache.getStats(),
      performanceMetrics: PerformanceMonitor.getMetrics(),
      configuration: this.config
    };
  }

  public async runDiagnostics(): Promise<{ status: string; details: any; recommendations: string[] }> {
    const health = this.getSystemHealth();
    const recommendations: string[] = [];
    
    if (health.performanceMetrics.avgTime > 10000) {
      recommendations.push('Considere aumentar uso de templates para reduzir latência');
    }
    
    if (health.performanceMetrics.errorRate > 0.1) {
      recommendations.push('Taxa de erro alta - verificar conectividade com Gemini API');
    }
    
    if (health.cacheStats.hitRate < 0.3) {
      recommendations.push('Taxa de cache baixa - revisar estratégia de cache');
    }
    
    let connectivityTest = { success: false, latency: 0 };
    if (this.isInitialized) {
      try {
        const start = Date.now();
        await this.generateContent('teste de conectividade - responda apenas "OK"');
        connectivityTest = { success: true, latency: Date.now() - start };
      } catch (error) {
        connectivityTest = { success: false, latency: -1 };
      }
    }
    
    const status = this.isInitialized && connectivityTest.success ? 'healthy' : 'unhealthy';
    
    return {
      status,
      details: { ...health, connectivityTest },
      recommendations
    };
  }

  public updateConfiguration(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Configuração atualizada:', this.config);
  }

  // =========================================================
  // NOVA FUNÇÃO PRINCIPAL OTIMIZADA
  // =========================================================
  public async generateOptimizedResponse(contact: IContact, message: string, audioData?: AudioData) {
    const startTime = Date.now();

    try {
      // Usar sistema RAG-enhanced por padrão
      console.log('🚀 Usando sistema RAG-enhanced para resposta otimizada');
      return await this.generateRAGEnhancedResponse(contact, message, audioData);

      // Código legado mantido para fallback se necessário
      /*
      const messageType = AdaptiveResponseSystem.classifyMessage(message);
      const priority = messageType === 'emergencia' ? 'high' : messageType === 'complexa' ? 'medium' : 'low';

      const rateLimitCheck = await AdaptiveRateLimiter.checkLimit(contact.phone, priority);
      if (!rateLimitCheck.allowed) {
        throw new Error(`Rate limit excedido. Tente novamente em ${rateLimitCheck.retryAfter} segundos.`);
      }
      
      const cacheKey = `response_${contact.phone}_${this.hashMessage(message)}_${!!audioData}`;
      const cachedResponse = IntelligentCache.get(cacheKey);
      if (cachedResponse && messageType !== 'emergencia') {
        const processingTime = Date.now() - startTime;
        PerformanceMonitor.recordRequest(processingTime, 'cache');
        return { ...cachedResponse, processingTime: `${processingTime}ms (cache)`, method: 'cache' };
      }
      
      const hasUrgency = AdaptiveResponseSystem.detectUrgency(message);
      console.log(`🔍 Tipo: ${messageType}, Urgência: ${hasUrgency}, Prioridade: ${priority}`);

      const shouldUseTemplate = (
        messageType !== 'complexa' && 
        messageType !== 'emergencia' && 
        !audioData && 
        !hasUrgency &&
        !PerformanceMonitor.shouldUseTemplate()
      );

      let result;
      if (shouldUseTemplate) {
        result = await this.generateTemplateResponse(contact, messageType, startTime);
      } else {
        result = await this.generateAIResponse(contact, message, audioData, messageType, startTime);
      }
      
      if (messageType !== 'emergencia' && result.method !== 'template') {
        IntelligentCache.set(cacheKey, result);
      }
      
      return result;
      */

    } catch (error) {
      console.error('❌ Erro na resposta RAG-enhanced (fallback):', error);

      // Fallback para template simples
      const messageType = 'saudacao';
      const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
      return validateResponseLength(response + ' (Resposta automática)', 30, 70);
    }
  }

  private hashMessage(message: string): string {
    return message.length.toString() + message.substring(0, 10) + message.substring(message.length - 10);
  }

  private async generateTemplateResponse(contact: IContact, messageType: any, startTime: number) {
    const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
    const processingTime = Date.now() - startTime;
    
    PerformanceMonitor.recordRequest(processingTime, 'template');
    console.log(`⚡ Resposta por template em ${processingTime}ms`);
    
    return {
      response: validateResponseLength(response, 30, 70),
      sentiment: { type: 'positive', score: 0.8, emotions: ['cuidado', 'carinho'] },
      needs: ['acompanhamento de rotina'],
      suggestions: ['manter contato regular'],
      priority: 'media',
      medical_attention: false,
      follow_up: ['acompanhar evolução'],
      processingTime: `${processingTime}ms`,
      method: 'template'
    };
  }

  private async generateAIResponse(contact: IContact, message: string, audioData: AudioData | undefined, messageType: any, startTime: number) {
    const context = await this.getConversationContext(contact, 3);
    const conversationSummary = PromptOptimizer.summarizeConversation(context);
    
    const result = await this.generateIntegratedResponse(contact, message, audioData, conversationSummary);
    
    const processingTime = Date.now() - startTime;
    PerformanceMonitor.recordRequest(processingTime, 'ai');
    console.log(`🤖 Resposta por IA em ${processingTime}ms`);
    
    return {
      ...result,
      processingTime: `${processingTime}ms`,
      method: 'ai_optimized'
    };
  }

  private generateFallbackResponse(contact: IContact, message: string, startTime: number) {
    const messageType = 'saudacao'; // Fallback seguro
    const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
    const processingTime = Date.now() - startTime;
    
    return {
      response: validateResponseLength(response + ' (Resposta automática)', 30, 70),
      sentiment: { type: 'neutral', score: 0.6, emotions: ['automatico'] },
      needs: ['verificar conexão'],
      suggestions: ['tentar novamente'],
      priority: 'media',
      medical_attention: false,
      follow_up: ['acompanhar sistema'],
      processingTime: `${processingTime}ms`,
      method: 'fallback'
    };
  }

  private async getConversationContext(_contact: IContact, _limit: number = 10) {
    console.log('📝 Contexto de conversa não implementado para Supabase');
    return [];
  }

  // =========================================================
  // ANÁLISE INTEGRADA - UMA ÚNICA CHAMADA
  // =========================================================
  private async generateIntegratedResponse(
    contact: IContact, 
    message: string, 
    audioData?: AudioData,
    conversationSummary?: string
  ): Promise<{response: string; sentiment: any; needs: string[]; suggestions: string[]; priority: string; medical_attention: boolean; follow_up: string[]}> {
    
    const hybridPrompt = PromptOptimizer.generateHybridPrompt(
      contact, message, !!audioData, conversationSummary
    );

    const promptParts: any[] = [hybridPrompt];
    
    if (audioData) {
      // Validar dados de áudio antes de enviar
      if (!this.validateAudioData(audioData.buffer, audioData.mimetype)) {
        throw new Error('Dados de áudio inválidos ou corrompidos');
      }

      promptParts.push({
        inlineData: {
          data: audioData.buffer.toString('base64'),
          mimeType: this.validateMimeType(audioData.mimetype)
        }
      });
    }

    const rawResponse = await this.generateContent(promptParts);
    
    return this.parseHybridResponse(rawResponse);
  }

  private parseHybridResponse(response: string): {response: string; sentiment: any; needs: string[]; suggestions: string[]; priority: string; medical_attention: boolean; follow_up: string[]} {
    try {
      // Extrai resposta da Rafaela
      const responseMatch = response.match(/RESPOSTA_RAFAELA:\s*(.*?)(?=ANÁLISE_JSON:|$)/s);
      const rafaelaResponse = responseMatch ? responseMatch[1].trim() : response;
      
      // Extrai e parseia análise JSON
      const analysisMatch = response.match(/ANÁLISE_JSON:\s*(\{.*?\})/s);
      let analysis = {
        sentiment: 'neutral',
        urgency: 'media',
        needs: ['acompanhamento geral'],
        medical: false
      };
      
      if (analysisMatch) {
        try {
          analysis = JSON.parse(analysisMatch[1]);
        } catch (e) {
          console.warn('⚠️ Fallback para análise padrão');
        }
      }
      
      return {
        response: validateResponseLength(rafaelaResponse, 30, 70),
        sentiment: {
          type: analysis.sentiment,
          score: analysis.sentiment === 'positive' ? 0.8 : analysis.sentiment === 'negative' ? 0.3 : 0.6,
          emotions: analysis.sentiment === 'positive' ? ['alegria'] : analysis.sentiment === 'negative' ? ['preocupação'] : ['neutro']
        },
        needs: Array.isArray(analysis.needs) ? analysis.needs : ['acompanhamento'],
        suggestions: ['manter acompanhamento', 'observar sintomas'],
        priority: analysis.urgency || 'media',
        medical_attention: analysis.medical || false,
        follow_up: ['acompanhar evolução']
      };
      
    } catch (error) {
      console.error('❌ Erro no parse híbrido:', error);
      return {
        response: validateResponseLength(response, 30, 70),
        sentiment: { type: 'neutral', score: 0.5, emotions: ['indefinido'] },
        needs: ['revisar resposta'],
        suggestions: ['verificar logs'],
        priority: 'media',
        medical_attention: false,
        follow_up: ['acompanhar']
      };
    }
  }

  private validateMimeType(mimetype: string): string {
    const supportedTypes = ['audio/wav', 'audio/mp3', 'audio/ogg', 'audio/aac', 'audio/flac'];
    if (supportedTypes.includes(mimetype)) {
      return mimetype;
    }

    // Fallbacks para tipos comuns
    if (mimetype.includes('webm') || mimetype.includes('opus')) {
      return 'audio/ogg';
    }
    if (mimetype.includes('mpeg') || mimetype.includes('mp3')) {
      return 'audio/mp3';
    }
    if (mimetype.includes('wav')) {
      return 'audio/wav';
    }

    console.log(`🔄 MimeType convertido de ${mimetype} para audio/ogg`);
    return 'audio/ogg';
  }

  private validateAudioData(buffer: Buffer, mimetype: string): boolean {
    // Validações básicas
    if (!buffer || buffer.length === 0) {
      console.error('❌ Buffer de áudio vazio');
      return false;
    }

    if (buffer.length < 100) {
      console.error('❌ Arquivo de áudio muito pequeno:', buffer.length, 'bytes');
      return false;
    }

    if (buffer.length > 10 * 1024 * 1024) { // 10MB
      console.error('❌ Arquivo de áudio muito grande:', buffer.length, 'bytes');
      return false;
    }

    // Verificar assinatura do arquivo (magic numbers)
    const signature = buffer.toString('hex', 0, 4);
    const validSignatures = [
      '52494646', // RIFF (WAV)
      '494433',   // ID3 (MP3)
      'fff3',     // MP3
      'fff2',     // MP3
      '4f676753', // OggS (OGG)
      '664c6143'  // fLaC (FLAC)
    ];

    const isValidSignature = validSignatures.some(sig =>
      signature.toLowerCase().startsWith(sig.toLowerCase())
    );

    if (!isValidSignature) {
      console.warn('⚠️ Assinatura de arquivo não reconhecida:', signature);
      // Não bloquear, apenas avisar
    }

    console.log('✅ Validação de áudio passou:', {
      size: buffer.length,
      mimetype,
      signature: signature.substring(0, 8)
    });

    return true;
  }

  // =========================================================
  // FUNÇÃO PARA SISTEMA RAG
  // =========================================================
  public async generateRAGResponse(userPrompt: string, systemPrompt?: string): Promise<string> {
    try {
      if (!this.isInitialized || !this.model) {
        throw new Error('Gemini AI não está inicializado');
      }

      const prompt = systemPrompt
        ? `${systemPrompt}\n\nUsuário: ${userPrompt}`
        : userPrompt;

      const result = await this.model.generateContent(prompt);
      const response = result.response;
      const text = response.text();

      return text.trim();
    } catch (error) {
      console.error('❌ Erro ao gerar resposta RAG:', error);
      throw error;
    }
  }

  // =========================================================
  // FUNÇÃO RAG PARA WHATSAPP - NOVA FUNCIONALIDADE
  // =========================================================
  public async generateRAGEnhancedResponse(contact: IContact, message: string, audioData?: AudioData): Promise<string> {
    const startTime = Date.now();

    try {
      console.log(`🧠 Gerando resposta RAG-enhanced para ${contact.name}: "${message}"`);

      // 1. Classificar intenção da mensagem
      const classification = await intentClassifier.classifyIntent(message, {
        threshold: 0.6,
        includeReasoning: false,
        suggestActions: false
      });

      console.log(`🎯 Intenção classificada: ${classification.intent.category} (${(classification.confidence * 100).toFixed(1)}%)`);

      // 2. Buscar na base de conhecimento se a confiança for alta
      let ragContext = '';
      let usedRAG = false;

      if (classification.confidence >= 0.7) {
        try {
          const ragResponse = await ragService.processQuery({
            question: message,
            category: classification.intent.category,
            maxResults: 3,
            threshold: 0.6
          });

          if (ragResponse.confidence >= 0.5 && ragResponse.sources.length > 0) {
            // Construir contexto da base de conhecimento
            ragContext = ragResponse.sources
              .slice(0, 2) // Usar apenas as 2 melhores fontes
              .map(source => source.content.substring(0, 300))
              .join('\n\n');

            usedRAG = true;
            console.log(`📚 Contexto RAG encontrado: ${ragResponse.sources.length} fontes (confiança: ${(ragResponse.confidence * 100).toFixed(1)}%)`);
          }
        } catch (error) {
          console.warn('⚠️ Erro ao buscar contexto RAG:', error);
        }
      }

      // 3. Construir prompt contextualizado
      const contextualPrompt = this.buildWhatsAppPrompt(contact, message, classification, ragContext, usedRAG);

      // 4. Gerar resposta com Gemini
      const response = await this.model.generateContent(contextualPrompt);
      const rawResponse = response.response.text().trim();

      // 5. Validar e ajustar resposta
      const finalResponse = this.validateWhatsAppResponse(rawResponse, contact, usedRAG);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Resposta RAG-enhanced gerada em ${processingTime}ms (RAG: ${usedRAG ? 'Sim' : 'Não'})`);

      return finalResponse;

    } catch (error) {
      console.error('❌ Erro na resposta RAG-enhanced:', error);

      // Fallback para resposta padrão
      return this.generateOptimizedResponse(contact, message, audioData);
    }
  }

  /**
   * Construir prompt contextualizado para WhatsApp
   */
  private buildWhatsAppPrompt(
    contact: IContact,
    message: string,
    classification: any,
    ragContext: string,
    usedRAG: boolean
  ): string {
    const basePrompt = `Você é Rafaela, assistente virtual da vereadora Rafaela Cuida em Natal/RN.

INFORMAÇÕES DO CONTATO:
- Nome: ${contact.name}
- Telefone: ${contact.phone}
- Gênero do bebê: ${contact.babyGender || 'Não informado'}

MENSAGEM RECEBIDA: "${message}"

CATEGORIA IDENTIFICADA: ${classification.intent.category}
CONFIANÇA: ${(classification.confidence * 100).toFixed(1)}%`;

    if (usedRAG && ragContext) {
      return `${basePrompt}

INFORMAÇÕES DA BASE DE CONHECIMENTO:
${ragContext}

INSTRUÇÕES:
1. Use as informações da base de conhecimento para responder de forma precisa
2. Seja calorosa e acolhedora como Rafaela
3. Mantenha o tom maternal e próximo
4. Use o emoji 🧡 como assinatura
5. Responda em até 60 palavras
6. Se a informação não estiver completa, oriente a entrar em contato com o gabinete

Responda de forma natural e útil:`;
    } else {
      return `${basePrompt}

INSTRUÇÕES:
1. Como não há informações específicas na base de conhecimento, seja acolhedora
2. Oriente sobre como obter a informação solicitada
3. Forneça contatos do gabinete se necessário
4. Mantenha o tom maternal e próximo da Rafaela
5. Use o emoji 🧡 como assinatura
6. Responda em até 60 palavras
7. Seja proativa em ajudar

Responda de forma natural e acolhedora:`;
    }
  }

  /**
   * Validar e ajustar resposta do WhatsApp
   */
  private validateWhatsAppResponse(response: string, contact: IContact, usedRAG: boolean): string {
    // Remover formatação markdown se houver
    let cleanResponse = response
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/```(.*?)```/gs, '$1')
      .trim();

    // Garantir que não seja muito longa
    if (cleanResponse.length > 300) {
      cleanResponse = cleanResponse.substring(0, 280) + '...';
    }

    // Adicionar assinatura se não tiver
    if (!cleanResponse.includes('🧡')) {
      cleanResponse += ' 🧡';
    }

    // Personalizar com nome se não tiver
    if (!cleanResponse.toLowerCase().includes(contact.name.toLowerCase().split(' ')[0])) {
      cleanResponse = `Oi ${contact.name.split(' ')[0]}! ${cleanResponse}`;
    }

    return cleanResponse;
  }

  // =========================================================
  // FUNÇÃO ORIGINAL MANTIDA PARA COMPATIBILIDADE
  // =========================================================
  public async generateResponse(contact: IContact, message: string, audioData?: AudioData) {
    console.log('⚠️ Usando método legado. Recomendado usar generateRAGEnhancedResponse()');
    return this.generateRAGEnhancedResponse(contact, message, audioData);
  }

  public async generateFollowUpMessage(contact: IContact) {
    if (!this.isInitialized) {
      console.warn('⚠️ Gemini AI não inicializado, usando template padrão');
      const messageType = Math.random() > 0.5 ? 'saudacao' : 'movimento_bebe';
      const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
      return validateResponseLength(response, 20, 50);
    }

    try {
      console.log('🤖 Gerando mensagem de follow-up personalizada com IA...');

      const babyGender = contact.babyGender === 'male' ? 'bebezinho' :
                        contact.babyGender === 'female' ? 'bebezinha' : 'bebê';

      const prompt = `Você é Rafaela, uma assistente virtual especializada em cuidados maternos.

Gere uma mensagem de acompanhamento carinhosa e personalizada para ${contact.name}.

Informações do contato:
- Nome: ${contact.name}
- Gênero do bebê: ${babyGender}

Diretrizes para a mensagem:
- Use um tom maternal, carinhoso e acolhedor
- Inclua o emoji 🧡 (marca da Rafaela)
- Use expressões como "nossa gente", "seguimos firmes", "minha fortaleza"
- Pergunte sobre o bem-estar da gestante e do bebê
- Seja proativa e demonstre interesse genuíno
- Mantenha entre 15-50 palavras
- Use linguagem natural e brasileira

Exemplos do estilo da Rafaela:
"Oi minha querida! Como você e o ${babyGender} estão hoje? Seguimos firmes nessa jornada! 🧡"
"Nossa gente, que saudade! Como anda tudo por aí? O ${babyGender} mexendo bastante? 🧡"

Gere uma mensagem única e personalizada:`;

      const result = await this.model.generateContent(prompt);
      const response = result.response.text().trim();

      console.log('✅ Mensagem de follow-up gerada com IA:', response.substring(0, 100) + '...');

      return validateResponseLength(response, 15, 50);

    } catch (error) {
      console.error('❌ Erro ao gerar follow-up com IA, usando template:', error);

      // Fallback para template
      const messageType = Math.random() > 0.5 ? 'saudacao' : 'movimento_bebe';
      const response = AdaptiveResponseSystem.applyTemplate(contact, messageType);
      return validateResponseLength(response, 20, 50);
    }
  }

  public async cleanupOldFiles(): Promise<void> {
    if (!this.ai) return;

    try {
      // Limpeza silenciosa - apenas log em caso de erro
      // A versão atual da biblioteca não suporta gerenciamento de arquivos
      // Para implementar, seria necessário atualizar para @google/generative-ai >= 0.12.0
    } catch (error) {
      console.error('❌ Erro na limpeza de arquivos:', error);
    }
  }
}

// Exportação singleton para evitar problemas de importação
export const geminiService = new GeminiAIService();