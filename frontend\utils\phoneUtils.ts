export function normalizeBrazilianPhoneNumber(phone: string, addDDI: boolean = false): string {
  // Remove todos os caracteres não numéricos
  const numbers = phone.replace(/\D/g, '');
  
  // Adiciona o DDI se necessário
  if (addDDI && !numbers.startsWith('55')) {
    return `55${numbers}`;
  }
  
  return numbers;
}

export function formatBrazilianPhoneNumberForDisplay(phone: string): string {
  const numbers = normalizeBrazilianPhoneNumber(phone);
  
  if (numbers.length === 11) {
    // Formato: (11) 98765-4321
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
  } else if (numbers.length === 10) {
    // Formato: (11) 8765-4321
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
  }
  
  return phone; // Retorna o número original se não estiver em um formato conhecido
} 