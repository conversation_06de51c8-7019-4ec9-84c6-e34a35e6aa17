{"name": "rafael<PERSON>-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:prod": "npm run test && npm run test:e2e", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@google/genai": "^1.3.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@types/react-router-dom": "^5.3.3", "@types/react-toastify": "^4.0.2", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.0.0", "@playwright/test": "^1.52.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^22.15.23", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^1.1.0", "@vitest/ui": "^1.1.0", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^1.1.0"}}