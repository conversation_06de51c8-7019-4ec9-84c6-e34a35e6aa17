import React, { useState, useEffect } from 'react';
import { 
  PhoneIcon, 
  PlayIcon, 
  StopIcon, 
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';

interface ConnectionStatus {
  isConnected: boolean;
  isAuthenticated: boolean;
  sessionStatus: string;
  phoneNumber?: string;
  deviceInfo?: any;
  lastActivity?: string;
}

interface QueueStats {
  pending: number;
  processing: number;
  byPriority: {
    high: number;
    normal: number;
    low: number;
  };
}

interface ProcessingStats {
  processed: number;
  failed: number;
  avgProcessingTime: number;
  successRate: number;
}

interface WhatsAppStatus {
  isRunning: boolean;
  connection: ConnectionStatus;
  stats: {
    audioQueue: {
      queue: QueueStats;
      processing: ProcessingStats;
    };
  };
  uptime: number;
}

export const WhatsAppMonitor: React.FC = () => {
  const [status, setStatus] = useState<WhatsAppStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [testPhone, setTestPhone] = useState('');

  // Buscar status
  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp-auto/status');
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.data);
      } else {
        console.error('Erro ao buscar status:', data.error);
      }
    } catch (error) {
      console.error('Erro na requisição:', error);
    }
  };

  // Auto refresh
  useEffect(() => {
    fetchStatus();
    
    if (autoRefresh) {
      const interval = setInterval(fetchStatus, 5000); // A cada 5 segundos
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  // Controlar serviço
  const controlService = async (action: 'start' | 'stop' | 'restart') => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/whatsapp-auto/${action}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        setTimeout(fetchStatus, 2000); // Atualizar após 2s
      } else {
        toast.error(data.error || `Erro ao ${action}`);
      }
    } catch (error) {
      toast.error(`Erro ao ${action} serviço`);
    } finally {
      setIsLoading(false);
    }
  };

  // Enviar mensagem de teste
  const sendTestMessage = async () => {
    if (!testPhone.trim()) {
      toast.error('Digite um número de telefone');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/whatsapp-auto/send-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phoneNumber: testPhone })
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Mensagem de teste enviada!');
        setTestPhone('');
      } else {
        toast.error(data.error || 'Erro ao enviar mensagem');
      }
    } catch (error) {
      toast.error('Erro na requisição');
    } finally {
      setIsLoading(false);
    }
  };

  // Testar processamento de áudio
  const testAudioProcessing = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/whatsapp-auto/test-audio', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast.success(`Teste iniciado! Job ID: ${data.data.jobId}`);
        setTimeout(fetchStatus, 1000);
      } else {
        toast.error(data.error || 'Erro no teste');
      }
    } catch (error) {
      toast.error('Erro na requisição');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (isConnected: boolean, isAuthenticated: boolean) => {
    if (isConnected && isAuthenticated) return 'text-green-600';
    if (isConnected) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (isConnected: boolean, isAuthenticated: boolean) => {
    if (isConnected && isAuthenticated) return <CheckCircleIcon className="w-5 h-5" />;
    if (isConnected) return <ExclamationTriangleIcon className="w-5 h-5" />;
    return <XCircleIcon className="w-5 h-5" />;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (!status) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <PhoneIcon className="w-8 h-8 text-green-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                WhatsApp Automático
              </h2>
              <p className="text-sm text-gray-500">
                Monitoramento e controle do sistema
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span>Auto-refresh</span>
            </label>
            
            <button
              onClick={fetchStatus}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
              title="Atualizar"
            >
              <ArrowPathIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Serviço */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Serviço</p>
                <p className={`text-lg font-semibold ${status.isRunning ? 'text-green-600' : 'text-red-600'}`}>
                  {status.isRunning ? 'Ativo' : 'Inativo'}
                </p>
              </div>
              {status.isRunning ? (
                <CheckCircleIcon className="w-8 h-8 text-green-600" />
              ) : (
                <XCircleIcon className="w-8 h-8 text-red-600" />
              )}
            </div>
            {status.isRunning && (
              <p className="text-xs text-gray-500 mt-1">
                Uptime: {formatUptime(status.uptime)}
              </p>
            )}
          </div>

          {/* Conexão */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Conexão</p>
                <p className={`text-lg font-semibold ${getStatusColor(status.connection.isConnected, status.connection.isAuthenticated)}`}>
                  {status.connection.isConnected && status.connection.isAuthenticated ? 'Conectado' : 
                   status.connection.isConnected ? 'Conectando' : 'Desconectado'}
                </p>
              </div>
              <div className={getStatusColor(status.connection.isConnected, status.connection.isAuthenticated)}>
                {getStatusIcon(status.connection.isConnected, status.connection.isAuthenticated)}
              </div>
            </div>
            {status.connection.phoneNumber && (
              <p className="text-xs text-gray-500 mt-1">
                📱 {status.connection.phoneNumber}
              </p>
            )}
          </div>

          {/* Fila de Áudio */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Fila de Áudio</p>
                <p className="text-lg font-semibold text-blue-600">
                  {status.stats.audioQueue.queue.pending} pendentes
                </p>
              </div>
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-blue-600" />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {status.stats.audioQueue.queue.processing} processando
            </p>
          </div>
        </div>
      </div>

      {/* Controles */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Controles</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Controle do Serviço */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Serviço</h4>
            <div className="flex space-x-2">
              <button
                onClick={() => controlService('start')}
                disabled={isLoading || status.isRunning}
                className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <PlayIcon className="w-4 h-4" />
                <span>Iniciar</span>
              </button>
              
              <button
                onClick={() => controlService('stop')}
                disabled={isLoading || !status.isRunning}
                className="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <StopIcon className="w-4 h-4" />
                <span>Parar</span>
              </button>
              
              <button
                onClick={() => controlService('restart')}
                disabled={isLoading}
                className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                <ArrowPathIcon className="w-4 h-4" />
                <span>Reiniciar</span>
              </button>
            </div>
          </div>

          {/* Testes */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Testes</h4>
            <div className="space-y-3">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={testPhone}
                  onChange={(e) => setTestPhone(e.target.value)}
                  placeholder="(11) 99999-9999"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={sendTestMessage}
                  disabled={isLoading || !status.connection.isConnected}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  Teste
                </button>
              </div>
              
              <button
                onClick={testAudioProcessing}
                disabled={isLoading}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                🎵 Testar Processamento de Áudio
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Estatísticas</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {status.stats.audioQueue.processing.processed}
            </p>
            <p className="text-sm text-gray-500">Processados</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">
              {status.stats.audioQueue.processing.failed}
            </p>
            <p className="text-sm text-gray-500">Falharam</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {status.stats.audioQueue.processing.successRate.toFixed(1)}%
            </p>
            <p className="text-sm text-gray-500">Taxa de Sucesso</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {status.stats.audioQueue.processing.avgProcessingTime.toFixed(0)}ms
            </p>
            <p className="text-sm text-gray-500">Tempo Médio</p>
          </div>
        </div>
      </div>

      {/* Status da Fila */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Fila de Processamento</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-800">Alta Prioridade</p>
                <p className="text-2xl font-bold text-red-600">
                  {status.stats.audioQueue.queue.byPriority.high}
                </p>
              </div>
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-800">Normal</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {status.stats.audioQueue.queue.byPriority.normal}
                </p>
              </div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            </div>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-800">Baixa Prioridade</p>
                <p className="text-2xl font-bold text-green-600">
                  {status.stats.audioQueue.queue.byPriority.low}
                </p>
              </div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
