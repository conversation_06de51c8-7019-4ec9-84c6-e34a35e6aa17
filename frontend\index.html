
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON></title>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1",
    "@headlessui/react": "https://esm.sh/@headlessui/react@^2.2.4",
    "@google/genai": "https://esm.sh/@google/genai@^1.1.0",
    "recharts": "https://esm.sh/recharts@^2.15.3",
    "date-fns/": "https://esm.sh/date-fns@^4.1.0/",
    "date-fns": "https://esm.sh/date-fns@^4.1.0"
  }
}
</script>
</head>
<body class="bg-neutral-light">
  <div id="root"></div>
  <script type="module" src="/src/index.tsx"></script>
</body>
</html>
