import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { apiService } from '../../services/apiService';
import { Message } from '../../schemas/validation';

// Mock do apiService
vi.mock('../../services/apiService', () => ({
  apiService: {
    getMessages: vi.fn(),
    sendMessage: vi.fn(),
  },
}));

// Mock do toast
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock dos dados de teste
const mockMessages: Message[] = [
  {
    id: 'msg-1',
    sender: 'user',
    text: 'Olá! Como você está se sentindo hoje?',
    timestamp: '2024-01-01T10:00:00.000Z',
    status: 'sent',
  },
  {
    id: 'msg-2',
    sender: 'gestante',
    text: 'Estou bem, obrigada! Apenas um pouco de enjoo pela manhã.',
    timestamp: '2024-01-01T10:05:00.000Z',
    status: 'read',
  },
  {
    id: 'msg-3',
    sender: 'ai',
    text: 'O enjoo matinal é comum no primeiro trimestre. Tente comer pequenas porções ao longo do dia.',
    timestamp: '2024-01-01T10:06:00.000Z',
    status: 'delivered',
  },
];

const pregnantWomanId = 'pregnant-1';

describe('CRUD de Mensagens', () => {
  const mockApiService = vi.mocked(apiService);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('READ - Buscar Mensagens', () => {
    it('deve buscar mensagens de uma gestante com sucesso', async () => {
      mockApiService.getMessages.mockResolvedValue(mockMessages);

      const result = await apiService.getMessages(pregnantWomanId);

      expect(mockApiService.getMessages).toHaveBeenCalledWith(pregnantWomanId);
      expect(result).toEqual(mockMessages);
      expect(result).toHaveLength(3);
    });

    it('deve retornar array vazio quando não há mensagens', async () => {
      mockApiService.getMessages.mockResolvedValue([]);

      const result = await apiService.getMessages(pregnantWomanId);

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
    });

    it('deve lançar erro quando falha ao buscar mensagens', async () => {
      const errorMessage = 'Erro de rede';
      mockApiService.getMessages.mockRejectedValue(new Error(errorMessage));

      await expect(apiService.getMessages(pregnantWomanId)).rejects.toThrow(errorMessage);
      expect(mockApiService.getMessages).toHaveBeenCalledWith(pregnantWomanId);
    });

    it('deve validar ID da gestante ao buscar mensagens', async () => {
      mockApiService.getMessages.mockRejectedValue(new Error('ID da gestante é obrigatório'));
      await expect(apiService.getMessages('')).rejects.toThrow('ID da gestante é obrigatório');
    });
  });

  describe('CREATE - Enviar Mensagem', () => {
    it('deve enviar mensagem com sucesso', async () => {
      const newMessage: Message = {
        id: 'msg-4',
        sender: 'user',
        text: 'Lembre-se de tomar suas vitaminas!',
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockResolvedValue(newMessage);

      const result = await apiService.sendMessage(pregnantWomanId, newMessage.text);

      expect(mockApiService.sendMessage).toHaveBeenCalledWith(pregnantWomanId, newMessage.text);
      expect(result).toEqual(newMessage);
      expect(result.sender).toBe('user');
      expect(result.text).toBe('Lembre-se de tomar suas vitaminas!');
    });

    it('deve validar texto da mensagem', async () => {
      // Texto vazio
      mockApiService.sendMessage.mockRejectedValue(new Error('Texto da mensagem é obrigatório'));
      await expect(apiService.sendMessage(pregnantWomanId, '')).rejects.toThrow('Texto da mensagem é obrigatório');

      // Texto muito longo (mais de 1000 caracteres)
      const longText = 'a'.repeat(1001);
      mockApiService.sendMessage.mockRejectedValue(new Error('Mensagem muito longa'));
      await expect(apiService.sendMessage(pregnantWomanId, longText)).rejects.toThrow('Mensagem muito longa');
    });

    it('deve validar ID da gestante ao enviar mensagem', async () => {
      mockApiService.sendMessage.mockRejectedValue(new Error('ID da gestante é obrigatório'));
      await expect(apiService.sendMessage('', 'Mensagem teste')).rejects.toThrow('ID da gestante é obrigatório');
    });

    it('deve sanitizar texto da mensagem', async () => {
      const maliciousText = '<script>alert("xss")</script>Mensagem normal';
      const sanitizedMessage: Message = {
        id: 'msg-5',
        sender: 'user',
        text: 'Mensagem normal', // Script removido
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockResolvedValue(sanitizedMessage);

      const result = await apiService.sendMessage(pregnantWomanId, maliciousText);

      expect(result.text).not.toContain('<script>');
      expect(result.text).toBe('Mensagem normal');
    });

    it('deve lidar com erro ao enviar mensagem', async () => {
      const errorMessage = 'Falha ao enviar mensagem';
      mockApiService.sendMessage.mockRejectedValue(new Error(errorMessage));

      await expect(
        apiService.sendMessage(pregnantWomanId, 'Mensagem teste')
      ).rejects.toThrow(errorMessage);
    });

    it('deve definir status correto para mensagem enviada', async () => {
      const message: Message = {
        id: 'msg-6',
        sender: 'user',
        text: 'Como está sua pressão arterial?',
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockResolvedValue(message);

      const result = await apiService.sendMessage(pregnantWomanId, message.text);

      expect(result.status).toBe('sent');
    });
  });

  describe('Validação de Dados de Mensagem', () => {
    it('deve validar estrutura da mensagem', async () => {
      const invalidMessage = {
        id: 'msg-invalid',
        sender: 'invalid-sender', // Sender inválido
        text: 'Mensagem teste',
        timestamp: 'invalid-date', // Data inválida
      };

      mockApiService.sendMessage.mockRejectedValue(new Error('Dados inválidos'));

      await expect(
        apiService.sendMessage(pregnantWomanId, 'Teste')
      ).rejects.toThrow('Dados inválidos');
    });

    it('deve validar timestamp da mensagem', async () => {
      const messageWithInvalidDate: any = {
        id: 'msg-7',
        sender: 'user',
        text: 'Mensagem teste',
        timestamp: 'not-a-date',
        status: 'sent',
      };

      mockApiService.sendMessage.mockRejectedValue(new Error('Timestamp inválido'));

      await expect(
        apiService.sendMessage(pregnantWomanId, 'Teste')
      ).rejects.toThrow('Timestamp inválido');
    });

    it('deve validar sender da mensagem', async () => {
      const messageWithInvalidSender: any = {
        id: 'msg-8',
        sender: 'robot', // Sender não permitido
        text: 'Mensagem teste',
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockRejectedValue(new Error('Sender inválido'));

      await expect(
        apiService.sendMessage(pregnantWomanId, 'Teste')
      ).rejects.toThrow('Sender inválido');
    });
  });

  describe('Integração com WhatsApp', () => {
    it('deve marcar mensagem como entregue quando WhatsApp confirma', async () => {
      const deliveredMessage: Message = {
        id: 'msg-9',
        sender: 'user',
        text: 'Mensagem via WhatsApp',
        timestamp: new Date().toISOString(),
        status: 'delivered',
      };

      mockApiService.sendMessage.mockResolvedValue(deliveredMessage);

      const result = await apiService.sendMessage(pregnantWomanId, deliveredMessage.text);

      expect(result.status).toBe('delivered');
    });

    it('deve marcar mensagem como falhada quando WhatsApp falha', async () => {
      const failedMessage: Message = {
        id: 'msg-10',
        sender: 'user',
        text: 'Mensagem que falhou',
        timestamp: new Date().toISOString(),
        status: 'failed',
      };

      mockApiService.sendMessage.mockResolvedValue(failedMessage);

      const result = await apiService.sendMessage(pregnantWomanId, failedMessage.text);

      expect(result.status).toBe('failed');
    });
  });

  describe('Mensagens da IA', () => {
    it('deve processar resposta da IA corretamente', async () => {
      const aiMessage: Message = {
        id: 'msg-ai-1',
        sender: 'ai',
        text: 'Com base nos seus sintomas, recomendo que você descanse mais e mantenha-se hidratada.',
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockResolvedValue(aiMessage);

      const result = await apiService.sendMessage(pregnantWomanId, 'Estou me sentindo cansada');

      expect(result.sender).toBe('ai');
      expect(result.text).toContain('recomendo');
    });

    it('deve validar resposta da IA', async () => {
      const invalidAiResponse: any = {
        id: 'msg-ai-invalid',
        sender: 'ai',
        text: '', // Texto vazio não é válido
        timestamp: new Date().toISOString(),
        status: 'sent',
      };

      mockApiService.sendMessage.mockRejectedValue(new Error('Resposta da IA inválida'));

      await expect(
        apiService.sendMessage(pregnantWomanId, 'Pergunta para IA')
      ).rejects.toThrow('Resposta da IA inválida');
    });
  });

  describe('Performance e Paginação', () => {
    it('deve lidar com grande volume de mensagens', async () => {
      const manyMessages = Array.from({ length: 100 }, (_, i) => ({
        id: `msg-${i}`,
        sender: 'user' as const,
        text: `Mensagem ${i}`,
        timestamp: new Date(Date.now() - i * 60000).toISOString(),
        status: 'sent' as const,
      }));

      mockApiService.getMessages.mockResolvedValue(manyMessages);

      const result = await apiService.getMessages(pregnantWomanId);

      expect(result).toHaveLength(100);
      expect(mockApiService.getMessages).toHaveBeenCalledTimes(1);
    });

    it('deve implementar timeout para operações longas', async () => {
      vi.useFakeTimers();

      const slowPromise = new Promise((resolve) => {
        setTimeout(() => resolve(mockMessages), 15000); // 15 segundos
      });

      mockApiService.getMessages.mockReturnValue(slowPromise as any);

      const resultPromise = apiService.getMessages(pregnantWomanId);

      // Avançar tempo
      vi.advanceTimersByTime(10000); // 10 segundos

      // Deve ainda estar pendente
      expect(resultPromise).toBeInstanceOf(Promise);

      vi.useRealTimers();
    });
  });
});
