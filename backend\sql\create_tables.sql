-- =====================================================
-- SCRIPT DE CRIAÇÃO DAS TABELAS PARA SUPABASE
-- Sistema de Gestão Materna - Rafaela Cuida
-- =====================================================

-- <PERSON><PERSON><PERSON><PERSON> necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TABELA: users (Usuários do sistema)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL CHECK (length(name) >= 2),
    email VARCHAR(255) UNIQUE NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    password VARCHAR(255) NOT NULL CHECK (length(password) >= 8),
    role VARCHAR(20) NOT NULL DEFAULT 'nurse' CHECK (role IN ('admin', 'nurse', 'doctor', 'coordinator')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMPTZ,
    permissions TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para users
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- =====================================================
-- TABELA: contacts (Gestantes)
-- =====================================================
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL CHECK (length(name) >= 2),
    phone VARCHAR(20) UNIQUE NOT NULL CHECK (phone ~ '^\+?[\d\s\-\(\)]+$'),
    baby_gender VARCHAR(10) DEFAULT 'unknown' CHECK (baby_gender IN ('male', 'female', 'unknown')),
    
    -- Campos de controle do sistema
    is_active BOOLEAN DEFAULT true,
    last_interaction TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID REFERENCES users(id),
    
    -- Campos para avaliação de interesse
    registration_status VARCHAR(20) DEFAULT 'unregistered' 
        CHECK (registration_status IN ('unregistered', 'evaluating', 'interested', 'registered', 'not_interested')),
    evaluation_start_date TIMESTAMPTZ,
    evaluation_messages INTEGER DEFAULT 0,
    interest_score INTEGER DEFAULT 0 CHECK (interest_score >= 0 AND interest_score <= 100),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para contacts
CREATE INDEX IF NOT EXISTS idx_contacts_name ON contacts(name);
CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone);
CREATE INDEX IF NOT EXISTS idx_contacts_is_active ON contacts(is_active);
CREATE INDEX IF NOT EXISTS idx_contacts_last_interaction ON contacts(last_interaction DESC);
CREATE INDEX IF NOT EXISTS idx_contacts_baby_gender ON contacts(baby_gender);
CREATE INDEX IF NOT EXISTS idx_contacts_registration_status ON contacts(registration_status);
CREATE INDEX IF NOT EXISTS idx_contacts_created_at ON contacts(created_at DESC);

-- =====================================================
-- TABELA: messages (Mensagens)
-- =====================================================
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
    content TEXT NOT NULL CHECK (length(content) <= 4000),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    type VARCHAR(20) DEFAULT 'text' 
        CHECK (type IN ('text', 'chat', 'image', 'audio', 'video', 'document', 'location', 'contact_card', 'sticker')),
    from_me BOOLEAN NOT NULL,
    message_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    
    -- Dados do WhatsApp (JSON)
    whatsapp_data JSONB,
    
    -- Metadados de mídia (JSON)
    media_data JSONB,
    
    -- Análise de sentimento e categorização
    sentiment VARCHAR(10) CHECK (sentiment IN ('positive', 'negative', 'neutral')),
    category VARCHAR(50),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para messages
CREATE INDEX IF NOT EXISTS idx_messages_contact_id ON messages(contact_id);
CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_messages_from_me ON messages(from_me);
CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);
CREATE INDEX IF NOT EXISTS idx_messages_sentiment ON messages(sentiment);
CREATE INDEX IF NOT EXISTS idx_messages_category ON messages(category);
CREATE INDEX IF NOT EXISTS idx_messages_message_id ON messages(message_id);

-- =====================================================
-- TABELA: message_templates (Templates de mensagens)
-- =====================================================
CREATE TABLE IF NOT EXISTS message_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(30) NOT NULL 
        CHECK (category IN ('routine_checkup', 'milestone', 'educational', 'emotional_support', 'reminder', 'emergency_follow_up')),
    
    -- Conteúdo do template
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL CHECK (length(content) <= 2000),
    variables TEXT[] DEFAULT '{}',
    
    -- Condições de uso
    gestational_week_min INTEGER CHECK (gestational_week_min >= 0 AND gestational_week_min <= 45),
    gestational_week_max INTEGER CHECK (gestational_week_max >= 0 AND gestational_week_max <= 45),
    pregnancy_stage VARCHAR(20) CHECK (pregnancy_stage IN ('first_trimester', 'second_trimester', 'third_trimester', 'postpartum')),
    is_high_risk BOOLEAN,
    
    -- Configurações
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    requires_response BOOLEAN DEFAULT false,
    follow_up_days INTEGER CHECK (follow_up_days > 0),
    
    -- Controle
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES users(id),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para message_templates
CREATE INDEX IF NOT EXISTS idx_message_templates_category ON message_templates(category);
CREATE INDEX IF NOT EXISTS idx_message_templates_is_active ON message_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_message_templates_priority ON message_templates(priority);

-- =====================================================
-- TRIGGERS PARA UPDATED_AT
-- =====================================================

-- Função para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para cada tabela
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_message_templates_updated_at BEFORE UPDATE ON message_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- POLÍTICAS RLS (Row Level Security) - Opcional
-- =====================================================

-- Habilitar RLS nas tabelas (comentado por enquanto)
-- ALTER TABLE users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE message_templates ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- TABELA: api_keys (Gerenciamento de API Keys)
-- =====================================================
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service VARCHAR(50) NOT NULL CHECK (service IN ('gemini', 'whatsapp', 'supabase', 'other')),
    key_name VARCHAR(100) NOT NULL,
    encrypted_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    rotation_interval_days INTEGER DEFAULT 30 CHECK (rotation_interval_days > 0),
    last_rotated TIMESTAMPTZ,
    rotation_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES users(id),
    notes TEXT,

    CONSTRAINT unique_active_service_key UNIQUE (service, key_name, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Índices para api_keys
CREATE INDEX IF NOT EXISTS idx_api_keys_service_active ON api_keys(service, is_active);
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_keys_rotation ON api_keys(service, rotation_interval_days, created_at);

-- =====================================================
-- DADOS INICIAIS (SEED)
-- =====================================================

-- Usuário admin padrão (senha: admin123)
INSERT INTO users (name, email, password, role, permissions) 
VALUES (
    'Administrador',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBw2fyuPifeS2aOEFc6LhAVqDgUBn9n3A.H2uab33vgibS', -- admin123
    'admin',
    ARRAY['read:all', 'write:all', 'delete:all', 'manage:users', 'manage:system']
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- COMENTÁRIOS DAS TABELAS
-- =====================================================

COMMENT ON TABLE users IS 'Usuários do sistema de gestão materna';
COMMENT ON TABLE contacts IS 'Gestantes cadastradas no sistema';
COMMENT ON TABLE messages IS 'Mensagens trocadas via WhatsApp';
COMMENT ON TABLE message_templates IS 'Templates de mensagens para automação';

COMMENT ON COLUMN contacts.baby_gender IS 'Gênero do bebê: male, female ou unknown';
COMMENT ON COLUMN contacts.registration_status IS 'Status do cadastro da gestante';
COMMENT ON COLUMN contacts.interest_score IS 'Pontuação de interesse (0-100)';
COMMENT ON COLUMN messages.from_me IS 'true se a mensagem foi enviada pelo sistema';
COMMENT ON COLUMN messages.whatsapp_data IS 'Dados específicos do WhatsApp em formato JSON';
COMMENT ON COLUMN messages.media_data IS 'Metadados de arquivos de mídia em formato JSON';
