/**
 * Cache Semântico para Sistema RAG
 * Armazena e recupera respostas baseadas em similaridade semântica
 */

import { embeddingService } from './embeddingService';
import { RAGResponse } from './ragService';
import * as fs from 'fs';
import * as path from 'path';

export interface CacheEntry {
  id: string;
  query: string;
  queryEmbedding: number[];
  response: RAGResponse;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  totalEntries: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  averageResponseTime: number;
  cacheSize: string;
}

export class SemanticCache {
  private cache: Map<string, CacheEntry> = new Map();
  private cacheFile: string;
  private maxEntries: number = 1000;
  private similarityThreshold: number = 0.85;
  private maxAge: number = 7 * 24 * 60 * 60 * 1000; // 7 dias
  
  // Estatísticas
  private stats = {
    hits: 0,
    misses: 0,
    totalResponseTime: 0,
    responseCount: 0
  };

  constructor() {
    this.cacheFile = path.join(process.cwd(), 'data', 'semantic_cache.json');
    this.ensureDataDir();
    this.loadCache();
    
    // Limpeza automática a cada hora
    setInterval(() => this.cleanup(), 60 * 60 * 1000);
  }

  /**
   * Buscar resposta no cache
   */
  async get(query: string): Promise<RAGResponse | null> {
    try {
      const startTime = Date.now();
      
      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);
      
      // Buscar entrada similar
      let bestMatch: CacheEntry | null = null;
      let bestSimilarity = 0;

      for (const entry of this.cache.values()) {
        const similarity = embeddingService.calculateCosineSimilarity(
          queryEmbedding.embedding,
          entry.queryEmbedding
        );

        if (similarity > bestSimilarity && similarity >= this.similarityThreshold) {
          bestSimilarity = similarity;
          bestMatch = entry;
        }
      }

      if (bestMatch) {
        // Atualizar estatísticas da entrada
        bestMatch.accessCount++;
        bestMatch.lastAccessed = Date.now();
        
        // Atualizar estatísticas globais
        this.stats.hits++;
        const responseTime = Date.now() - startTime;
        this.stats.totalResponseTime += responseTime;
        this.stats.responseCount++;

        console.log(`🎯 Cache hit! Similaridade: ${(bestSimilarity * 100).toFixed(1)}% (${responseTime}ms)`);
        
        return {
          ...bestMatch.response,
          processingTime: responseTime,
          metadata: {
            ...bestMatch.response.metadata,
            fromCache: true,
            cacheSimilarity: bestSimilarity
          }
        };
      }

      this.stats.misses++;
      return null;

    } catch (error) {
      console.error('❌ Erro ao buscar no cache semântico:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Armazenar resposta no cache
   */
  async set(query: string, response: RAGResponse): Promise<void> {
    try {
      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);
      
      const entry: CacheEntry = {
        id: `cache_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query,
        queryEmbedding: queryEmbedding.embedding,
        response: {
          ...response,
          metadata: {
            ...response.metadata,
            fromCache: false
          }
        },
        timestamp: Date.now(),
        accessCount: 0,
        lastAccessed: Date.now()
      };

      this.cache.set(entry.id, entry);
      
      // Limpar cache se exceder limite
      if (this.cache.size > this.maxEntries) {
        await this.evictOldEntries();
      }

      console.log(`💾 Resposta armazenada no cache semântico (${this.cache.size}/${this.maxEntries})`);
      
      // Salvar periodicamente
      if (this.cache.size % 10 === 0) {
        await this.saveCache();
      }

    } catch (error) {
      console.error('❌ Erro ao armazenar no cache semântico:', error);
    }
  }

  /**
   * Remover entradas antigas
   */
  private async evictOldEntries(): Promise<void> {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());
    
    // Ordenar por score (combinação de idade, acesso e frequência)
    entries.sort(([, a], [, b]) => {
      const scoreA = this.calculateEvictionScore(a, now);
      const scoreB = this.calculateEvictionScore(b, now);
      return scoreA - scoreB; // Menor score = primeiro a ser removido
    });

    // Remover 20% das entradas menos úteis
    const toRemove = Math.floor(this.maxEntries * 0.2);
    for (let i = 0; i < toRemove && entries.length > 0; i++) {
      const [id] = entries[i];
      this.cache.delete(id);
    }

    console.log(`🧹 Cache limpo: ${toRemove} entradas removidas`);
  }

  /**
   * Calcular score para remoção (menor = remove primeiro)
   */
  private calculateEvictionScore(entry: CacheEntry, now: number): number {
    const age = now - entry.timestamp;
    const timeSinceAccess = now - entry.lastAccessed;
    const accessFrequency = entry.accessCount;
    
    // Score baseado em idade, acesso recente e frequência
    const ageScore = Math.min(age / this.maxAge, 1) * 100;
    const accessScore = Math.min(timeSinceAccess / (24 * 60 * 60 * 1000), 1) * 50;
    const frequencyScore = Math.max(0, 50 - (accessFrequency * 5));
    
    return ageScore + accessScore + frequencyScore;
  }

  /**
   * Limpeza automática de entradas expiradas
   */
  private cleanup(): void {
    const now = Date.now();
    let removed = 0;

    for (const [id, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.maxAge) {
        this.cache.delete(id);
        removed++;
      }
    }

    if (removed > 0) {
      console.log(`🧹 Limpeza automática: ${removed} entradas expiradas removidas`);
    }
  }

  /**
   * Obter estatísticas do cache
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    const avgResponseTime = this.stats.responseCount > 0 
      ? this.stats.totalResponseTime / this.stats.responseCount 
      : 0;

    return {
      totalEntries: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      averageResponseTime: Math.round(avgResponseTime),
      cacheSize: this.formatBytes(this.estimateCacheSize())
    };
  }

  /**
   * Limpar todo o cache
   */
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, totalResponseTime: 0, responseCount: 0 };
    console.log('🧹 Cache semântico limpo completamente');
  }

  /**
   * Salvar cache em arquivo
   */
  private async saveCache(): Promise<void> {
    try {
      const cacheData = {
        entries: Array.from(this.cache.entries()),
        stats: this.stats,
        timestamp: Date.now()
      };

      await fs.promises.writeFile(this.cacheFile, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.error('❌ Erro ao salvar cache:', error);
    }
  }

  /**
   * Carregar cache do arquivo
   */
  private loadCache(): void {
    try {
      if (fs.existsSync(this.cacheFile)) {
        const data = JSON.parse(fs.readFileSync(this.cacheFile, 'utf8'));
        
        if (data.entries && Array.isArray(data.entries)) {
          this.cache = new Map(data.entries);
          console.log(`📚 Cache carregado: ${this.cache.size} entradas`);
        }
        
        if (data.stats) {
          this.stats = { ...this.stats, ...data.stats };
        }
      }
    } catch (error) {
      console.warn('⚠️ Erro ao carregar cache, iniciando limpo:', error);
      this.cache.clear();
    }
  }

  /**
   * Garantir que o diretório existe
   */
  private ensureDataDir(): void {
    const dataDir = path.dirname(this.cacheFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
  }

  /**
   * Estimar tamanho do cache em bytes
   */
  private estimateCacheSize(): number {
    return JSON.stringify(Array.from(this.cache.entries())).length;
  }

  /**
   * Formatar bytes em formato legível
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Instância singleton
export const semanticCache = new SemanticCache();
