/**
 * Sistema de Indexação Incremental
 * Atualiza apenas documentos novos ou modificados para melhor performance
 */

import { chromaVectorDB } from './chromaVectorDB';
import { hybridEmbeddingService } from './hybridEmbeddings';
import { supabase } from '../config/supabase';
import { documentProcessor } from './documentProcessor';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

export interface DocumentVersion {
  id: string;
  filePath: string;
  hash: string;
  lastModified: string;
  version: number;
  status: 'new' | 'modified' | 'deleted' | 'unchanged';
  chunks: number;
  processingTime?: number;
}

export interface IndexingJob {
  id: string;
  type: 'full' | 'incremental';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  documentsProcessed: number;
  documentsAdded: number;
  documentsUpdated: number;
  documentsDeleted: number;
  errors: string[];
  metadata: Record<string, any>;
}

export class IncrementalIndexingService {
  private indexingHistory: Map<string, DocumentVersion> = new Map();
  private currentJob: IndexingJob | null = null;
  private watchedDirectories: string[] = [];
  private isWatching: boolean = false;

  constructor() {
    this.loadIndexingHistory();
    this.setupDirectoryWatching();
  }

  /**
   * Executar indexação incremental
   */
  async runIncrementalIndexing(): Promise<IndexingJob> {
    if (this.currentJob && this.currentJob.status === 'running') {
      throw new Error('Indexação já está em execução');
    }

    const job: IndexingJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'incremental',
      status: 'running',
      startTime: new Date().toISOString(),
      documentsProcessed: 0,
      documentsAdded: 0,
      documentsUpdated: 0,
      documentsDeleted: 0,
      errors: [],
      metadata: {}
    };

    this.currentJob = job;

    try {
      console.log('🔄 Iniciando indexação incremental...');

      // 1. Detectar mudanças nos arquivos
      const changes = await this.detectFileChanges();
      console.log(`📊 Detectadas ${changes.length} mudanças`);

      // 2. Processar cada mudança
      for (const change of changes) {
        try {
          await this.processDocumentChange(change);
          job.documentsProcessed++;

          switch (change.status) {
            case 'new':
            case 'modified':
              job.documentsAdded++;
              break;
            case 'deleted':
              job.documentsDeleted++;
              break;
          }

        } catch (error) {
          console.error(`❌ Erro ao processar ${change.filePath}:`, error);
          job.errors.push(`${change.filePath}: ${error}`);
        }
      }

      // 3. Limpar documentos órfãos
      await this.cleanupOrphanedDocuments();

      // 4. Atualizar histórico
      await this.saveIndexingHistory();

      job.status = 'completed';
      job.endTime = new Date().toISOString();

      console.log(`✅ Indexação incremental concluída: ${job.documentsProcessed} documentos processados`);

    } catch (error) {
      console.error('❌ Erro na indexação incremental:', error);
      job.status = 'failed';
      job.errors.push(`Erro geral: ${error}`);
    }

    this.currentJob = null;
    await this.saveJobHistory(job);

    return job;
  }

  /**
   * Detectar mudanças nos arquivos
   */
  private async detectFileChanges(): Promise<DocumentVersion[]> {
    const changes: DocumentVersion[] = [];
    const documentsDir = path.join(process.cwd(), 'data', 'documents');

    if (!fs.existsSync(documentsDir)) {
      console.log('📁 Diretório de documentos não existe');
      return changes;
    }

    const files = this.getAllFiles(documentsDir);

    for (const filePath of files) {
      try {
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath);
        const hash = crypto.createHash('md5').update(content).digest('hex');
        const lastModified = stats.mtime.toISOString();

        const relativePath = path.relative(documentsDir, filePath);
        const existingVersion = this.indexingHistory.get(relativePath);

        let status: DocumentVersion['status'] = 'unchanged';

        if (!existingVersion) {
          status = 'new';
        } else if (existingVersion.hash !== hash) {
          status = 'modified';
        }

        if (status !== 'unchanged') {
          changes.push({
            id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            filePath: relativePath,
            hash,
            lastModified,
            version: (existingVersion?.version || 0) + 1,
            status,
            chunks: 0
          });
        }

      } catch (error) {
        console.error(`❌ Erro ao analisar arquivo ${filePath}:`, error);
      }
    }

    // Detectar arquivos deletados
    for (const [filePath, version] of this.indexingHistory.entries()) {
      const fullPath = path.join(documentsDir, filePath);
      if (!fs.existsSync(fullPath)) {
        changes.push({
          ...version,
          status: 'deleted'
        });
      }
    }

    return changes;
  }

  /**
   * Processar mudança em documento
   */
  private async processDocumentChange(change: DocumentVersion): Promise<void> {
    console.log(`🔄 Processando ${change.status}: ${change.filePath}`);

    switch (change.status) {
      case 'new':
      case 'modified':
        await this.indexDocument(change);
        break;
      case 'deleted':
        await this.removeDocument(change);
        break;
    }

    // Atualizar histórico
    if (change.status === 'deleted') {
      this.indexingHistory.delete(change.filePath);
    } else {
      this.indexingHistory.set(change.filePath, change);
    }
  }

  /**
   * Indexar documento novo ou modificado
   */
  private async indexDocument(change: DocumentVersion): Promise<void> {
    const startTime = Date.now();
    const documentsDir = path.join(process.cwd(), 'data', 'documents');
    const fullPath = path.join(documentsDir, change.filePath);

    try {
      // 1. Processar documento
      const processedDoc = await documentProcessor.processFile(fullPath);

      // 2. Gerar embeddings híbridos para cada chunk
      const chromaDocuments = [];

      for (let i = 0; i < processedDoc.chunks.length; i++) {
        const chunk = processedDoc.chunks[i];
        
        // Gerar embedding híbrido baseado no tipo de documento
        const embeddingResult = await hybridEmbeddingService.generateDomainSpecificEmbedding(
          chunk.content,
          processedDoc.metadata.documentType
        );

        chromaDocuments.push({
          id: `${change.id}_chunk_${i}`,
          content: chunk.content,
          metadata: {
            ...processedDoc.metadata,
            chunkIndex: i,
            totalChunks: processedDoc.chunks.length,
            sourceFile: change.filePath,
            version: change.version,
            lastModified: change.lastModified
          },
          embedding: embeddingResult.combined
        });
      }

      // 3. Remover versão anterior se existir
      if (change.status === 'modified') {
        await this.removeDocumentChunks(change.filePath);
      }

      // 4. Adicionar ao ChromaDB
      await chromaVectorDB.addDocuments(chromaDocuments);

      change.chunks = chromaDocuments.length;
      change.processingTime = Date.now() - startTime;

      console.log(`✅ Documento indexado: ${change.chunks} chunks em ${change.processingTime}ms`);

    } catch (error) {
      console.error(`❌ Erro ao indexar documento ${change.filePath}:`, error);
      throw error;
    }
  }

  /**
   * Remover documento deletado
   */
  private async removeDocument(change: DocumentVersion): Promise<void> {
    try {
      await this.removeDocumentChunks(change.filePath);
      console.log(`🗑️ Documento removido: ${change.filePath}`);
    } catch (error) {
      console.error(`❌ Erro ao remover documento ${change.filePath}:`, error);
      throw error;
    }
  }

  /**
   * Remover chunks de um documento
   */
  private async removeDocumentChunks(filePath: string): Promise<void> {
    // Em uma implementação real, ChromaDB permitiria buscar por metadata
    // Por enquanto, usamos uma abordagem simplificada
    console.log(`🧹 Removendo chunks do documento: ${filePath}`);
    
    // Implementação simplificada - em produção seria mais eficiente
    // await chromaVectorDB.deleteByMetadata({ sourceFile: filePath });
  }

  /**
   * Limpar documentos órfãos
   */
  private async cleanupOrphanedDocuments(): Promise<void> {
    console.log('🧹 Limpando documentos órfãos...');
    
    // Implementação simplificada
    // Em produção, verificaria quais documentos no ChromaDB não têm arquivo correspondente
  }

  /**
   * Configurar monitoramento de diretórios
   */
  private setupDirectoryWatching(): void {
    const documentsDir = path.join(process.cwd(), 'data', 'documents');
    this.watchedDirectories = [documentsDir];

    // Em produção, usaria fs.watch ou chokidar para monitoramento em tempo real
    console.log('👁️ Monitoramento de diretórios configurado');
  }

  /**
   * Iniciar monitoramento automático
   */
  startAutoIndexing(intervalMinutes: number = 30): void {
    if (this.isWatching) {
      console.log('⚠️ Monitoramento automático já está ativo');
      return;
    }

    this.isWatching = true;
    
    const interval = setInterval(async () => {
      try {
        console.log('🔄 Executando indexação automática...');
        await this.runIncrementalIndexing();
      } catch (error) {
        console.error('❌ Erro na indexação automática:', error);
      }
    }, intervalMinutes * 60 * 1000);

    console.log(`⏰ Indexação automática iniciada (intervalo: ${intervalMinutes} minutos)`);
  }

  /**
   * Parar monitoramento automático
   */
  stopAutoIndexing(): void {
    this.isWatching = false;
    console.log('⏹️ Monitoramento automático parado');
  }

  /**
   * Obter estatísticas de indexação
   */
  async getIndexingStats(): Promise<{
    totalDocuments: number;
    lastIndexing: string | null;
    pendingChanges: number;
    indexingHistory: DocumentVersion[];
    recentJobs: IndexingJob[];
  }> {
    const changes = await this.detectFileChanges();
    const pendingChanges = changes.filter(c => c.status !== 'unchanged').length;

    // Buscar jobs recentes
    const { data: recentJobs } = await supabase
      .from('indexing_jobs')
      .select('*')
      .order('start_time', { ascending: false })
      .limit(10) || { data: [] };

    return {
      totalDocuments: this.indexingHistory.size,
      lastIndexing: this.getLastIndexingTime(),
      pendingChanges,
      indexingHistory: Array.from(this.indexingHistory.values()),
      recentJobs: recentJobs || []
    };
  }

  /**
   * Métodos auxiliares
   */
  private getAllFiles(dir: string): string[] {
    const files: string[] = [];
    
    if (!fs.existsSync(dir)) return files;

    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        files.push(...this.getAllFiles(fullPath));
      } else if (this.isSupportedFile(fullPath)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  private isSupportedFile(filePath: string): boolean {
    const supportedExtensions = ['.txt', '.md', '.pdf', '.docx', '.json'];
    const ext = path.extname(filePath).toLowerCase();
    return supportedExtensions.includes(ext);
  }

  private async loadIndexingHistory(): Promise<void> {
    try {
      const historyFile = path.join(process.cwd(), 'data', 'indexing_history.json');
      
      if (fs.existsSync(historyFile)) {
        const data = JSON.parse(fs.readFileSync(historyFile, 'utf8'));
        this.indexingHistory = new Map(Object.entries(data));
        console.log(`📚 Histórico carregado: ${this.indexingHistory.size} documentos`);
      }
    } catch (error) {
      console.warn('⚠️ Erro ao carregar histórico de indexação:', error);
    }
  }

  private async saveIndexingHistory(): Promise<void> {
    try {
      const historyFile = path.join(process.cwd(), 'data', 'indexing_history.json');
      const dataDir = path.dirname(historyFile);
      
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      const data = Object.fromEntries(this.indexingHistory.entries());
      fs.writeFileSync(historyFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('❌ Erro ao salvar histórico:', error);
    }
  }

  private async saveJobHistory(job: IndexingJob): Promise<void> {
    try {
      await supabase
        .from('indexing_jobs')
        .insert([{
          job_id: job.id,
          type: job.type,
          status: job.status,
          start_time: job.startTime,
          end_time: job.endTime,
          documents_processed: job.documentsProcessed,
          documents_added: job.documentsAdded,
          documents_updated: job.documentsUpdated,
          documents_deleted: job.documentsDeleted,
          errors: job.errors,
          metadata: job.metadata
        }]);
    } catch (error) {
      console.error('❌ Erro ao salvar histórico do job:', error);
    }
  }

  private getLastIndexingTime(): string | null {
    let lastTime: string | null = null;
    
    for (const version of this.indexingHistory.values()) {
      if (!lastTime || version.lastModified > lastTime) {
        lastTime = version.lastModified;
      }
    }
    
    return lastTime;
  }
}

// Instância singleton
export const incrementalIndexingService = new IncrementalIndexingService();
