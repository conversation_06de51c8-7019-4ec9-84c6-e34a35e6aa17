/**
 * Script de Inicialização do Sistema RAG
 * Configura ChromaDB e popula com dados iniciais
 */

import { simpleVectorDatabase } from '../services/simpleVectorDB';
import { documentProcessor } from '../services/documentProcessor';
import { ragService } from '../services/ragService';

// Dados iniciais para popular a base de conhecimento
const initialDocuments = [
  {
    title: 'FAQ - Como solicitar poda de árvore',
    content: `Para solicitar poda de árvore em Natal, o cidadão deve:

1. Entrar em contato com a Secretaria Municipal de Meio Ambiente e Urbanismo (SEMURB)
2. Telefone: (84) 3232-8000
3. Ou através do aplicativo Natal Digital
4. Informar o endereço completo onde está a árvore
5. Descrever o problema (galhos sobre fiação, obstruindo passagem, etc.)
6. O prazo para atendimento é de até 30 dias úteis

IMPORTANTE: É proibido podar árvores por conta própria. A poda irregular pode gerar multa de R$ 500 a R$ 5.000.`,
    documentType: 'faq' as const,
    category: 'infrastructure' as const,
    source: 'gabinete_inicial',
    tags: ['poda', 'árvore', 'semurb', 'meio ambiente']
  },
  {
    title: 'Serviços - Solicitação de tapa-buraco',
    content: `Para solicitar reparo de buraco na via pública:

1. Acesse o aplicativo Natal Digital ou ligue 156
2. Selecione "Infraestrutura" > "Tapa-buraco"
3. Informe o endereço exato
4. Se possível, anexe foto do problema
5. Acompanhe o protocolo gerado

PRAZO: Até 15 dias úteis para vias principais, 30 dias para vias secundárias.

RESPONSÁVEL: Secretaria Municipal de Infraestrutura (SEINFRA)
Telefone: (84) 3232-7000`,
    documentType: 'service' as const,
    category: 'infrastructure' as const,
    source: 'gabinete_inicial',
    tags: ['buraco', 'asfalto', 'infraestrutura', 'seinfra']
  },
  {
    title: 'Legislação - Lei de Acessibilidade Municipal',
    content: `Lei Municipal nº 123/2023 - Acessibilidade em Estabelecimentos Públicos

RESUMO: Estabelece normas para garantir acessibilidade em todos os prédios públicos municipais.

PRINCIPAIS PONTOS:
- Rampas de acesso obrigatórias
- Banheiros adaptados
- Sinalização em braile
- Vagas preferenciais de estacionamento
- Prazo de adequação: 24 meses

FISCALIZAÇÃO: Secretaria de Acessibilidade e Inclusão
Denúncias: (84) 3232-9000 ou <EMAIL>

MULTAS: R$ 1.000 a R$ 10.000 para estabelecimentos em desacordo.`,
    documentType: 'legislation' as const,
    category: 'legislation' as const,
    source: 'gabinete_inicial',
    tags: ['acessibilidade', 'lei', 'inclusão', 'deficiência']
  },
  {
    title: 'Projeto - Programa Mães de Natal',
    content: `Projeto de Lei nº 45/2024 - Programa Mães de Natal
Autora: Vereadora Rafaela

OBJETIVO: Criar programa municipal de apoio às mães em situação de vulnerabilidade social.

BENEFÍCIOS PROPOSTOS:
- Auxílio financeiro mensal de R$ 300
- Acompanhamento pré-natal gratuito
- Curso de capacitação profissional
- Creche prioritária para filhos
- Apoio psicológico

CRITÉRIOS:
- Renda familiar até 2 salários mínimos
- Residir em Natal há pelo menos 2 anos
- Estar grávida ou ter filhos até 3 anos

STATUS: Em tramitação na Câmara Municipal
PREVISÃO: Votação em dezembro/2024`,
    documentType: 'project' as const,
    category: 'health' as const,
    source: 'gabinete_inicial',
    tags: ['mães', 'maternidade', 'auxílio', 'vulnerabilidade', 'rafaela']
  },
  {
    title: 'Agenda - Audiências Públicas 2024',
    content: `Calendário de Audiências Públicas - 2º Semestre 2024

DEZEMBRO:
- 05/12 - 19h - Orçamento Municipal 2025
  Local: Plenário da Câmara Municipal
  
- 12/12 - 18h - Mobilidade Urbana
  Local: Centro de Convenções
  
- 19/12 - 19h - Saúde Pública
  Local: Auditório da UFRN

COMO PARTICIPAR:
- Inscrições: camara.natal.rn.gov.br
- Presencial ou online (YouTube da Câmara)
- Perguntas podem ser enviadas antecipadamente

CONTATO: (84) 3232-6000
E-mail: <EMAIL>`,
    documentType: 'agenda' as const,
    category: 'transparency' as const,
    source: 'gabinete_inicial',
    tags: ['audiência', 'participação', 'orçamento', 'transparência']
  }
];

/**
 * Função principal de inicialização
 */
async function initializeRAG(): Promise<void> {
  try {
    console.log('🚀 Iniciando configuração do Sistema RAG...');

    // 1. Inicializar Vector Database
    console.log('📊 Inicializando Simple Vector Database...');
    await simpleVectorDatabase.initialize();

    // 2. Verificar se já existem documentos
    const stats = await simpleVectorDatabase.getStats();
    console.log(`📋 Documentos existentes: ${stats.totalDocuments}`);

    if (stats.totalDocuments === 0) {
      console.log('📝 Populando base de conhecimento inicial...');

      // 3. Adicionar documentos iniciais
      for (let i = 0; i < initialDocuments.length; i++) {
        const doc = initialDocuments[i];
        console.log(`📄 Adicionando: ${doc.title}`);

        try {
          await simpleVectorDatabase.addDocument(doc.content, {
            title: doc.title,
            content: doc.content,
            documentType: doc.documentType,
            category: doc.category,
            source: doc.source,
            tags: doc.tags
          });

          console.log(`✅ Documento ${i + 1}/${initialDocuments.length} adicionado`);
        } catch (error) {
          console.error(`❌ Erro ao adicionar documento ${doc.title}:`, error);
        }

        // Delay entre documentos
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } else {
      console.log('📚 Base de conhecimento já possui documentos');
    }

    // 4. Testar sistema RAG
    console.log('🧪 Testando sistema RAG...');
    
    const testQueries = [
      'Como solicitar poda de árvore?',
      'Quais são os projetos da vereadora Rafaela?',
      'Quando será a próxima audiência pública?'
    ];

    for (const query of testQueries) {
      console.log(`🔍 Testando: "${query}"`);
      
      try {
        const response = await ragService.processQuery({ question: query });
        console.log(`✅ Resposta gerada (confiança: ${(response.confidence * 100).toFixed(1)}%)`);
        console.log(`📊 Fontes encontradas: ${response.sources.length}`);
      } catch (error) {
        console.error(`❌ Erro no teste: ${error}`);
      }
    }

    // 5. Exibir estatísticas finais
    const finalStats = await simpleVectorDatabase.getStats();
    console.log('\n📊 ESTATÍSTICAS FINAIS:');
    console.log(`Total de documentos: ${finalStats.totalDocuments}`);
    console.log('Por tipo:', finalStats.documentsByType);
    console.log('Por categoria:', finalStats.documentsByCategory);

    console.log('\n✅ Sistema RAG inicializado com sucesso!');
    console.log('🎯 Pronto para receber consultas do atendente geral do gabinete');

  } catch (error) {
    console.error('❌ Erro na inicialização do RAG:', error);
    throw error;
  }
}

/**
 * Executar se chamado diretamente
 */
if (require.main === module) {
  initializeRAG()
    .then(() => {
      console.log('🎉 Inicialização concluída!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na inicialização:', error);
      process.exit(1);
    });
}

export { initializeRAG };
