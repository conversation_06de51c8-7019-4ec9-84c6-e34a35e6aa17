import { IContact, Contact } from '../models/Contact';
import { GeminiAIService } from './gemini';
import { systemEventEmitter } from './eventEmitter';

/**
 * Serviço para avaliar se pessoa não cadastrada está falando sobre o projeto gestantes
 */
export class InterestEvaluatorService {
  private geminiService: GeminiModule.GeminiAIService;

  constructor(geminiService: GeminiModule.GeminiAIService) {
    this.geminiService = geminiService;
  }

  /**
   * Analisar se a mensagem está relacionada ao projeto gestantes
   */
  public async analyzeIfRelatedToProject(message: string): Promise<{
    isRelated: boolean;
    shouldOfferRegistration: boolean;
    isGreeting: boolean;
    messageType: 'greeting' | 'project_related' | 'unrelated' | 'unclear';
    response?: string;
    confidence: number;
  }> {
    try {
      const prompt = `
        Analise se esta mensagem está relacionada ao projeto de acompanhamento de gestantes.

        Mensagem: "${message}"

        CRITÉRIOS PARA SER RELACIONADO:
        - Menciona gravidez, gestação, bebê, pré-natal
        - Fala sobre saúde maternal
        - Pede ajuda com questões de gravidez
        - Demonstra interesse em acompanhamento
        - Pergunta sobre serviços para gestantes

        CRITÉRIOS PARA SAUDAÇÕES SIMPLES:
        - Apenas "oi", "olá", "bom dia", "boa tarde", "boa noite"
        - Saudações genéricas sem contexto específico
        - Mensagens muito curtas e vagas
        - Cumprimentos básicos sem menção ao projeto

        CRITÉRIOS PARA NÃO SER RELACIONADO:
        - Mensagens genéricas (oi, olá, tudo bem)
        - Assuntos não relacionados à gravidez
        - Perguntas sobre outros temas
        - Mensagens de erro/engano

        Responda APENAS com JSON válido:
        {
          "isRelated": true/false,
          "shouldOfferRegistration": true/false,
          "isGreeting": true/false,
          "confidence": número_entre_0_e_100,
          "messageType": "greeting" | "project_related" | "unrelated" | "unclear",
          "reasoning": "explicação_breve"
        }`;

      const response = await this.geminiService.generateContent(prompt);
      const analysis = JSON.parse(this.cleanJSON(response));

      // Validar resposta
      analysis.confidence = Math.max(0, Math.min(100, analysis.confidence || 0));

      return {
        isRelated: analysis.isRelated || false,
        shouldOfferRegistration: analysis.shouldOfferRegistration || false,
        isGreeting: analysis.isGreeting || false,
        messageType: analysis.messageType || 'unclear',
        confidence: analysis.confidence
      };

    } catch (error) {
      console.error('❌ Erro ao analisar mensagem:', error);
      // Fallback: análise manual básica
      return this.basicProjectAnalysis(message);
    }
  }

  /**
   * Gerar resposta inteligente para saudações de não cadastrados
   */
  public async generateGreetingResponse(message: string): Promise<string> {
    try {
      const prompt = `
        Uma pessoa não cadastrada enviou apenas uma saudação simples.
        Responda de forma acolhedora como Rafaela e descubra se ela precisa de ajuda com gestação.

        Saudação recebida: "${message}"

        DIRETRIZES RAFAELA:
        1. Responda à saudação de forma calorosa
        2. Se apresente brevemente como Rafaela
        3. Pergunte se ela está grávida ou tem dúvidas sobre gestação
        4. Use tom maternal e acolhedor
        5. Use 30-60 palavras
        6. Inclua elementos da personalidade Rafaela: "nossa gente", "🧡"

        RESPOSTA PARA SAUDAÇÃO (30-60 palavras):`;

      const response = await this.geminiService.generateContent(prompt);
      return this.validateResponseLength(response, 30, 60);
    } catch (error) {
      console.error('❌ Erro ao gerar resposta para saudação:', error);
      return 'Oi! Tudo bem? Sou a Rafaela! 🧡 Estou aqui para ajudar gestantes durante essa jornada especial. Você está grávida ou tem alguma dúvida sobre gestação? Nossa gente, fico feliz em conversar com você!';
    }
  }

  /**
   * Gerar resposta oferecendo cadastro
   */
  public async generateRegistrationOffer(message: string): Promise<string> {
    try {
      const prompt = `
        Uma pessoa não cadastrada enviou uma mensagem relacionada ao projeto de gestantes.
        Ofereça o cadastro de forma acolhedora.

        Mensagem recebida: "${message}"

        DIRETRIZES:
        1. Seja acolhedora e maternal (estilo Rafaela)
        2. Explique brevemente que é um serviço de acompanhamento
        3. Solicite os dados: Nome completo, telefone, gênero do bebê (opcional)
        4. Use tom caloroso mas profissional
        5. Use 40-80 palavras

        RESPOSTA OFERECENDO CADASTRO (40-80 palavras):`;

      const response = await this.geminiService.generateContent(prompt);
      return this.validateResponseLength(response, 40, 80);
    } catch (error) {
      console.error('❌ Erro ao gerar oferta de cadastro:', error);
      return 'Olá! Que bom saber que você está grávida! Sou Rafaela e posso acompanhá-la durante sua gestação. Para começar, preciso de seu nome completo, telefone e, se quiser, o gênero do bebê. Como posso chamá-la?';
    }
  }

  /**
   * Análise manual básica (fallback)
   */
  private basicProjectAnalysis(message: string): {
    isRelated: boolean;
    shouldOfferRegistration: boolean;
    isGreeting: boolean;
    messageType: 'greeting' | 'project_related' | 'unrelated' | 'unclear';
    confidence: number;
  } {
    const lowerMessage = message.toLowerCase();

    // Palavras que indicam relação com projeto gestantes
    const pregnancyWords = [
      'grávida', 'gestante', 'gravidez', 'gestação', 'bebê', 'bebe', 'criança',
      'pré-natal', 'prenatal', 'consulta', 'médico', 'obstetra', 'ginecologista',
      'ultrassom', 'exame', 'trimestre', 'semanas', 'meses', 'parto', 'nascimento',
      'maternidade', 'maternal', 'mãe', 'mamãe', 'filho', 'filha', 'menino', 'menina'
    ];

    // Palavras que indicam interesse em acompanhamento
    const interestWords = [
      'ajuda', 'acompanhamento', 'orientação', 'dúvida', 'pergunta', 'informação',
      'cuidado', 'saúde', 'bem-estar', 'apoio', 'suporte'
    ];

    // Verificar se contém palavras relacionadas
    const hasPregnancyWords = pregnancyWords.some(word => lowerMessage.includes(word));
    const hasInterestWords = interestWords.some(word => lowerMessage.includes(word));

    // Saudações simples
    const greetingMessages = [
      'oi', 'olá', 'ola', 'hey', 'e ai', 'eai', 'opa', 'oie',
      'bom dia', 'boa tarde', 'boa noite', 'bomdia', 'boatarde', 'boanoite',
      'tudo bem', 'como vai', 'como está', 'como esta', 'tudo bom'
    ];
    const isGreeting = greetingMessages.some(greeting =>
      lowerMessage.trim() === greeting ||
      lowerMessage.trim().startsWith(greeting + ' ') ||
      lowerMessage.trim().endsWith(' ' + greeting)
    );

    let isRelated = false;
    let shouldOfferRegistration = false;
    let isGreetingDetected = false;
    let messageType: 'greeting' | 'project_related' | 'unrelated' | 'unclear' = 'unclear';
    let confidence = 0;

    if (hasPregnancyWords) {
      isRelated = true;
      shouldOfferRegistration = true;
      messageType = 'project_related';
      confidence = 85;
    } else if (hasInterestWords && !isGreeting) {
      isRelated = true;
      shouldOfferRegistration = false; // Precisa de mais contexto
      messageType = 'unclear';
      confidence = 60;
    } else if (isGreeting) {
      isRelated = false;
      shouldOfferRegistration = false;
      isGreetingDetected = true;
      messageType = 'greeting';
      confidence = 95;
    } else {
      isRelated = false;
      shouldOfferRegistration = false;
      messageType = 'unrelated';
      confidence = 70;
    }

    return {
      isRelated,
      shouldOfferRegistration,
      isGreeting: isGreetingDetected,
      messageType,
      confidence
    };
  }



  /**
   * Processar dados de cadastro fornecidos
   */
  public async processRegistrationData(contact: IContact, message: string): Promise<{
    success: boolean;
    response: string;
    needsMoreData: boolean;
    extractedData?: any;
  }> {
    try {
      // Usar IA para extrair dados da mensagem
      const extractedData = await this.extractRegistrationData(message);
      
      if (extractedData.name && extractedData.phone) {
        // Dados completos - finalizar cadastro
        // Nota: Atualização seria feita via contactService do Supabase
        console.log('✅ Cadastro finalizado:', {
          name: extractedData.name,
          babyGender: extractedData.babyGender || 'unknown',
          registrationStatus: 'registered'
        });
        
        return {
          success: true,
          response: `Perfeito, ${extractedData.name}! Seu cadastro foi realizado com sucesso. Agora posso acompanhá-la durante sua jornada maternal. Como está se sentindo hoje?`,
          needsMoreData: false,
          extractedData
        };
      } else {
        // Dados incompletos - solicitar mais informações
        const missingFields = [];
        if (!extractedData.name) missingFields.push('nome completo');
        if (!extractedData.phone) missingFields.push('telefone');
        
        return {
          success: false,
          response: `Preciso de mais algumas informações. Por favor, me informe: ${missingFields.join(' e ')}.`,
          needsMoreData: true,
          extractedData
        };
      }
    } catch (error) {
      console.error('❌ Erro ao processar dados de cadastro:', error);
      return {
        success: false,
        response: 'Desculpe, tive dificuldade para processar suas informações. Pode repetir seu nome completo e telefone?',
        needsMoreData: true
      };
    }
  }

  /**
   * Extrair dados de cadastro da mensagem usando IA
   */
  private async extractRegistrationData(message: string): Promise<{
    name?: string;
    phone?: string;
    babyGender?: 'male' | 'female' | 'unknown';
  }> {
    const prompt = `
      Extraia dados de cadastro desta mensagem:
      
      "${message}"
      
      Responda APENAS com JSON válido:
      {
        "name": "nome_completo_ou_null",
        "phone": "telefone_ou_null",
        "babyGender": "male/female/unknown"
      }
      
      REGRAS:
      - name: apenas se tiver nome E sobrenome
      - phone: apenas números de telefone válidos
      - babyGender: "male" para menino, "female" para menina, "unknown" se não mencionado`;

    try {
      const response = await this.geminiService.generateContent(prompt);
      return JSON.parse(this.cleanJSON(response));
    } catch (error) {
      console.error('❌ Erro ao extrair dados:', error);
      return {};
    }
  }

  /**
   * Limpar JSON da resposta da IA
   */
  private cleanJSON(text: string): string {
    return text.replace(/```json\s*/g, '').replace(/```\s*/g, '').trim();
  }

  /**
   * Validar tamanho da resposta
   */
  private validateResponseLength(response: string, minWords: number, maxWords: number): string {
    const words = response.trim().split(/\s+/);
    if (words.length > maxWords) {
      return words.slice(0, maxWords).join(' ') + '.';
    }
    return response;
  }
}

export default InterestEvaluatorService;
