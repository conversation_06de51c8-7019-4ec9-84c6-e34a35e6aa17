
import React, { useState } from 'react';
import PageTitle from '../shared/PageTitle';
import Input from '../shared/Input';
import Button from '../shared/Button';
import { DEFAULT_API_KEY_NOTICE } from '../../constants';

const SettingsPage: React.FC = () => {
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState('system');
  // const apiKey = process.env.API_KEY; // This is how you'd typically access it

  return (
    <div>
      <PageTitle title="Configurações" subtitle="Ajuste as preferências do sistema." />

      <div className="space-y-8">
        <section className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">API Key (Google Gemini)</h3>
          <p className="text-sm text-gray-600 mb-2">
            A API Key para o Google Gemini é configurada através de variáveis de ambiente no servidor (<code>process.env.API_KEY</code>). 
            Esta seção é apenas para visualização do status.
          </p>
          {process.env.API_KEY ? (
             <div className="p-3 bg-green-50 border border-green-200 rounded-md text-sm text-green-700">
                API Key está configurada e carregada.
             </div>
          ) : (
             <div className="p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700">
                {DEFAULT_API_KEY_NOTICE}
             </div>
          )}
           <p className="text-xs text-gray-500 mt-2">
            Para alterar a API Key, você precisa modificar as configurações do ambiente de execução desta aplicação.
          </p>
        </section>

        <section className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Notificações</h3>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="notifications"
              checked={notificationsEnabled}
              onChange={() => setNotificationsEnabled(!notificationsEnabled)}
              className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
            />
            <label htmlFor="notifications" className="ml-2 block text-sm text-gray-900">
              Ativar notificações desktop (simulado)
            </label>
          </div>
        </section>

        <section className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Tema da Interface (Simulado)</h3>
          <div className="space-y-2">
            {['Claro', 'Escuro', 'Sistema'].map(theme => (
              <div key={theme} className="flex items-center">
                <input
                  type="radio"
                  id={`theme-${theme.toLowerCase()}`}
                  name="theme"
                  value={theme.toLowerCase()}
                  checked={selectedTheme === theme.toLowerCase()}
                  onChange={(e) => setSelectedTheme(e.target.value)}
                  className="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                />
                <label htmlFor={`theme-${theme.toLowerCase()}`} className="ml-2 block text-sm text-gray-900">
                  {theme}
                </label>
              </div>
            ))}
          </div>
        </section>
        
        <section className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-dark mb-4">Backup de Dados (Simulado)</h3>
          <p className="text-sm text-gray-600 mb-3">Simulação de funcionalidade de backup.</p>
          <Button variant="secondary">Iniciar Backup Agora</Button>
        </section>

      </div>
    </div>
  );
};

export default SettingsPage;
    