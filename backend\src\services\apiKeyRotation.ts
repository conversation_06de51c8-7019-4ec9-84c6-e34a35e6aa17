import { supabase } from '../config/supabase';
import crypto from 'crypto';

interface ApiKeyConfig {
  id: string;
  service: string;
  key_name: string;
  encrypted_key: string;
  is_active: boolean;
  created_at: string;
  expires_at?: string;
  rotation_interval_days: number;
  last_rotated?: string;
  rotation_count: number;
}

interface RotationResult {
  success: boolean;
  service: string;
  oldKeyId?: string;
  newKeyId?: string;
  error?: string;
  nextRotation?: string;
}

/**
 * Serviço de rotação automática de API Keys
 * Gerencia rotação segura de chaves de API para diferentes serviços
 */
class ApiKeyRotationService {
  private encryptionKey: string;
  private rotationInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Configurações de rotação por serviço
  private readonly ROTATION_CONFIG = {
    gemini: {
      intervalDays: 30, // Rodar a cada 30 dias
      warningDays: 7,   // Avisar 7 dias antes
      maxKeys: 3        // Manter máximo 3 chaves ativas
    },
    whatsapp: {
      intervalDays: 90, // Rodar a cada 90 dias
      warningDays: 14,  // Avisar 14 dias antes
      maxKeys: 2        // Manter máximo 2 chaves ativas
    },
    supabase: {
      intervalDays: 60, // Rodar a cada 60 dias
      warningDays: 10,  // Avisar 10 dias antes
      maxKeys: 2        // Manter máximo 2 chaves ativas
    }
  };

  constructor() {
    this.encryptionKey = this.getEncryptionKey();
  }

  /**
   * Obter chave de criptografia
   */
  private getEncryptionKey(): string {
    const key = process.env.API_ENCRYPTION_KEY || process.env.JWT_SECRET;
    if (!key) {
      throw new Error('Chave de criptografia não configurada');
    }
    return crypto.createHash('sha256').update(key).digest('hex').substring(0, 32);
  }

  /**
   * Criptografar API key
   */
  private encryptApiKey(apiKey: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(apiKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * Descriptografar API key
   */
  private decryptApiKey(encryptedKey: string): string {
    const [ivHex, encrypted] = encryptedKey.split(':');
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * Iniciar serviço de rotação automática
   */
  public start(): void {
    if (this.isRunning) {
      console.log('⚠️ Serviço de rotação de API keys já está rodando');
      return;
    }

    console.log('🔐 Iniciando serviço de rotação de API keys...');
    this.isRunning = true;

    // Verificar imediatamente
    this.checkAndRotateKeys();

    // Configurar verificação diária
    this.rotationInterval = setInterval(() => {
      this.checkAndRotateKeys();
    }, 24 * 60 * 60 * 1000); // 24 horas

    console.log('✅ Serviço de rotação iniciado - verificação diária ativa');
  }

  /**
   * Parar serviço de rotação
   */
  public stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Serviço de rotação não está rodando');
      return;
    }

    console.log('🛑 Parando serviço de rotação de API keys...');

    if (this.rotationInterval) {
      clearInterval(this.rotationInterval);
      this.rotationInterval = null;
    }

    this.isRunning = false;
    console.log('✅ Serviço de rotação parado');
  }

  /**
   * Verificar e rotar chaves que precisam de rotação
   */
  private async checkAndRotateKeys(): Promise<void> {
    try {
      console.log('🔍 Verificando chaves que precisam de rotação...');

      const services = Object.keys(this.ROTATION_CONFIG);
      const results: RotationResult[] = [];

      for (const service of services) {
        try {
          const result = await this.checkServiceKeys(service);
          results.push(result);
        } catch (error) {
          console.error(`❌ Erro ao verificar chaves do serviço ${service}:`, error);
          results.push({
            success: false,
            service,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }
      }

      // Log dos resultados
      const needRotation = results.filter(r => !r.success && r.error?.includes('rotação'));
      const rotated = results.filter(r => r.success && r.newKeyId);

      if (rotated.length > 0) {
        console.log(`🔄 ${rotated.length} chave(s) rotacionada(s) com sucesso`);
      }

      if (needRotation.length > 0) {
        console.warn(`⚠️ ${needRotation.length} chave(s) precisam de rotação manual`);
      }

      if (results.every(r => r.success)) {
        console.log('✅ Todas as chaves estão atualizadas');
      }

    } catch (error) {
      console.error('❌ Erro durante verificação de rotação:', error);
    }
  }

  /**
   * Verificar chaves de um serviço específico
   */
  private async checkServiceKeys(service: string): Promise<RotationResult> {
    const config = this.ROTATION_CONFIG[service as keyof typeof this.ROTATION_CONFIG];
    if (!config) {
      return {
        success: false,
        service,
        error: 'Serviço não configurado para rotação'
      };
    }

    // Buscar chaves ativas do serviço
    const { data: keys, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('service', service)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Erro ao buscar chaves: ${error.message}`);
    }

    if (!keys || keys.length === 0) {
      return {
        success: false,
        service,
        error: 'Nenhuma chave ativa encontrada'
      };
    }

    const latestKey = keys[0];
    const keyAge = this.calculateKeyAge(latestKey.created_at);

    // Verificar se precisa de rotação
    if (keyAge >= config.intervalDays) {
      console.log(`🔄 Chave do ${service} precisa de rotação (${keyAge} dias)`);
      
      // Em produção, aqui seria feita a rotação automática
      // Por segurança, apenas logamos a necessidade
      return {
        success: false,
        service,
        error: `Chave precisa de rotação (${keyAge}/${config.intervalDays} dias)`
      };
    }

    // Verificar se está próximo da rotação (warning)
    const daysUntilRotation = config.intervalDays - keyAge;
    if (daysUntilRotation <= config.warningDays) {
      console.warn(`⚠️ Chave do ${service} expira em ${daysUntilRotation} dias`);
    }

    return {
      success: true,
      service,
      nextRotation: this.calculateNextRotation(latestKey.created_at, config.intervalDays)
    };
  }

  /**
   * Calcular idade da chave em dias
   */
  private calculateKeyAge(createdAt: string): number {
    const created = new Date(createdAt);
    const now = new Date();
    const diffMs = now.getTime() - created.getTime();
    return Math.floor(diffMs / (1000 * 60 * 60 * 24));
  }

  /**
   * Calcular próxima rotação
   */
  private calculateNextRotation(createdAt: string, intervalDays: number): string {
    const created = new Date(createdAt);
    const nextRotation = new Date(created);
    nextRotation.setDate(nextRotation.getDate() + intervalDays);
    return nextRotation.toISOString();
  }

  /**
   * Registrar nova API key (para uso manual)
   */
  public async registerApiKey(
    service: string,
    keyName: string,
    apiKey: string,
    expiresAt?: string
  ): Promise<string> {
    try {
      const encryptedKey = this.encryptApiKey(apiKey);
      const config = this.ROTATION_CONFIG[service as keyof typeof this.ROTATION_CONFIG];

      const { data, error } = await supabase
        .from('api_keys')
        .insert({
          service,
          key_name: keyName,
          encrypted_key: encryptedKey,
          is_active: true,
          expires_at: expiresAt,
          rotation_interval_days: config?.intervalDays || 30,
          rotation_count: 0
        })
        .select('id')
        .single();

      if (error) {
        throw new Error(`Erro ao registrar chave: ${error.message}`);
      }

      console.log(`✅ API key registrada: ${service}/${keyName}`);
      return data.id;

    } catch (error) {
      console.error('❌ Erro ao registrar API key:', error);
      throw error;
    }
  }

  /**
   * Obter API key ativa para um serviço
   */
  public async getActiveApiKey(service: string): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('api_keys')
        .select('encrypted_key')
        .eq('service', service)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        console.warn(`⚠️ Nenhuma chave ativa encontrada para ${service}`);
        return null;
      }

      return this.decryptApiKey(data.encrypted_key);

    } catch (error) {
      console.error(`❌ Erro ao obter chave para ${service}:`, error);
      return null;
    }
  }

  /**
   * Obter status de todas as chaves
   */
  public async getKeysStatus(): Promise<any> {
    try {
      const { data: keys, error } = await supabase
        .from('api_keys')
        .select('service, key_name, is_active, created_at, expires_at, rotation_interval_days, last_rotated, rotation_count')
        .eq('is_active', true)
        .order('service', { ascending: true });

      if (error) {
        throw new Error(`Erro ao buscar status: ${error.message}`);
      }

      const status = keys?.reduce((acc, key) => {
        const age = this.calculateKeyAge(key.created_at);
        const config = this.ROTATION_CONFIG[key.service as keyof typeof this.ROTATION_CONFIG];
        
        acc[key.service] = {
          keyName: key.key_name,
          ageDays: age,
          maxAgeDays: key.rotation_interval_days,
          needsRotation: age >= key.rotation_interval_days,
          warningThreshold: config?.warningDays || 7,
          nextRotation: this.calculateNextRotation(key.created_at, key.rotation_interval_days),
          rotationCount: key.rotation_count
        };
        
        return acc;
      }, {} as any);

      return {
        services: status,
        summary: {
          totalKeys: keys?.length || 0,
          needRotation: Object.values(status).filter((s: any) => s.needsRotation).length,
          isRunning: this.isRunning
        }
      };

    } catch (error) {
      console.error('❌ Erro ao obter status das chaves:', error);
      throw error;
    }
  }
}

// Exportar instância singleton
export const apiKeyRotationService = new ApiKeyRotationService();
