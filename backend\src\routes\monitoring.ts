import { Router } from 'express';
import { performanceMonitor } from '../services/performanceMonitor';
import { apiKeyRotationService } from '../services/apiKeyRotation';
import { followUpService } from '../services/followUpService';
import { wppConnectIntegration } from '../services/wppConnectIntegration';

const router = Router();

/**
 * GET /api/monitoring/health
 * Health check completo do sistema
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: await checkDatabaseHealth(),
        whatsapp: checkWhatsAppHealth(),
        followUp: checkFollowUpHealth(),
        apiKeyRotation: checkApiKeyRotationHealth(),
        performanceMonitor: checkPerformanceMonitorHealth()
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        nodeVersion: process.version,
        platform: process.platform
      }
    };

    // Determinar status geral
    const serviceStatuses = Object.values(health.services);
    const hasUnhealthy = serviceStatuses.some(service => service.status !== 'healthy');
    
    if (hasUnhealthy) {
      health.status = 'degraded';
    }

    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);

  } catch (error) {
    console.error('❌ Erro no health check:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/monitoring/metrics
 * Métricas de performance do sistema
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = {
      system: performanceMonitor.getSystemMetrics(),
      history: performanceMonitor.getMetricsHistory(),
      status: performanceMonitor.getStatus()
    };

    res.json({
      success: true,
      data: metrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao obter métricas:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/monitoring/api-keys
 * Status das API keys e rotação
 */
router.get('/api-keys', async (req, res) => {
  try {
    const keysStatus = await apiKeyRotationService.getKeysStatus();

    res.json({
      success: true,
      data: keysStatus,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao obter status das API keys:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/monitoring/services
 * Status detalhado de todos os serviços
 */
router.get('/services', async (req, res) => {
  try {
    const services = {
      followUp: {
        status: followUpService.getStatus(),
        description: 'Serviço de follow-up automático para gestantes'
      },
      whatsapp: {
        status: wppConnectIntegration?.getConnectionStatus() || { isConnected: false },
        description: 'Integração com WhatsApp via wppConnect'
      },
      performanceMonitor: {
        status: performanceMonitor.getStatus(),
        description: 'Monitoramento de performance do sistema'
      },
      apiKeyRotation: {
        status: await apiKeyRotationService.getKeysStatus(),
        description: 'Rotação automática de API keys'
      }
    };

    res.json({
      success: true,
      data: services,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao obter status dos serviços:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * POST /api/monitoring/metrics/custom
 * Registrar métrica customizada
 */
router.post('/metrics/custom', async (req, res) => {
  try {
    const { name, value, unit, service, metadata } = req.body;

    if (!name || value === undefined || !unit || !service) {
      return res.status(400).json({
        success: false,
        error: 'Campos obrigatórios: name, value, unit, service'
      });
    }

    await performanceMonitor.recordCustomMetric(name, value, unit, service, metadata);

    res.json({
      success: true,
      message: 'Métrica registrada com sucesso',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao registrar métrica customizada:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/monitoring/errors
 * Obter erros recentes do sistema
 */
router.get('/errors', async (req, res) => {
  try {
    // Em produção, buscar de um sistema de logging
    // Por enquanto, retornar placeholder
    const errors = {
      recent: [],
      summary: {
        last24h: 0,
        lastWeek: 0,
        mostCommon: 'Nenhum erro recente'
      }
    };

    res.json({
      success: true,
      data: errors,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Erro ao obter erros do sistema:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Funções auxiliares para health checks

async function checkDatabaseHealth() {
  try {
    const { supabase } = await import('../config/supabase');
    const startTime = Date.now();
    
    const { data, error } = await supabase
      .from('contacts')
      .select('count(*)', { count: 'exact', head: true });

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        responseTime
      };
    }

    return {
      status: 'healthy',
      responseTime,
      details: 'Conexão com Supabase funcionando'
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

function checkWhatsAppHealth() {
  try {
    if (!wppConnectIntegration) {
      return {
        status: 'unhealthy',
        error: 'Integração WhatsApp não inicializada'
      };
    }

    const connectionStatus = wppConnectIntegration.getConnectionStatus();
    
    return {
      status: connectionStatus.isConnected ? 'healthy' : 'unhealthy',
      details: connectionStatus
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

function checkFollowUpHealth() {
  try {
    const status = followUpService.getStatus();
    
    return {
      status: status.isRunning ? 'healthy' : 'unhealthy',
      details: status
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

function checkApiKeyRotationHealth() {
  try {
    // Verificar se o serviço está rodando
    // Como não temos método público para isso, assumir healthy
    return {
      status: 'healthy',
      details: 'Serviço de rotação de API keys ativo'
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

function checkPerformanceMonitorHealth() {
  try {
    const status = performanceMonitor.getStatus();
    
    return {
      status: status.isRunning ? 'healthy' : 'unhealthy',
      details: status
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}

export default router;
