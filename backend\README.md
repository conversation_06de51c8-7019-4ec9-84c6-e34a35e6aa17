# Backend - Gestação Materna Integrada

Backend robusto para gerenciamento de conversas via WhatsApp com integração de IA para gestantes.

## Funcionalidades

- ✅ Integração WhatsApp via whatsapp-web.js
  - Conexão via QR Code automática
  - Envio de mensagens individuais e em lote
  - Recebimento automático via webhook
  - Status de conexão em tempo real
  - Dados enriquecidos de contatos
  - Histórico de conversas completo

- 🤖 Inteligência Artificial
  - Processamento Gemini AI para respostas inteligentes
  - Contexto de conversa mantido
  - Respostas personalizadas por gestante
  - Análise de sentimento e necessidades
  - Sugestões automáticas de acompanhamento

## Requisitos

- Node.js 16+
- Supabase (PostgreSQL)
- Chave de API do Gemini AI

## Instalação

1. Clone o repositório
2. Instale as dependências:
```bash
npm install
```

3. Copie o arquivo de ambiente:
```bash
cp .env.example .env
```

4. Configure as variáveis de ambiente no arquivo `.env`

5. Inicie o servidor:
```bash
npm run dev
```

## Estrutura do Projeto

```
src/
  ├── config/         # Configurações (Supabase, etc)
  ├── models/         # Interfaces TypeScript (Supabase)
  ├── routes/         # Rotas da API
  ├── services/       # Serviços (WhatsApp, IA, Supabase)
  ├── webhooks/       # Webhooks para processamento
  └── server.ts       # Arquivo principal
```

## API Endpoints

### WhatsApp
- `GET /api/whatsapp/status` - Status da conexão
- `POST /api/whatsapp/send` - Enviar mensagem
- `POST /api/whatsapp/send-bulk` - Enviar mensagens em lote

### Contatos
- `GET /api/contacts` - Listar contatos
- `GET /api/contacts/:id/messages` - Mensagens do contato
- `PUT /api/contacts/:id` - Atualizar contato

### Análise
- `POST /api/analyze` - Analisar mensagem

## Webhooks

- `POST /webhook/message` - Processar mensagens recebidas
- `POST /webhook/status` - Atualizar status da conexão

## Desenvolvimento

```bash
# Iniciar em modo desenvolvimento
npm run dev

# Compilar TypeScript
npm run build

# Iniciar em produção
npm start
``` 