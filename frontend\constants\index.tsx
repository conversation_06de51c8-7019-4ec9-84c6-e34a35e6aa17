import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon,
  PlusIcon,
  PencilSquareIcon,
  TrashIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  CheckIcon,
  XMarkIcon,
  BellIcon,
  CalendarIcon,
  ClockIcon,
  PhoneIcon,
  EnvelopeIcon,
  HeartIcon,
  ArrowRightOnRectangleIcon,
  MicrophoneIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PaperAirplaneIcon,
  ChevronDownIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export const APP_NAME = "Rafaela Cuida";

export const DEFAULT_API_KEY_NOTICE = "API Key não configurada. Verifique as variáveis de ambiente.";

export const ICONS = {
  dashboard: <HomeIcon className="w-5 h-5" />,
  pregnant: <UserGroupIcon className="w-5 h-5" />,
  whatsapp: <ChatBubbleLeftRightIcon className="w-5 h-5" />,
  settings: <Cog6ToothIcon className="w-5 h-5" />,
  logout: <ArrowRightOnRectangleIcon className="w-5 h-5" />,
  add: <PlusIcon className="w-5 h-5" />,
  edit: <PencilSquareIcon className="w-5 h-5" />,
  delete: <TrashIcon className="w-5 h-5" />,
  upload: <ArrowUpTrayIcon className="w-5 h-5" />,
  download: <ArrowDownTrayIcon className="w-5 h-5" />,
  check: <CheckIcon className="w-5 h-5" />,
  x: <XMarkIcon className="w-5 h-5" />,
  notification: <BellIcon className="w-5 h-5" />,
  calendar: <CalendarIcon className="w-5 h-5" />,
  clock: <ClockIcon className="w-5 h-5" />,
  phone: <PhoneIcon className="w-5 h-5" />,
  email: <EnvelopeIcon className="w-5 h-5" />,
  followUp: <HeartIcon className="w-5 h-5" />,
  microphone: <MicrophoneIcon className="w-5 h-5" />,
  ai: <SparklesIcon className="w-5 h-5" />,
  warning: <ExclamationTriangleIcon className="w-5 h-5" />,
  checkCircle: <CheckCircleIcon className="w-5 h-5" />,
  send: <PaperAirplaneIcon className="w-5 h-5" />,
  chevronDown: <ChevronDownIcon className="w-5 h-5" />,
  info: <InformationCircleIcon className="w-5 h-5" />,
  xCircle: <XCircleIcon className="w-5 h-5" />
};