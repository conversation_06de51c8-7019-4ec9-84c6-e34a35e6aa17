export interface Contact {
  id: string;
  _id?: string; // Compatibilidade com código antigo
  name: string;
  phone: string;
  babyGender?: 'male' | 'female' | 'unknown';
  baby_gender?: 'male' | 'female' | 'unknown'; // Compatibilidade com backend
  lastInteraction?: Date | string;
  last_interaction?: string; // Compatibilidade com backend
  createdAt?: Date | string;
  created_at?: string; // Compatibilidade com backend
  updatedAt?: Date | string;
  updated_at?: string; // Compatibilidade com backend
  isActive?: boolean;
  is_active?: boolean; // Compatibilidade com backend
  registrationStatus?: string;
  registration_status?: string; // Compatibilidade com backend
}

export interface Message {
  id: string;
  contactId?: string;
  content?: string;
  text?: string;
  sender?: 'user' | 'gestante' | 'ai';
  timestamp?: string;
  status?: 'sending' | 'sent' | 'delivered' | 'failed' | 'saved';
  isFromUser?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface WhatsAppStatus {
  isConnected: boolean;
  qrCode?: string;
  lastError?: string;
}

export enum WhatsAppConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  QR_CODE = 'qr_code',
  ERROR = 'error'
}

export interface GroundingChunk {
  web?: {
    uri: string;
    title?: string;
  };
}

export interface PregnantWoman {
  id: string;
  name: string;
  phone: string;
  email?: string;
  dueDate?: string;
  observations?: string;
  createdAt?: string;
  age?: number;
}