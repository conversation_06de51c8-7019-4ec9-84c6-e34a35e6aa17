import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import { WhatsAppClient } from './services/whatsapp';
import { GeminiAIService } from './services/gemini';
import { whatsappAutoStart } from './services/whatsappAutoStart';

import { setupRoutes } from './routes';

import { eventCaptureMiddleware, errorEventMiddleware, emitSystemStartup, emitSystemShutdown } from './middleware/eventCapture';

dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:3000',
      process.env.FRONTEND_URL || 'http://localhost:5173'
    ],
    methods: ['GET', 'POST']
  }
});

// Middlewares de segurança
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Rate limiting global (temporariamente mais permissivo para debug)
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'), // 1 minuto
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '1000'), // máximo 1000 requests por IP
  message: {
    error: 'Muitas requisições deste IP, tente novamente mais tarde.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// CORS configurado para múltiplas portas
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:3000',
    process.env.CORS_ORIGIN || 'http://localhost:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing
app.use(express.json({
  limit: process.env.MAX_FILE_SIZE || '10mb',
  verify: (req, res, buf) => {
    // Verificação adicional de segurança para JSON
    try {
      JSON.parse(buf.toString());
    } catch (e) {
      throw new Error('JSON inválido');
    }
  }
}));

app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de captura de eventos
app.use(eventCaptureMiddleware);

// Inicialização dos serviços
const geminiService = new GeminiAIService();
const whatsappClient = new WhatsAppClient(io, geminiService);

// Inicializar wppConnect com Socket.IO
import { initializeWppConnect } from './services/wppConnectIntegration';
import { followUpService } from './services/followUpService';
import { sessionMonitor } from './services/sessionMonitor';
import { apiKeyRotationService } from './services/apiKeyRotation';
import { performanceMonitor } from './services/performanceMonitor';

const wppIntegration = initializeWppConnect(io);

// Inicializar monitor de sessão
console.log('📊 Inicializando monitor de sessão WhatsApp...');
sessionMonitor.initialize(wppIntegration);
console.log('✅ Monitor de sessão ativo');

// WhatsApp automático desabilitado para evitar conflito de sessões
// Usando apenas wppConnectIntegration
console.log('📱 WhatsApp: Usando apenas wppConnectIntegration (sessão unificada)');

// Configuração das rotas e webhooks
setupRoutes(app, whatsappClient, geminiService);


// Middleware de tratamento de erros (deve ser o último)
app.use(errorEventMiddleware);


async function initializeServices() {
  console.log('🚀 Banco de dados: Supabase (PostgreSQL)');

  console.log('📋 Gestantes cadastradas: Disponíveis via Supabase');

  // Inicializar serviço de follow-up automático
  console.log('🔄 Iniciando serviço de follow-up automático...');
  followUpService.start();

  // Inicializar monitoramento de performance
  console.log('📊 Iniciando monitoramento de performance...');
  performanceMonitor.start();

  // Inicializar rotação de API keys
  console.log('🔐 Iniciando serviço de rotação de API keys...');
  apiKeyRotationService.start();

  console.log('✅ Todos os serviços inicializados com sucesso!');
}



initializeServices();

const PORT = process.env.PORT || 3001; // Porta configurável

httpServer.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
  console.log('Aguardando conexão do WhatsApp...');

  // Emitir evento de startup do sistema
  emitSystemStartup();
});

// Capturar sinais de shutdown para emitir evento
process.on('SIGTERM', () => {
  console.log('🛑 Recebido SIGTERM, encerrando servidor...');
  gracefulShutdown('SIGTERM received');
});

process.on('SIGINT', () => {
  console.log('🛑 Recebido SIGINT, encerrando servidor...');
  gracefulShutdown('SIGINT received');
});

// Função de shutdown graceful
async function gracefulShutdown(reason: string) {
  console.log(`🛑 Iniciando shutdown graceful: ${reason}`);

  try {
    // Parar serviços em ordem
    console.log('🔄 Parando follow-up service...');
    followUpService.stop();

    console.log('📊 Parando performance monitor...');
    performanceMonitor.stop();

    console.log('🔐 Parando API key rotation...');
    apiKeyRotationService.stop();

    console.log('📱 Desconectando WhatsApp...');
    // wppConnectIntegration será desconectado automaticamente

    // Emitir evento de shutdown
    emitSystemShutdown(reason);

    console.log('✅ Shutdown graceful concluído');
    process.exit(0);

  } catch (error) {
    console.error('❌ Erro durante shutdown:', error);
    process.exit(1);
  }
}