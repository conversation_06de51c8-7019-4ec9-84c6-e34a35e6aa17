import React, { useState, useRef, useEffect } from 'react';
import { MicrophoneIcon, StopIcon, SpeakerWaveIcon } from '@heroicons/react/24/solid';
import { toast } from 'react-toastify';

interface RealTimeTranscriptionProps {
  onTranscriptionUpdate: (text: string) => void;
  onFinalTranscription: (text: string) => void;
  disabled?: boolean;
  language?: string;
}

export const RealTimeTranscription: React.FC<RealTimeTranscriptionProps> = ({
  onTranscriptionUpdate,
  onFinalTranscription,
  disabled = false,
  language = 'pt-BR'
}) => {
  const [isListening, setIsListening] = useState(false);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [finalTranscription, setFinalTranscription] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Verificar suporte do navegador
  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = language;
      recognition.maxAlternatives = 1;
      
      // Configurações para melhor qualidade
      if ('webkitSpeechRecognition' in window) {
        (recognition as any).webkitGrammarList = null;
      }
      
      recognitionRef.current = recognition;
      
      // Event listeners
      recognition.onstart = () => {
        console.log('🎤 Transcrição iniciada');
        setIsListening(true);
        toast.success('Transcrição em tempo real ativada! 🎤');
      };
      
      recognition.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          
          if (event.results[i].isFinal) {
            finalTranscript += transcript + ' ';
          } else {
            interimTranscript += transcript;
          }
        }
        
        // Atualizar transcrição em tempo real
        if (interimTranscript) {
          setCurrentTranscription(interimTranscript);
          onTranscriptionUpdate(finalTranscription + interimTranscript);
        }
        
        // Transcrição final
        if (finalTranscript) {
          const newFinalText = finalTranscription + finalTranscript;
          setFinalTranscription(newFinalText);
          setCurrentTranscription('');
          onFinalTranscription(newFinalText);
          
          console.log('✅ Transcrição final:', finalTranscript);
        }
        
        // Reset timeout para parar automaticamente após silêncio
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
        
        timeoutRef.current = setTimeout(() => {
          if (isListening) {
            stopListening();
          }
        }, 3000); // Parar após 3 segundos de silêncio
      };
      
      recognition.onerror = (event) => {
        console.error('❌ Erro na transcrição:', event.error);
        
        switch (event.error) {
          case 'no-speech':
            toast.warn('Nenhuma fala detectada. Tente falar mais alto.');
            break;
          case 'audio-capture':
            toast.error('Erro ao capturar áudio. Verifique o microfone.');
            break;
          case 'not-allowed':
            toast.error('Permissão de microfone negada.');
            break;
          case 'network':
            toast.error('Erro de rede. Verifique sua conexão.');
            break;
          default:
            toast.error(`Erro na transcrição: ${event.error}`);
        }
        
        setIsListening(false);
      };
      
      recognition.onend = () => {
        console.log('🛑 Transcrição finalizada');
        setIsListening(false);
        
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
      
    } else {
      setIsSupported(false);
      console.warn('⚠️ Speech Recognition não suportado neste navegador');
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [language, onTranscriptionUpdate, onFinalTranscription, finalTranscription, isListening]);

  const startListening = () => {
    if (!recognitionRef.current || !isSupported) {
      toast.error('Transcrição não suportada neste navegador');
      return;
    }
    
    try {
      setCurrentTranscription('');
      setFinalTranscription('');
      recognitionRef.current.start();
    } catch (error) {
      console.error('Erro ao iniciar transcrição:', error);
      toast.error('Erro ao iniciar transcrição');
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const clearTranscription = () => {
    setCurrentTranscription('');
    setFinalTranscription('');
    onTranscriptionUpdate('');
    onFinalTranscription('');
    toast.success('Transcrição limpa');
  };

  if (!isSupported) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-yellow-800">
          <SpeakerWaveIcon className="w-5 h-5" />
          <span className="font-medium">Transcrição não suportada</span>
        </div>
        <p className="text-sm text-yellow-700 mt-1">
          Seu navegador não suporta transcrição em tempo real. 
          Use Chrome, Edge ou Safari para esta funcionalidade.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          🎤 Transcrição em Tempo Real
        </h3>
        <div className="flex items-center space-x-2">
          {isListening && (
            <div className="flex items-center space-x-2 text-red-600">
              <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">Ouvindo...</span>
            </div>
          )}
        </div>
      </div>

      {/* Área de transcrição */}
      <div className="min-h-[120px] bg-gray-50 rounded-lg p-4 border-2 border-dashed border-gray-300">
        {finalTranscription || currentTranscription ? (
          <div className="space-y-2">
            {finalTranscription && (
              <p className="text-gray-900 leading-relaxed">
                {finalTranscription}
              </p>
            )}
            {currentTranscription && (
              <p className="text-blue-600 italic leading-relaxed">
                {currentTranscription}
                <span className="animate-pulse">|</span>
              </p>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <SpeakerWaveIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">
                {isListening 
                  ? 'Fale agora... sua voz será transcrita em tempo real'
                  : 'Clique em "Iniciar" para começar a transcrição'
                }
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Controles */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-3">
          {!isListening ? (
            <button
              onClick={startListening}
              disabled={disabled}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <MicrophoneIcon className="w-5 h-5" />
              <span>Iniciar Transcrição</span>
            </button>
          ) : (
            <button
              onClick={stopListening}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <StopIcon className="w-5 h-5" />
              <span>Parar</span>
            </button>
          )}

          {(finalTranscription || currentTranscription) && (
            <button
              onClick={clearTranscription}
              disabled={isListening}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 transition-colors"
            >
              Limpar
            </button>
          )}
        </div>

        <div className="text-xs text-gray-500">
          <p>Idioma: {language === 'pt-BR' ? 'Português (Brasil)' : language}</p>
        </div>
      </div>

      {/* Informações */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Fale naturalmente e veja a transcrição aparecer em tempo real</p>
        <p>• A transcrição para automaticamente após 3 segundos de silêncio</p>
        <p>• Texto em azul = transcrição em andamento | Texto em preto = finalizado</p>
      </div>
    </div>
  );
};

// Declaração de tipos para Speech Recognition
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
}

interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: SpeechRecognitionResultList;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}
