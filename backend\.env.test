# Configurações de teste
NODE_ENV=test
PORT=3001

# JWT
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# Supabase (para testes)
SUPABASE_URL=your-test-supabase-url
SUPABASE_ANON_KEY=your-test-supabase-key

# Gemini AI (mock)
GEMINI_API_KEY=test_gemini_api_key

# WhatsApp (mock)
WHATSAPP_SESSION_NAME=test_session

# Rate Limiting (mais permissivo para testes)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS
CORS_ORIGIN=http://localhost:5173

# Logs
LOG_LEVEL=error

# Tamanho máximo de arquivo
MAX_FILE_SIZE=1mb
