import React, { useState, useEffect } from 'react';
import { SpeakerWaveIcon, ClockIcon, UserIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { apiService } from '../../services/apiService';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Spinner from '../shared/Spinner';

interface Transcription {
  id: string;
  content: string;
  transcription: string;
  timestamp: string;
  type: string;
  contacts: {
    id: string;
    name: string;
    phone: string;
  };
}

interface AudioTranscriptionsPanelProps {
  className?: string;
}

const AudioTranscriptionsPanel: React.FC<AudioTranscriptionsPanelProps> = ({ className = '' }) => {
  const [transcriptions, setTranscriptions] = useState<Transcription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchTranscriptions = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await apiService.getTranscriptions(10, 0);
      
      if (response.success && Array.isArray(response.data)) {
        setTranscriptions(response.data);
      } else {
        setTranscriptions([]);
      }
    } catch (err) {
      console.error('Erro ao carregar transcrições:', err);
      setError('Erro ao carregar transcrições de áudio');
      setTranscriptions([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTranscriptions();
    
    // Auto-refresh a cada 30 segundos
    const interval = setInterval(() => {
      fetchTranscriptions(true);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchTranscriptions(true);
  };

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <div className="flex items-center justify-center h-32">
          <Spinner size="md" />
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <SpeakerWaveIcon className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Transcrições de Áudio
          </h3>
          {refreshing && (
            <div className="animate-spin">
              <Spinner size="sm" />
            </div>
          )}
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
        >
          Atualizar
        </button>
      </div>

      {error && (
        <div className="text-red-500 text-sm mb-4 p-3 bg-red-50 rounded-lg">
          {error}
        </div>
      )}

      {transcriptions.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <SpeakerWaveIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
          <p>Nenhuma transcrição de áudio encontrada</p>
          <p className="text-sm">As transcrições aparecerão aqui quando as gestantes enviarem áudios</p>
        </div>
      ) : (
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {transcriptions.map((transcription) => (
            <div
              key={transcription.id}
              className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
            >
              {/* Cabeçalho com informações do contato */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <UserIcon className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-900">
                    {transcription.contacts.name}
                  </span>
                  <div className="flex items-center space-x-1 text-sm text-gray-500">
                    <PhoneIcon className="h-3 w-3" />
                    <span>{transcription.contacts.phone}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <ClockIcon className="h-3 w-3" />
                  <span>
                    {formatDistanceToNow(new Date(transcription.timestamp), {
                      addSuffix: true,
                      locale: ptBR,
                    })}
                  </span>
                </div>
              </div>

              {/* Transcrição */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-start space-x-2">
                  <SpeakerWaveIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-sm font-medium text-blue-800">
                        Transcrição do áudio:
                      </p>
                      <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        IA Gemini
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {transcription.transcription || transcription.content.replace('[ÁUDIO] ', '')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Resposta da IA (se houver) */}
              {transcription.content && transcription.content !== transcription.transcription && (
                <div className="mt-3 bg-orange-50 p-3 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <div className="w-4 h-4 bg-orange-500 rounded-full flex-shrink-0 mt-0.5 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">R</span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-orange-800 mb-1">
                        Resposta da Rafaela:
                      </p>
                      <p className="text-sm text-gray-700 leading-relaxed">
                        {transcription.content}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {transcriptions.length > 0 && (
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            Mostrando as {transcriptions.length} transcrições mais recentes
          </p>
          <p className="text-xs text-blue-600 mt-1">
            🤖 Transcrições processadas automaticamente pela IA Gemini
          </p>
        </div>
      )}
    </div>
  );
};

export default AudioTranscriptionsPanel;
