/**
 * ChromaDB Vector Database Service
 * Vector database dedicado para melhor performance e escalabilidade
 */

import { ChromaApi, Collection } from 'chromadb';
import { v4 as uuidv4 } from 'uuid';
import { embeddingService } from './embeddingService';

export interface ChromaDocument {
  id: string;
  content: string;
  metadata: {
    title: string;
    documentType: string;
    category: string;
    tags: string[];
    uploadedAt: string;
    chunkIndex?: number;
    totalChunks?: number;
    sourceFile?: string;
    [key: string]: any;
  };
  embedding?: number[];
}

export interface ChromaSearchOptions {
  limit?: number;
  threshold?: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeEmbeddings?: boolean;
}

export interface ChromaSearchResult {
  id: string;
  content: string;
  metadata: any;
  similarity: number;
  distance: number;
}

export class ChromaVectorDB {
  private client: ChromaApi;
  private collection: Collection | null = null;
  private collectionName: string;
  private isInitialized: boolean = false;

  constructor() {
    this.collectionName = process.env.CHROMA_COLLECTION_NAME || 'gabinete_knowledge_base';

    // ChromaDB pode não estar disponível, então vamos fazer fallback gracioso
    try {
      const { ChromaApi } = require('chromadb');
      this.client = new ChromaApi({
        path: process.env.CHROMA_URL || 'http://localhost:8000'
      });
    } catch (error) {
      console.warn('⚠️ ChromaDB não disponível, usando fallback');
      this.client = null as any;
    }
  }

  /**
   * Inicializar conexão com ChromaDB
   */
  async initialize(): Promise<void> {
    if (!this.client) {
      console.warn('⚠️ ChromaDB não disponível, pulando inicialização');
      return;
    }

    try {
      console.log('🔌 Conectando ao ChromaDB...');

      // Verificar se a coleção existe
      try {
        this.collection = await this.client.getCollection({
          name: this.collectionName
        });
        console.log(`✅ Coleção '${this.collectionName}' encontrada`);
      } catch (error) {
        // Criar coleção se não existir
        console.log(`📁 Criando coleção '${this.collectionName}'...`);
        this.collection = await this.client.createCollection({
          name: this.collectionName,
          metadata: {
            description: 'Knowledge base do gabinete da vereadora',
            created_at: new Date().toISOString()
          }
        });
        console.log(`✅ Coleção '${this.collectionName}' criada`);
      }

      this.isInitialized = true;
      console.log('🎉 ChromaDB inicializado com sucesso');

    } catch (error) {
      console.error('❌ Erro ao inicializar ChromaDB:', error);
      console.warn('⚠️ Continuando sem ChromaDB');
      this.isInitialized = false;
    }
  }

  /**
   * Adicionar documentos ao ChromaDB
   */
  async addDocuments(documents: ChromaDocument[]): Promise<void> {
    if (!this.isInitialized || !this.collection) {
      await this.initialize();
    }

    try {
      console.log(`📚 Adicionando ${documents.length} documentos ao ChromaDB...`);

      // Preparar dados para ChromaDB
      const ids: string[] = [];
      const embeddings: number[][] = [];
      const metadatas: any[] = [];
      const documents_content: string[] = [];

      for (const doc of documents) {
        ids.push(doc.id);
        documents_content.push(doc.content);
        metadatas.push(doc.metadata);

        // Gerar embedding se não fornecido
        if (doc.embedding) {
          embeddings.push(doc.embedding);
        } else {
          const embeddingResult = await embeddingService.generateEmbedding(doc.content);
          embeddings.push(embeddingResult.embedding);
        }
      }

      // Adicionar ao ChromaDB
      await this.collection!.add({
        ids,
        embeddings,
        metadatas,
        documents: documents_content
      });

      console.log(`✅ ${documents.length} documentos adicionados ao ChromaDB`);

    } catch (error) {
      console.error('❌ Erro ao adicionar documentos ao ChromaDB:', error);
      throw error;
    }
  }

  /**
   * Buscar documentos similares
   */
  async search(query: string, options: ChromaSearchOptions = {}): Promise<ChromaSearchResult[]> {
    if (!this.client) {
      console.warn('⚠️ ChromaDB não disponível, retornando array vazio');
      return [];
    }

    if (!this.isInitialized || !this.collection) {
      await this.initialize();
    }

    if (!this.isInitialized || !this.collection) {
      console.warn('⚠️ ChromaDB não inicializado, retornando array vazio');
      return [];
    }

    try {
      const {
        limit = 10,
        threshold = 0.5,
        filter,
        includeMetadata = true,
        includeEmbeddings = false
      } = options;

      console.log(`🔍 Buscando no ChromaDB: "${query}" (limite: ${limit}, threshold: ${threshold})`);

      // Gerar embedding da query
      const queryEmbedding = await embeddingService.generateEmbedding(query);

      // Buscar no ChromaDB
      const results = await this.collection!.query({
        queryEmbeddings: [queryEmbedding.embedding],
        nResults: limit,
        where: filter,
        include: ['documents', 'metadatas', 'distances']
      });

      // Processar resultados
      const searchResults: ChromaSearchResult[] = [];

      if (results.ids && results.ids[0]) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          const similarity = 1 - distance; // ChromaDB usa distância, convertemos para similaridade

          // Aplicar threshold
          if (similarity >= threshold) {
            searchResults.push({
              id: results.ids[0][i],
              content: results.documents?.[0]?.[i] || '',
              metadata: results.metadatas?.[0]?.[i] || {},
              similarity,
              distance
            });
          }
        }
      }

      console.log(`📊 ChromaDB encontrou ${searchResults.length} resultados relevantes`);
      return searchResults;

    } catch (error) {
      console.error('❌ Erro na busca ChromaDB:', error);
      return [];
    }
  }

  /**
   * Busca híbrida com múltiplas queries
   */
  async hybridSearch(queries: string[], options: ChromaSearchOptions = {}): Promise<ChromaSearchResult[]> {
    const allResults: ChromaSearchResult[] = [];
    const seenIds = new Set<string>();

    for (const query of queries) {
      const results = await this.search(query, {
        ...options,
        limit: Math.ceil((options.limit || 10) / queries.length)
      });

      // Adicionar apenas resultados únicos
      for (const result of results) {
        if (!seenIds.has(result.id)) {
          seenIds.add(result.id);
          allResults.push(result);
        }
      }
    }

    // Ordenar por similaridade
    return allResults
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, options.limit || 10);
  }

  /**
   * Atualizar documento existente
   */
  async updateDocument(id: string, document: Partial<ChromaDocument>): Promise<void> {
    if (!this.isInitialized || !this.collection) {
      await this.initialize();
    }

    try {
      console.log(`🔄 Atualizando documento ${id} no ChromaDB...`);

      // Preparar dados para atualização
      const updateData: any = {
        ids: [id]
      };

      if (document.content) {
        updateData.documents = [document.content];
        
        // Gerar novo embedding se o conteúdo mudou
        const embeddingResult = await embeddingService.generateEmbedding(document.content);
        updateData.embeddings = [embeddingResult.embedding];
      }

      if (document.metadata) {
        updateData.metadatas = [document.metadata];
      }

      await this.collection!.update(updateData);
      console.log(`✅ Documento ${id} atualizado no ChromaDB`);

    } catch (error) {
      console.error(`❌ Erro ao atualizar documento ${id}:`, error);
      throw error;
    }
  }

  /**
   * Remover documento
   */
  async deleteDocument(id: string): Promise<void> {
    if (!this.isInitialized || !this.collection) {
      await this.initialize();
    }

    try {
      await this.collection!.delete({
        ids: [id]
      });
      console.log(`🗑️ Documento ${id} removido do ChromaDB`);
    } catch (error) {
      console.error(`❌ Erro ao remover documento ${id}:`, error);
      throw error;
    }
  }

  /**
   * Obter estatísticas da coleção
   */
  async getStats(): Promise<{
    totalDocuments: number;
    collectionName: string;
    isConnected: boolean;
  }> {
    if (!this.isInitialized || !this.collection) {
      return {
        totalDocuments: 0,
        collectionName: this.collectionName,
        isConnected: false
      };
    }

    try {
      const count = await this.collection!.count();
      
      return {
        totalDocuments: count,
        collectionName: this.collectionName,
        isConnected: true
      };
    } catch (error) {
      console.error('❌ Erro ao obter estatísticas:', error);
      return {
        totalDocuments: 0,
        collectionName: this.collectionName,
        isConnected: false
      };
    }
  }

  /**
   * Limpar toda a coleção
   */
  async clear(): Promise<void> {
    if (!this.isInitialized || !this.collection) {
      await this.initialize();
    }

    try {
      await this.client.deleteCollection({ name: this.collectionName });
      console.log(`🧹 Coleção '${this.collectionName}' limpa`);
      
      // Recriar coleção
      await this.initialize();
    } catch (error) {
      console.error('❌ Erro ao limpar coleção:', error);
      throw error;
    }
  }

  /**
   * Verificar se ChromaDB está disponível
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.heartbeat();
      return true;
    } catch (error) {
      console.error('❌ ChromaDB não está disponível:', error);
      return false;
    }
  }

  /**
   * Migrar dados do sistema antigo
   */
  async migrateFromSimpleDB(simpleDBData: any[]): Promise<void> {
    console.log('🔄 Migrando dados para ChromaDB...');

    const documents: ChromaDocument[] = simpleDBData.map(item => ({
      id: item.id || uuidv4(),
      content: item.content,
      metadata: item.metadata,
      embedding: item.embedding
    }));

    await this.addDocuments(documents);
    console.log(`✅ ${documents.length} documentos migrados para ChromaDB`);
  }
}

// Instância singleton
export const chromaVectorDB = new ChromaVectorDB();
