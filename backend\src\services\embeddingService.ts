/**
 * Serviço de Embeddings para Sistema RAG
 * Gera embeddings usando Gemini AI para busca semântica
 */

import { GoogleGenerativeAI } from '@google/generative-ai';

export interface EmbeddingResult {
  embedding: number[];
  text: string;
  metadata?: Record<string, any>;
}

export interface EmbeddingBatch {
  embeddings: EmbeddingResult[];
  totalTokens: number;
  processingTime: number;
}

export class EmbeddingService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY não configurada');
    }

    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'text-embedding-004' });
  }

  /**
   * Gerar embedding para um texto único
   */
  async generateEmbedding(text: string, metadata?: Record<string, any>): Promise<EmbeddingResult> {
    try {
      const startTime = Date.now();
      
      // Limpar e preparar o texto
      const cleanText = this.preprocessText(text);
      
      if (cleanText.length === 0) {
        throw new Error('Texto vazio após preprocessamento');
      }

      // Gerar embedding usando Gemini
      const result = await this.model.embedContent(cleanText);
      const embedding = result.embedding.values;

      console.log(`📊 Embedding gerado: ${embedding.length} dimensões em ${Date.now() - startTime}ms`);

      return {
        embedding,
        text: cleanText,
        metadata: {
          ...metadata,
          originalLength: text.length,
          processedLength: cleanText.length,
          generatedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('❌ Erro ao gerar embedding:', error);
      throw new Error(`Falha ao gerar embedding: ${error}`);
    }
  }

  /**
   * Gerar embeddings em lote
   */
  async generateEmbeddingBatch(
    texts: string[], 
    metadata?: Record<string, any>[]
  ): Promise<EmbeddingBatch> {
    const startTime = Date.now();
    const embeddings: EmbeddingResult[] = [];
    let totalTokens = 0;

    console.log(`📊 Gerando embeddings para ${texts.length} textos...`);

    for (let i = 0; i < texts.length; i++) {
      try {
        const textMetadata = metadata?.[i] || {};
        const result = await this.generateEmbedding(texts[i], {
          ...textMetadata,
          batchIndex: i,
          batchSize: texts.length
        });

        embeddings.push(result);
        totalTokens += result.text.length;

        // Log de progresso
        if ((i + 1) % 10 === 0 || i === texts.length - 1) {
          console.log(`📊 Progresso: ${i + 1}/${texts.length} embeddings gerados`);
        }

        // Delay para evitar rate limiting
        if (i < texts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error(`❌ Erro ao gerar embedding ${i + 1}:`, error);
        // Continuar com os próximos mesmo se um falhar
      }
    }

    const processingTime = Date.now() - startTime;
    console.log(`✅ Batch completo: ${embeddings.length}/${texts.length} embeddings em ${processingTime}ms`);

    return {
      embeddings,
      totalTokens,
      processingTime
    };
  }

  /**
   * Calcular similaridade de cosseno entre dois embeddings
   */
  calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings devem ter o mesmo tamanho');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    return Math.max(-1, Math.min(1, similarity)); // Garantir que está entre -1 e 1
  }

  /**
   * Encontrar os embeddings mais similares
   */
  findMostSimilar(
    queryEmbedding: number[], 
    candidateEmbeddings: EmbeddingResult[], 
    topK: number = 5
  ): Array<EmbeddingResult & { similarity: number }> {
    const similarities = candidateEmbeddings.map(candidate => ({
      ...candidate,
      similarity: this.calculateCosineSimilarity(queryEmbedding, candidate.embedding)
    }));

    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  }

  /**
   * Preprocessar texto antes de gerar embedding
   */
  private preprocessText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ') // Normalizar espaços
      .replace(/[^\w\s\-.,!?;:()\[\]]/g, '') // Remover caracteres especiais
      .substring(0, 8000); // Limitar tamanho (limite do Gemini)
  }

  /**
   * Dividir texto longo em chunks
   */
  splitIntoChunks(
    text: string, 
    chunkSize: number = 1000, 
    overlap: number = 200
  ): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let currentChunk = '';
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      
      if (currentChunk.length + trimmedSentence.length > chunkSize) {
        if (currentChunk.length > 0) {
          chunks.push(currentChunk.trim());
          
          // Criar overlap pegando as últimas palavras
          const words = currentChunk.split(' ');
          const overlapWords = words.slice(-Math.floor(overlap / 10));
          currentChunk = overlapWords.join(' ') + ' ';
        }
      }
      
      currentChunk += trimmedSentence + '. ';
    }
    
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }
    
    return chunks.filter(chunk => chunk.length > 50); // Filtrar chunks muito pequenos
  }

  /**
   * Validar embedding
   */
  validateEmbedding(embedding: number[]): boolean {
    if (!Array.isArray(embedding) || embedding.length === 0) {
      return false;
    }

    // Verificar se todos os valores são números válidos
    return embedding.every(value => 
      typeof value === 'number' && 
      !isNaN(value) && 
      isFinite(value)
    );
  }

  /**
   * Obter estatísticas do embedding
   */
  getEmbeddingStats(embedding: number[]): {
    dimensions: number;
    mean: number;
    std: number;
    min: number;
    max: number;
  } {
    const dimensions = embedding.length;
    const mean = embedding.reduce((sum, val) => sum + val, 0) / dimensions;
    const variance = embedding.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / dimensions;
    const std = Math.sqrt(variance);
    const min = Math.min(...embedding);
    const max = Math.max(...embedding);

    return { dimensions, mean, std, min, max };
  }
}

// Instância singleton
export const embeddingService = new EmbeddingService();
