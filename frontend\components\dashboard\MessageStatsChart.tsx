
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { ChartDataPoint } from '../../types';

interface MessageStatsChartProps {
  data: ChartDataPoint[]; // Using generic ChartDataPoint
}

const MessageStatsChart: React.FC<MessageStatsChartProps> = ({ data }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <BarChart
        data={data}
        margin={{
          top: 5, right: 30, left: 0, bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis dataKey="name" stroke="#666" />
        <YAxis allowDecimals={false} stroke="#666" />
        <Tooltip wrapperClassName="bg-white !border-gray-300 !shadow-lg !rounded-md" />
        <Legend />
        <Bar dataKey="value" name="Nº de Gestantes" fill="#4CAF50" />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default MessageStatsChart;
    