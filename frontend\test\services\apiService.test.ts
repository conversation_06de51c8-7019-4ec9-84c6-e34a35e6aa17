import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import { toast } from 'react-toastify';

// Mock do axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock do toast
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock do localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('ApiService', () => {
  let mockAxiosInstance: any;
  let apiService: any;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Mock da instância do axios
    mockAxiosInstance = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
    };

    // Configurar o mock antes de importar o apiService
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Importar dinamicamente após configurar o mock
    const module = await import('../../services/apiService');
    apiService = module.apiService;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getPregnantWomen', () => {
    it('deve retornar lista de gestantes com sucesso', async () => {
      const mockData = [
        {
          id: '1',
          name: 'Ana Silva',
          dueDate: '2024-06-15T00:00:00.000Z',
          phone: '11999999999',
          email: '<EMAIL>',
          createdAt: '2024-01-01T00:00:00.000Z',
          age: 28,
        },
      ];

      mockAxiosInstance.get.mockResolvedValue({ data: mockData });

      const result = await apiService.getPregnantWomen();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/pregnant-women');
      expect(result).toEqual(mockData);
    });

    it('deve lançar erro quando dados são inválidos', async () => {
      const invalidData = [
        {
          id: '1',
          name: '', // Nome inválido
          dueDate: 'invalid-date',
          phone: 'invalid-phone',
        },
      ];

      mockAxiosInstance.get.mockResolvedValue({ data: invalidData });

      await expect(apiService.getPregnantWomen()).rejects.toThrow(
        'Dados inválidos recebidos do servidor'
      );
    });
  });

  describe('createPregnantWoman', () => {
    it('deve criar gestante com sucesso', async () => {
      const newPregnant = {
        name: 'Maria Santos',
        dueDate: '2024-08-15T00:00:00.000Z',
        phone: '11888888888',
        email: '<EMAIL>',
        age: 25,
      };

      const createdPregnant = {
        id: '2',
        ...newPregnant,
        createdAt: '2024-01-01T00:00:00.000Z',
      };

      mockAxiosInstance.post.mockResolvedValue({ data: createdPregnant });

      const result = await apiService.createPregnantWoman(newPregnant);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/pregnant-women', newPregnant);
      expect(result).toEqual(createdPregnant);
      expect(toast.success).toHaveBeenCalledWith('Gestante cadastrada com sucesso!');
    });
  });

  describe('getDashboardMetrics', () => {
    it('deve retornar métricas do dashboard', async () => {
      const mockBackendData = {
        totalContacts: 25,
        babyGenders: { male: 10, female: 8, unknown: 7 },
        messages: { total: 150, today: 5 }
      };

      const expectedMetrics = {
        totalPregnantWomen: 25,
        avgWeeksGestation: 0,
        messagesSentThisMonth: 150,
        activeConversations: 25,
      };

      mockAxiosInstance.get.mockResolvedValue({ data: mockBackendData });

      const result = await apiService.getDashboardMetrics();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/analytics/dashboard');
      expect(result).toEqual(expectedMetrics);
    });

    it('deve validar dados das métricas', async () => {
      const invalidMetrics = {
        totalPregnantWomen: -5, // Valor inválido
        avgWeeksGestation: 50, // Valor inválido
        messagesSentThisMonth: 'invalid', // Tipo inválido
      };

      mockAxiosInstance.get.mockResolvedValue({ data: invalidMetrics });

      await expect(apiService.getDashboardMetrics()).rejects.toThrow(
        'Dados inválidos recebidos do servidor'
      );
    });
  });

  describe('sendMessage', () => {
    it('deve enviar mensagem com sucesso', async () => {
      const pregnantWomanId = '1';
      const text = 'Olá! Como você está se sentindo hoje?';
      
      const sentMessage = {
        id: 'msg-1',
        sender: 'user' as const,
        text,
        timestamp: '2024-01-01T12:00:00.000Z',
        status: 'sent' as const,
      };

      mockAxiosInstance.post.mockResolvedValue({ data: sentMessage });

      const result = await apiService.sendMessage(pregnantWomanId, text);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/messages/send', {
        pregnantWomanId,
        text,
      });
      expect(result).toEqual(sentMessage);
    });
  });

  describe('healthCheck', () => {
    it('deve retornar status de saúde', async () => {
      const healthData = {
        status: 'ok',
        timestamp: '2024-01-01T12:00:00.000Z',
      };

      mockAxiosInstance.get.mockResolvedValue({ data: healthData });

      const result = await apiService.healthCheck();

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/health');
      expect(result).toEqual(healthData);
    });
  });
});
