/**
 * Teste Simples da Integração RAG + WhatsApp
 */

const axios = require('axios');

async function testRAGIntegration() {
  try {
    console.log('🧪 Testando Integração RAG + WhatsApp...\n');

    // Simular mensagens que um usuário enviaria via WhatsApp
    const testMessages = [
      {
        message: 'Como solicitar poda de árvore na minha rua?',
        expectedCategory: 'infrastructure',
        description: 'Pergunta sobre infraestrutura urbana'
      },
      {
        message: 'Preciso marcar uma consulta médica no posto de saúde',
        expectedCategory: 'health',
        description: 'Solicitação de serviço de saúde'
      },
      {
        message: 'Quais são os projetos da vereadora Rafaela?',
        expectedCategory: 'legislation',
        description: 'Consulta sobre legislação/projetos'
      },
      {
        message: 'Quando será a próxima audiência pública sobre o orçamento?',
        expectedCategory: 'transparency',
        description: 'Pergunta sobre transparência'
      },
      {
        message: 'Como fazer matrícula do meu filho na escola?',
        expectedCategory: 'education',
        description: 'Consulta sobre educação'
      }
    ];

    console.log(`📋 Testando ${testMessages.length} cenários de WhatsApp...\n`);

    for (let i = 0; i < testMessages.length; i++) {
      const test = testMessages[i];
      
      console.log(`🔍 Teste ${i + 1}: ${test.description}`);
      console.log(`   📝 Mensagem: "${test.message}"`);
      console.log(`   🎯 Categoria esperada: ${test.expectedCategory}`);
      
      try {
        // 1. Testar classificação de intenção
        console.log('   🧠 Classificando intenção...');
        const classificationResponse = await axios.post('http://localhost:3001/api/knowledge-base/classify-intent', {
          query: test.message,
          threshold: 0.6,
          includeReasoning: true,
          suggestActions: true
        });

        if (classificationResponse.data.success) {
          const classification = classificationResponse.data.classification;
          const isCorrect = classification.intent.category === test.expectedCategory;
          
          console.log(`      ✅ Categoria: ${classification.intent.category} ${isCorrect ? '✓' : '✗'}`);
          console.log(`      📊 Confiança: ${(classification.confidence * 100).toFixed(1)}%`);
          console.log(`      🤖 Requer humano: ${classification.requiresHuman ? 'Sim' : 'Não'}`);
        }

        // 2. Testar consulta RAG
        console.log('   📚 Buscando na base de conhecimento...');
        const ragResponse = await axios.post('http://localhost:3001/api/knowledge-base/rag/query', {
          question: test.message,
          maxResults: 3,
          threshold: 0.6
        });

        if (ragResponse.data.success) {
          const rag = ragResponse.data.response;
          console.log(`      📊 Confiança RAG: ${(rag.confidence * 100).toFixed(1)}%`);
          console.log(`      📚 Fontes encontradas: ${rag.sources.length}`);
          console.log(`      💬 Resposta: "${rag.answer.substring(0, 100)}..."`);
        }

        // 3. Testar consulta inteligente (classificação + RAG)
        console.log('   🚀 Testando consulta inteligente...');
        const smartResponse = await axios.post('http://localhost:3001/api/knowledge-base/smart-query', {
          question: test.message,
          classifyFirst: true,
          maxResults: 3,
          threshold: 0.6
        });

        if (smartResponse.data.success) {
          const smart = smartResponse.data;
          console.log(`      🎯 Categoria detectada: ${smart.classification.intent.category}`);
          console.log(`      📊 Confiança total: ${(smart.ragResponse.confidence * 100).toFixed(1)}%`);
          console.log(`      ⚠️ Requer humano: ${smart.smartSuggestions.requiresHuman ? 'Sim' : 'Não'}`);
          console.log(`      💡 Ações sugeridas: ${smart.smartSuggestions.recommendedActions.length}`);
          
          // Esta seria a resposta que o WhatsApp receberia
          console.log(`      🤖 Resposta final: "${smart.ragResponse.answer.substring(0, 150)}..."`);
        }

      } catch (error) {
        console.log(`   ❌ Erro: ${error.response?.data?.error || error.message}`);
      }
      
      console.log(''); // Linha em branco
      
      // Delay entre testes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Teste de performance
    console.log('⚡ Teste de Performance do Sistema RAG:\n');
    
    const performanceTest = 'Como solicitar poda de árvore?';
    const iterations = 3;
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
      console.log(`   🔄 Iteração ${i + 1}/${iterations}...`);
      
      const startTime = Date.now();
      
      try {
        const response = await axios.post('http://localhost:3001/api/knowledge-base/smart-query', {
          question: performanceTest,
          classifyFirst: true,
          maxResults: 3,
          threshold: 0.6
        });
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        times.push(duration);
        
        console.log(`      ⏱️ Tempo: ${duration}ms`);
        
        if (response.data.success) {
          console.log(`      📊 Confiança: ${(response.data.ragResponse.confidence * 100).toFixed(1)}%`);
        }
        
      } catch (error) {
        console.log(`      ❌ Erro: ${error.message}`);
      }
    }
    
    if (times.length > 0) {
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);
      
      console.log(`\n   📊 Estatísticas de Performance:`);
      console.log(`      - Tempo médio: ${avgTime.toFixed(0)}ms`);
      console.log(`      - Tempo mínimo: ${minTime}ms`);
      console.log(`      - Tempo máximo: ${maxTime}ms`);
      console.log(`      - Performance: ${avgTime < 3000 ? '✅ Boa' : avgTime < 5000 ? '⚠️ Aceitável' : '❌ Lenta'}`);
    }

    // Teste de estatísticas do sistema
    console.log('\n📊 Estatísticas do Sistema:\n');
    
    try {
      // Estatísticas da base de conhecimento
      const kbStats = await axios.get('http://localhost:3001/api/knowledge-base/stats');
      if (kbStats.data.success) {
        console.log('   📚 Base de Conhecimento:');
        console.log(`      - Total de documentos: ${kbStats.data.stats.totalDocuments}`);
        console.log(`      - Tipos: ${Object.keys(kbStats.data.stats.documentsByType).length}`);
        console.log(`      - Categorias: ${Object.keys(kbStats.data.stats.documentsByCategory).length}`);
      }

      // Estatísticas de intenções
      const intentStats = await axios.get('http://localhost:3001/api/knowledge-base/intents');
      if (intentStats.data.success) {
        console.log('\n   🎯 Sistema de Intenções:');
        console.log(`      - Total de intenções: ${intentStats.data.statistics.totalIntents}`);
        console.log(`      - Categorias cobertas: ${Object.keys(intentStats.data.statistics.intentsByCategory).length}`);
      }

    } catch (error) {
      console.log(`   ❌ Erro ao obter estatísticas: ${error.message}`);
    }

    console.log('\n🎉 Teste de Integração RAG + WhatsApp concluído!');
    console.log('\n📋 Resumo:');
    console.log('   ✅ Sistema de classificação de intenções funcionando');
    console.log('   ✅ Base de conhecimento RAG operacional');
    console.log('   ✅ Consulta inteligente (classificação + RAG) integrada');
    console.log('   ✅ APIs prontas para integração com WhatsApp');
    console.log('\n🚀 O chatbot agora pode usar a base de conhecimento para responder!');

  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message);
  }
}

testRAGIntegration();
