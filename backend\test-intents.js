/**
 * Teste do Sistema de Classificação de Intenções
 */

const axios = require('axios');

async function testIntentClassification() {
  try {
    console.log('🎯 Testando Sistema de Classificação de Intenções...');

    const testQueries = [
      {
        query: 'Como marcar uma consulta médica no posto de saúde?',
        expectedCategory: 'health'
      },
      {
        query: 'Preciso fazer matrícula do meu filho na escola',
        expectedCategory: 'education'
      },
      {
        query: 'Tem um buraco enorme na minha rua que precisa ser consertado',
        expectedCategory: 'infrastructure'
      },
      {
        query: 'Quais projetos a vereadora Rafaela apresentou este ano?',
        expectedCategory: 'legislation'
      },
      {
        query: 'Quando será a próxima audiência pública sobre o orçamento?',
        expectedCategory: 'transparency'
      },
      {
        query: 'Qual o telefone do gabinete da vereadora?',
        expectedCategory: 'other'
      }
    ];

    console.log(`\n📋 Testando ${testQueries.length} consultas...\n`);

    for (let i = 0; i < testQueries.length; i++) {
      const test = testQueries[i];
      
      console.log(`🔍 Teste ${i + 1}: "${test.query}"`);
      
      try {
        const response = await axios.post('http://localhost:3001/api/knowledge-base/classify-intent', {
          query: test.query,
          threshold: 0.6,
          includeReasoning: true,
          suggestActions: true
        });

        const result = response.data;
        
        if (result.success) {
          const classification = result.classification;
          const isCorrect = classification.intent.category === test.expectedCategory;
          
          console.log(`   ✅ Categoria: ${classification.intent.category} ${isCorrect ? '✓' : '✗ (esperado: ' + test.expectedCategory + ')'}`);
          console.log(`   📊 Confiança: ${(classification.confidence * 100).toFixed(1)}%`);
          console.log(`   🎯 Intenção: ${classification.intent.name}`);
          console.log(`   🤖 Requer humano: ${classification.requiresHuman ? 'Sim' : 'Não'}`);
          console.log(`   💡 Ações: ${classification.suggestedActions.length} sugeridas`);
          
          if (classification.reasoning) {
            console.log(`   🧠 Raciocínio: ${classification.reasoning.substring(0, 100)}...`);
          }
        } else {
          console.log(`   ❌ Erro: ${result.error}`);
        }
      } catch (error) {
        console.log(`   ❌ Erro na requisição: ${error.message}`);
      }
      
      console.log(''); // Linha em branco
      
      // Delay entre testes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Teste da consulta inteligente
    console.log('🧠 Testando Consulta Inteligente (Classificação + RAG)...\n');
    
    try {
      const smartResponse = await axios.post('http://localhost:3001/api/knowledge-base/smart-query', {
        question: 'Como solicitar poda de árvore na minha rua?',
        classifyFirst: true,
        maxResults: 3,
        threshold: 0.6
      });

      const smartResult = smartResponse.data;
      
      if (smartResult.success) {
        console.log('📋 Consulta Inteligente - Resultados:');
        console.log(`   🎯 Categoria: ${smartResult.classification.intent.category}`);
        console.log(`   📊 Confiança Classificação: ${(smartResult.classification.confidence * 100).toFixed(1)}%`);
        console.log(`   🤖 Confiança RAG: ${(smartResult.ragResponse.confidence * 100).toFixed(1)}%`);
        console.log(`   📚 Fontes RAG: ${smartResult.ragResponse.sources.length}`);
        console.log(`   ⚠️ Requer Humano: ${smartResult.smartSuggestions.requiresHuman ? 'Sim' : 'Não'}`);
        console.log(`   💡 Ações Recomendadas: ${smartResult.smartSuggestions.recommendedActions.length}`);
        
        if (smartResult.ragResponse.answer) {
          console.log(`   💬 Resposta: ${smartResult.ragResponse.answer.substring(0, 200)}...`);
        }
      }
    } catch (error) {
      console.log(`❌ Erro na consulta inteligente: ${error.message}`);
    }

    // Teste de estatísticas
    console.log('\n📊 Testando Estatísticas de Intenções...\n');
    
    try {
      const statsResponse = await axios.get('http://localhost:3001/api/knowledge-base/intents');
      const stats = statsResponse.data;
      
      if (stats.success) {
        console.log('📈 Estatísticas das Intenções:');
        console.log(`   📋 Total de Intenções: ${stats.statistics.totalIntents}`);
        console.log('   🏷️ Por Categoria:');
        
        Object.entries(stats.statistics.intentsByCategory).forEach(([category, count]) => {
          console.log(`      - ${category}: ${count} intenções`);
        });
        
        console.log('\n   📝 Intenções Disponíveis:');
        stats.statistics.availableIntents.forEach((intent, index) => {
          console.log(`      ${index + 1}. ${intent.name} (${intent.category})`);
        });
      }
    } catch (error) {
      console.log(`❌ Erro ao obter estatísticas: ${error.message}`);
    }

    console.log('\n🎉 Testes de Classificação de Intenções concluídos!');

  } catch (error) {
    console.error('❌ Erro geral nos testes:', error);
  }
}

testIntentClassification();
