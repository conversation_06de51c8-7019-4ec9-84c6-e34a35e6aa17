/**
 * Serviço de Re-ranking para Sistema RAG
 * Melhora a relevância dos resultados usando múltiplas estratégias
 */

import { SearchResult } from './simpleVectorDB';
import { geminiService } from './gemini';

export interface RerankingOptions {
  query: string;
  results: SearchResult[];
  topK?: number;
  useSemanticRerank?: boolean;
  useLengthPenalty?: boolean;
  useRecencyBoost?: boolean;
  useTypeBoost?: boolean;
}

export interface RerankingResult {
  rerankedResults: SearchResult[];
  originalOrder: number[];
  rerankingScores: number[];
  processingTime: number;
  strategy: string;
}

export class RerankingService {
  
  /**
   * Re-ranking principal dos resultados
   */
  async rerankResults(options: RerankingOptions): Promise<RerankingResult> {
    const startTime = Date.now();
    const {
      query,
      results,
      topK = 5,
      useSemanticRerank = true,
      useLengthPenalty = true,
      useRecencyBoost = true,
      useTypeBoost = true
    } = options;

    if (results.length === 0) {
      return {
        rerankedResults: [],
        originalOrder: [],
        rerankingScores: [],
        processingTime: 0,
        strategy: 'empty'
      };
    }

    console.log(`🔄 Re-ranking ${results.length} resultados para: "${query}"`);

    // 1. Calcular scores combinados
    const scoredResults = await Promise.all(
      results.map(async (result, index) => {
        let combinedScore = result.similarity;

        // Score semântico via Gemini (se habilitado)
        if (useSemanticRerank) {
          const semanticScore = await this.calculateSemanticRelevance(query, result.content);
          combinedScore = (combinedScore * 0.6) + (semanticScore * 0.4);
        }

        // Penalidade por comprimento
        if (useLengthPenalty) {
          const lengthScore = this.calculateLengthScore(result.content);
          combinedScore *= lengthScore;
        }

        // Boost por recência
        if (useRecencyBoost) {
          const recencyScore = this.calculateRecencyScore(result.metadata.uploadedAt);
          combinedScore *= recencyScore;
        }

        // Boost por tipo de documento
        if (useTypeBoost) {
          const typeScore = this.calculateTypeScore(result.metadata.documentType, query);
          combinedScore *= typeScore;
        }

        return {
          ...result,
          originalIndex: index,
          combinedScore
        };
      })
    );

    // 2. Ordenar por score combinado
    const rerankedResults = scoredResults
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, topK)
      .map(({ originalIndex, combinedScore, ...result }) => ({
        ...result,
        similarity: combinedScore // Atualizar similarity com score combinado
      }));

    const processingTime = Date.now() - startTime;
    console.log(`✅ Re-ranking concluído em ${processingTime}ms`);

    return {
      rerankedResults,
      originalOrder: scoredResults.map(r => r.originalIndex),
      rerankingScores: scoredResults.map(r => r.combinedScore),
      processingTime,
      strategy: 'combined'
    };
  }

  /**
   * Calcular relevância semântica usando Gemini
   */
  private async calculateSemanticRelevance(query: string, content: string): Promise<number> {
    try {
      const prompt = `
Avalie a relevância do seguinte conteúdo para a pergunta do usuário.
Retorne apenas um número de 0.0 a 1.0 (onde 1.0 = muito relevante, 0.0 = irrelevante).

Pergunta: "${query}"

Conteúdo: "${content.substring(0, 500)}..."

Relevância (0.0-1.0):`;

      const response = await geminiService.generateRAGResponse(prompt);
      const score = parseFloat(response.trim());
      
      return isNaN(score) ? 0.5 : Math.max(0, Math.min(1, score));
    } catch (error) {
      console.warn('⚠️ Erro no re-ranking semântico:', error);
      return 0.5; // Score neutro em caso de erro
    }
  }

  /**
   * Score baseado no comprimento do conteúdo
   */
  private calculateLengthScore(content: string): number {
    const length = content.length;
    
    // Penalizar conteúdos muito curtos ou muito longos
    if (length < 50) return 0.7; // Muito curto
    if (length > 2000) return 0.8; // Muito longo
    if (length >= 100 && length <= 800) return 1.0; // Tamanho ideal
    
    return 0.9; // Tamanho aceitável
  }

  /**
   * Score baseado na recência do documento
   */
  private calculateRecencyScore(uploadedAt: string): number {
    try {
      const uploadDate = new Date(uploadedAt);
      const now = new Date();
      const daysDiff = (now.getTime() - uploadDate.getTime()) / (1000 * 60 * 60 * 24);
      
      // Boost para documentos mais recentes
      if (daysDiff <= 7) return 1.1; // Última semana
      if (daysDiff <= 30) return 1.05; // Último mês
      if (daysDiff <= 90) return 1.0; // Últimos 3 meses
      if (daysDiff <= 365) return 0.95; // Último ano
      
      return 0.9; // Mais antigo
    } catch (error) {
      return 1.0; // Score neutro se não conseguir calcular
    }
  }

  /**
   * Score baseado no tipo de documento
   */
  private calculateTypeScore(documentType: string, query: string): number {
    const queryLower = query.toLowerCase();
    
    // Boost baseado no tipo de pergunta
    if (queryLower.includes('projeto') || queryLower.includes('lei')) {
      return documentType === 'project' || documentType === 'legislation' ? 1.2 : 1.0;
    }
    
    if (queryLower.includes('serviço') || queryLower.includes('como')) {
      return documentType === 'service' || documentType === 'faq' ? 1.2 : 1.0;
    }
    
    if (queryLower.includes('agenda') || queryLower.includes('quando')) {
      return documentType === 'agenda' ? 1.2 : 1.0;
    }
    
    return 1.0; // Score neutro
  }

  /**
   * Re-ranking rápido sem Gemini (para performance)
   */
  async fastRerank(query: string, results: SearchResult[], topK: number = 5): Promise<SearchResult[]> {
    if (results.length === 0) return [];

    const scoredResults = results.map((result, index) => {
      let score = result.similarity;
      
      // Aplicar apenas scores rápidos
      score *= this.calculateLengthScore(result.content);
      score *= this.calculateRecencyScore(result.metadata.uploadedAt);
      score *= this.calculateTypeScore(result.metadata.documentType, query);
      
      return { ...result, similarity: score };
    });

    return scoredResults
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  }

  /**
   * Diversificar resultados para evitar redundância
   */
  diversifyResults(results: SearchResult[], maxSimilarContent: number = 2): SearchResult[] {
    const diversified: SearchResult[] = [];
    const contentHashes = new Set<string>();

    for (const result of results) {
      // Criar hash simples do conteúdo
      const contentHash = this.simpleHash(result.content.substring(0, 200));
      
      // Contar quantos resultados similares já temos
      const similarCount = Array.from(contentHashes).filter(hash => 
        this.hammingDistance(hash, contentHash) < 3
      ).length;

      if (similarCount < maxSimilarContent) {
        diversified.push(result);
        contentHashes.add(contentHash);
      }
    }

    return diversified;
  }

  /**
   * Hash simples para comparação de conteúdo
   */
  private simpleHash(text: string): string {
    return text.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 50);
  }

  /**
   * Distância de Hamming para comparar hashes
   */
  private hammingDistance(str1: string, str2: string): number {
    const maxLen = Math.max(str1.length, str2.length);
    let distance = Math.abs(str1.length - str2.length);
    
    for (let i = 0; i < Math.min(str1.length, str2.length); i++) {
      if (str1[i] !== str2[i]) distance++;
    }
    
    return distance;
  }
}

// Instância singleton
export const rerankingService = new RerankingService();
