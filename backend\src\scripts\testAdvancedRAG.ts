/**
 * Script de teste completo para o sistema RAG avançado
 */

import { ragService } from '../services/ragService';
import { chromaVectorDB } from '../services/chromaVectorDB';
import { hybridEmbeddingService } from '../services/hybridEmbeddings';
import { feedbackLearningService } from '../services/feedbackLearning';
import { incrementalIndexingService } from '../services/incrementalIndexing';
import { semanticCache } from '../services/semanticCache';

async function testAdvancedRAGSystem() {
  console.log('🚀 Testando Sistema RAG Avançado');
  console.log('==================================\n');

  // 1. Teste de Conectividade ChromaDB
  console.log('📊 1. Testando ChromaDB');
  console.log('------------------------');
  
  try {
    await chromaVectorDB.initialize();
    const chromaStats = await chromaVectorDB.getStats();
    console.log(`✅ ChromaDB conectado: ${chromaStats.totalDocuments} documentos`);
    console.log(`🔗 Coleção: ${chromaStats.collectionName}`);
  } catch (error) {
    console.log('❌ ChromaDB não disponível, usando sistema simples');
  }

  // 2. Teste de Embeddings Híbridos
  console.log('\n📊 2. Testando Embeddings Híbridos');
  console.log('-----------------------------------');
  
  const testText = 'Como solicitar serviços do gabinete da vereadora?';
  
  try {
    const hybridEmbedding = await hybridEmbeddingService.generateHybridEmbedding(testText);
    console.log(`✅ Embedding híbrido gerado:`);
    console.log(`   Dimensões: ${hybridEmbedding.dimensions}`);
    console.log(`   Modelo: ${hybridEmbedding.model}`);
    console.log(`   Tempo: ${hybridEmbedding.processingTime}ms`);
    console.log(`   Pesos: [${hybridEmbedding.weights.map(w => w.toFixed(2)).join(', ')}]`);
  } catch (error) {
    console.log('❌ Erro nos embeddings híbridos:', error);
  }

  // 3. Teste de Queries RAG Avançadas
  console.log('\n📊 3. Testando Queries RAG Avançadas');
  console.log('------------------------------------');
  
  const testQueries = [
    {
      question: 'Quais são os projetos da vereadora Rafaela?',
      useChromaDB: true,
      useHybridEmbeddings: true,
      sessionId: 'test_session_1'
    },
    {
      question: 'Como solicitar poda de árvore na cidade?',
      useChromaDB: true,
      useHybridEmbeddings: true,
      sessionId: 'test_session_2'
    },
    {
      question: 'Agenda de eventos desta semana',
      useChromaDB: false, // Testar fallback
      useHybridEmbeddings: false,
      sessionId: 'test_session_3'
    }
  ];

  const results = [];

  for (const [index, query] of testQueries.entries()) {
    console.log(`\n🔍 Query ${index + 1}: "${query.question}"`);
    
    try {
      const startTime = Date.now();
      const response = await ragService.processQuery(query);
      const totalTime = Date.now() - startTime;
      
      console.log(`   ⏱️ Tempo total: ${totalTime}ms`);
      console.log(`   🎯 Confiança: ${(response.confidence * 100).toFixed(1)}%`);
      console.log(`   📚 Fontes: ${response.sources.length}`);
      console.log(`   🔄 ChromaDB: ${response.metadata.usedChromaDB ? 'Sim' : 'Não'}`);
      console.log(`   🧠 Embeddings híbridos: ${response.metadata.usedHybridEmbeddings ? 'Sim' : 'Não'}`);
      console.log(`   💾 Cache: ${response.metadata.fromCache ? 'Hit' : 'Miss'}`);
      console.log(`   📈 Query expansion: ${response.metadata.queryExpansion ? 'Sim' : 'Não'}`);
      
      results.push({
        query: query.question,
        response,
        totalTime,
        sessionId: query.sessionId
      });
      
    } catch (error) {
      console.log(`   ❌ Erro: ${error}`);
    }
  }

  // 4. Teste de Cache Semântico
  console.log('\n📊 4. Testando Cache Semântico');
  console.log('------------------------------');
  
  // Repetir primeira query para testar cache
  if (results.length > 0) {
    const firstQuery = testQueries[0];
    console.log(`🔍 Repetindo query: "${firstQuery.question}"`);
    
    const startTime = Date.now();
    const cachedResponse = await ragService.processQuery(firstQuery);
    const cacheTime = Date.now() - startTime;
    
    console.log(`   ⏱️ Tempo com cache: ${cacheTime}ms`);
    console.log(`   💾 Do cache: ${cachedResponse.metadata.fromCache ? 'Sim' : 'Não'}`);
    
    const speedup = results[0].totalTime / cacheTime;
    console.log(`   🚀 Aceleração: ${speedup.toFixed(1)}x`);
  }

  // Estatísticas do cache
  const cacheStats = semanticCache.getStats();
  console.log(`\n📊 Estatísticas do Cache:`);
  console.log(`   📚 Entradas: ${cacheStats.totalEntries}`);
  console.log(`   🎯 Taxa de acerto: ${cacheStats.hitRate}%`);
  console.log(`   ⏱️ Tempo médio: ${cacheStats.averageResponseTime}ms`);

  // 5. Teste de Feedback Learning
  console.log('\n📊 5. Testando Sistema de Feedback');
  console.log('----------------------------------');
  
  // Simular feedback para as queries testadas
  for (const [index, result] of results.entries()) {
    const feedback = {
      sessionId: result.sessionId,
      query: result.query,
      response: result.response.answer,
      sources: result.response.sources.map(s => s.id),
      rating: Math.floor(Math.random() * 3) + 3, // 3-5
      isHelpful: Math.random() > 0.3, // 70% helpful
      responseTime: result.totalTime,
      confidence: result.response.confidence,
      timestamp: new Date().toISOString(),
      metadata: result.response.metadata
    };
    
    try {
      await feedbackLearningService.recordFeedback(feedback);
      console.log(`   ✅ Feedback ${index + 1} registrado (Rating: ${feedback.rating}/5)`);
    } catch (error) {
      console.log(`   ❌ Erro no feedback ${index + 1}:`, error);
    }
  }

  // Obter insights de performance
  try {
    const insights = await feedbackLearningService.getPerformanceInsights();
    console.log(`\n📈 Insights de Performance:`);
    console.log(`   📊 Total de queries: ${insights.totalQueries}`);
    console.log(`   ⭐ Rating médio: ${insights.avgRating.toFixed(1)}/5`);
    console.log(`   ⏱️ Tempo médio: ${insights.avgResponseTime.toFixed(0)}ms`);
    console.log(`   🎯 Taxa de sucesso: ${(insights.successRate * 100).toFixed(1)}%`);
  } catch (error) {
    console.log(`   ❌ Erro ao obter insights:`, error);
  }

  // 6. Teste de Indexação Incremental
  console.log('\n📊 6. Testando Indexação Incremental');
  console.log('------------------------------------');
  
  try {
    const indexingStats = await incrementalIndexingService.getIndexingStats();
    console.log(`   📚 Total de documentos: ${indexingStats.totalDocuments}`);
    console.log(`   🔄 Mudanças pendentes: ${indexingStats.pendingChanges}`);
    console.log(`   📅 Última indexação: ${indexingStats.lastIndexing || 'Nunca'}`);
    
    if (indexingStats.pendingChanges > 0) {
      console.log(`\n🔄 Executando indexação incremental...`);
      const job = await incrementalIndexingService.runIncrementalIndexing();
      console.log(`   ✅ Job concluído: ${job.status}`);
      console.log(`   📊 Processados: ${job.documentsProcessed}`);
      console.log(`   ➕ Adicionados: ${job.documentsAdded}`);
      console.log(`   🔄 Atualizados: ${job.documentsUpdated}`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na indexação:`, error);
  }

  // 7. Benchmark de Performance
  console.log('\n📊 7. Benchmark de Performance');
  console.log('------------------------------');
  
  const benchmarkQueries = [
    'serviços disponíveis',
    'projetos em andamento',
    'como entrar em contato',
    'agenda da vereadora',
    'legislação municipal'
  ];

  const benchmarkResults = [];
  
  for (const query of benchmarkQueries) {
    const start = Date.now();
    try {
      const response = await ragService.processQuery({ 
        question: query,
        useChromaDB: true,
        useHybridEmbeddings: true
      });
      const time = Date.now() - start;
      
      benchmarkResults.push({
        query,
        time,
        confidence: response.confidence,
        sources: response.sources.length,
        fromCache: response.metadata.fromCache
      });
      
    } catch (error) {
      benchmarkResults.push({
        query,
        time: -1,
        confidence: 0,
        sources: 0,
        error: error.message
      });
    }
  }

  // Estatísticas do benchmark
  const validResults = benchmarkResults.filter(r => r.time > 0);
  const avgTime = validResults.reduce((sum, r) => sum + r.time, 0) / validResults.length;
  const avgConfidence = validResults.reduce((sum, r) => sum + r.confidence, 0) / validResults.length;
  const avgSources = validResults.reduce((sum, r) => sum + r.sources, 0) / validResults.length;
  
  console.log(`\n📈 Resultados do Benchmark:`);
  console.log(`   ⏱️ Tempo médio: ${avgTime.toFixed(0)}ms`);
  console.log(`   🎯 Confiança média: ${(avgConfidence * 100).toFixed(1)}%`);
  console.log(`   📚 Fontes médias: ${avgSources.toFixed(1)}`);
  console.log(`   ✅ Queries bem-sucedidas: ${validResults.length}/${benchmarkQueries.length}`);

  // 8. Resumo Final
  console.log('\n🎯 Resumo Final do Teste');
  console.log('========================');
  
  const systemHealth = {
    chromaDB: chromaStats?.isConnected || false,
    hybridEmbeddings: true,
    semanticCache: cacheStats.totalEntries > 0,
    feedbackSystem: true,
    incrementalIndexing: true,
    avgPerformance: avgTime < 3000 ? 'Boa' : avgTime < 5000 ? 'Aceitável' : 'Lenta'
  };

  console.log(`✅ ChromaDB: ${systemHealth.chromaDB ? 'Funcionando' : 'Indisponível'}`);
  console.log(`✅ Embeddings Híbridos: ${systemHealth.hybridEmbeddings ? 'Funcionando' : 'Erro'}`);
  console.log(`✅ Cache Semântico: ${systemHealth.semanticCache ? 'Ativo' : 'Vazio'}`);
  console.log(`✅ Sistema de Feedback: ${systemHealth.feedbackSystem ? 'Funcionando' : 'Erro'}`);
  console.log(`✅ Indexação Incremental: ${systemHealth.incrementalIndexing ? 'Funcionando' : 'Erro'}`);
  console.log(`📊 Performance Geral: ${systemHealth.avgPerformance}`);

  console.log('\n🎉 Teste do Sistema RAG Avançado Concluído!');
}

// Executar se chamado diretamente
if (require.main === module) {
  testAdvancedRAGSystem()
    .then(() => {
      console.log('\n✅ Todos os testes concluídos');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Erro nos testes:', error);
      process.exit(1);
    });
}

export { testAdvancedRAGSystem };
