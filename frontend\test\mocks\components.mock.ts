import { vi } from 'vitest';
import React from 'react';

// Mock para React Router
export const mockNavigate = vi.fn();
export const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  state: null,
  key: 'default',
};

vi.mock('react-router-dom', () => ({
  BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
  Link: ({ children, to, ...props }: any) => 
    React.createElement('a', { href: to, ...props }, children),
}));

// Mock para React Toastify
export const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
};

vi.mock('react-toastify', () => ({
  toast: mockToast,
  ToastContainer: () => React.createElement('div', { 'data-testid': 'toast-container' }),
}));

// Mock para componentes de formulário
export const mockPregnantFormModal = vi.fn(({ isOpen, children, onSave, onClose, contact }) => {
  if (!isOpen) return null;
  
  return React.createElement('div', { 'data-testid': 'pregnant-form-modal' }, [
    React.createElement('h2', { key: 'title' }, contact ? 'Editar Gestante' : 'Cadastrar Gestante'),
    React.createElement('form', {
      key: 'form',
      onSubmit: (e: Event) => {
        e.preventDefault();
        const formData = new FormData(e.target as HTMLFormElement);
        const data = Object.fromEntries(formData.entries());
        onSave(data);
      }
    }, [
      React.createElement('input', {
        key: 'name',
        name: 'name',
        'aria-label': 'Nome',
        defaultValue: contact?.name || '',
        required: true,
      }),
      React.createElement('input', {
        key: 'phone',
        name: 'phone',
        'aria-label': 'Telefone',
        defaultValue: contact?.phone || '',
        required: true,
      }),
      React.createElement('input', {
        key: 'email',
        name: 'email',
        'aria-label': 'Email',
        type: 'email',
        defaultValue: contact?.email || '',
      }),
      React.createElement('button', {
        key: 'save',
        type: 'submit',
      }, 'Salvar'),
      React.createElement('button', {
        key: 'cancel',
        type: 'button',
        onClick: onClose,
      }, 'Cancelar'),
    ]),
  ]);
});

vi.mock('../../../components/pregnant/PregnantFormModal', () => ({
  default: mockPregnantFormModal,
}));

// Mock para modal de confirmação de exclusão
export const mockDeleteConfirmModal = vi.fn(({ isOpen, onConfirm, onClose, contactName }) => {
  if (!isOpen) return null;
  
  return React.createElement('div', { 'data-testid': 'delete-confirm-modal' }, [
    React.createElement('h2', { key: 'title' }, 'Confirmar Exclusão'),
    React.createElement('p', { key: 'message' }, `Tem certeza que deseja excluir a gestante ${contactName}?`),
    React.createElement('p', { key: 'warning' }, 'Esta ação não pode ser desfeita.'),
    React.createElement('button', {
      key: 'confirm',
      onClick: onConfirm,
    }, 'Excluir'),
    React.createElement('button', {
      key: 'cancel',
      onClick: onClose,
    }, 'Cancelar'),
  ]);
});

vi.mock('../../../components/pregnant/DeleteConfirmModal', () => ({
  default: mockDeleteConfirmModal,
}));

// Mock para tabela de gestantes
export const mockPregnantTable = vi.fn(({ contacts, onEdit, onDelete, onSendMessage, onGenerateFollowUp }) => {
  if (contacts.length === 0) {
    return React.createElement('p', {}, 'Nenhuma gestante encontrada.');
  }
  
  return React.createElement('table', { 'data-testid': 'pregnant-table' }, [
    React.createElement('thead', { key: 'head' }, 
      React.createElement('tr', {}, [
        React.createElement('th', { key: 'name' }, 'Nome'),
        React.createElement('th', { key: 'phone' }, 'Telefone'),
        React.createElement('th', { key: 'actions' }, 'Ações'),
      ])
    ),
    React.createElement('tbody', { key: 'body' }, 
      contacts.map((contact: any, index: number) => 
        React.createElement('tr', { key: contact._id || index }, [
          React.createElement('td', { key: 'name' }, contact.name),
          React.createElement('td', { key: 'phone' }, contact.phone),
          React.createElement('td', { key: 'actions' }, [
            React.createElement('button', {
              key: 'edit',
              onClick: () => onEdit(contact),
            }, 'Editar'),
            React.createElement('button', {
              key: 'delete',
              onClick: () => onDelete(contact),
            }, 'Excluir'),
            React.createElement('button', {
              key: 'followup',
              onClick: () => onGenerateFollowUp(contact),
            }, 'Acompanhamento'),
          ]),
        ])
      )
    ),
  ]);
});

vi.mock('../../../components/pregnant/PregnantTable', () => ({
  default: mockPregnantTable,
}));

// Mock para página de listagem de gestantes
export const mockPregnantListPage = vi.fn(() => {
  const [loading, setLoading] = React.useState(true);
  const [contacts, setContacts] = React.useState([]);
  const [error, setError] = React.useState(null);
  
  React.useEffect(() => {
    // Simular carregamento
    setTimeout(() => {
      setLoading(false);
      setContacts([
        { _id: '1', name: 'Ana Silva', phone: '11999999999' },
        { _id: '2', name: 'Maria Santos', phone: '11888888888' },
      ]);
    }, 100);
  }, []);
  
  if (loading) {
    return React.createElement('div', {}, 'Carregando...');
  }
  
  if (error) {
    return React.createElement('div', {}, 'Erro ao carregar gestantes');
  }
  
  return React.createElement('div', { 'data-testid': 'pregnant-list-page' }, [
    React.createElement('h1', { key: 'title' }, 'Gerenciamento de Gestantes'),
    React.createElement('input', {
      key: 'search',
      placeholder: 'Buscar por nome, telefone, email...',
      'data-testid': 'search-input',
    }),
    React.createElement('button', {
      key: 'new',
      'data-testid': 'new-pregnant-button',
    }, 'Nova Gestante'),
    React.createElement(mockPregnantTable, {
      key: 'table',
      contacts,
      onEdit: vi.fn(),
      onDelete: vi.fn(),
      onSendMessage: vi.fn(),
      onGenerateFollowUp: vi.fn(),
    }),
  ]);
});

vi.mock('../../../components/pregnant/PregnantListPage', () => ({
  default: mockPregnantListPage,
}));

// Mock para componentes compartilhados
export const mockButton = vi.fn(({ children, onClick, variant, size, className, ...props }) => 
  React.createElement('button', {
    onClick,
    className: `btn ${variant || 'primary'} ${size || 'md'} ${className || ''}`,
    ...props,
  }, children)
);

vi.mock('../../../components/shared/Button', () => ({
  default: mockButton,
}));

export const mockInput = vi.fn(({ label, error, Icon, ...props }) => 
  React.createElement('div', { className: 'input-group' }, [
    label && React.createElement('label', { key: 'label' }, label),
    React.createElement('input', { key: 'input', ...props }),
    error && React.createElement('span', { key: 'error', className: 'error' }, error),
  ])
);

vi.mock('../../../components/shared/Input', () => ({
  default: mockInput,
}));

export const mockSpinner = vi.fn(() => 
  React.createElement('div', { 'data-testid': 'spinner', className: 'spinner' }, 'Loading...')
);

vi.mock('../../../components/shared/Spinner', () => ({
  default: mockSpinner,
}));

// Função para limpar todos os mocks
export const clearAllMocks = () => {
  mockNavigate.mockClear();
  mockToast.success.mockClear();
  mockToast.error.mockClear();
  mockToast.warning.mockClear();
  mockToast.info.mockClear();
  mockPregnantFormModal.mockClear();
  mockDeleteConfirmModal.mockClear();
  mockPregnantTable.mockClear();
  mockPregnantListPage.mockClear();
  mockButton.mockClear();
  mockInput.mockClear();
  mockSpinner.mockClear();
};

// Função para resetar todos os mocks
export const resetAllMocks = () => {
  mockNavigate.mockReset();
  mockToast.success.mockReset();
  mockToast.error.mockReset();
  mockToast.warning.mockReset();
  mockToast.info.mockReset();
  mockPregnantFormModal.mockReset();
  mockDeleteConfirmModal.mockReset();
  mockPregnantTable.mockReset();
  mockPregnantListPage.mockReset();
  mockButton.mockReset();
  mockInput.mockReset();
  mockSpinner.mockReset();
};
