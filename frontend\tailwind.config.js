/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        // Cores da Rafaela baseadas na identidade visual
        primary: {
          DEFAULT: '#FF8C00', // Laranja principal da Rafael<PERSON> 🧡
          light: '#FFB347',
          dark: '#E67E00',
          50: '#FFF8F0',
          100: '#FFEDD5',
          200: '#FED7AA',
          300: '#FDBA74',
          400: '#FB923C',
          500: '#FF8C00',
          600: '#E67E00',
          700: '#C2610C',
          800: '#9A4D0F',
          900: '#7C3F0F'
        },
        neutral: {
          light: '#F8F9FA',
          DEFAULT: '#6C757D',
          dark: '#343A40'
        },
        // Cores de apoio para o sistema maternal
        maternal: {
          pink: '#FFB6C1',
          blue: '#87CEEB',
          green: '#98FB98',
          purple: '#DDA0DD'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      screens: {
        'xs': '475px',
        '3xl': '1600px',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      }
    },
  },
  plugins: [],
}