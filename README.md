# 🧡 Rafaela Cuida - Sistema Integrado de Gestão Materna

**Sistema completo para acompanhamento e cuidado de gestantes, com personalidade única da Vereadora Rafaela de Parnamirim**

## 📁 Estrutura do Projeto

```
rafaela-cuida/
├── backend/          # API Node.js + TypeScript
│   ├── src/         # Código fonte do backend
│   ├── sql/         # Scripts SQL (Supabase)
│   └── package.json # Dependências do backend
├── frontend/         # Interface React + TypeScript
│   ├── src/         # Código fonte do frontend
│   ├── components/  # Componentes React
│   └── package.json # Dependências do frontend
├── docs/            # Documentação
└── package.json     # Scripts principais do projeto
```

## 🚀 Instalação e Execução

### Pré-requisitos
- Node.js 16+
- npm 8+
- Conta Supabase
- Chave API do Gemini

### Instalação Rápida

```bash
# Clonar o repositório
git clone https://github.com/ItaloCabral1995RN/gest-o-materna-integrada.git
cd gestão-materna-integrada

# Instalar dependências de ambos os projetos
npm run install:all

# Configurar variáveis de ambiente
cp backend/.env.example backend/.env
# Editar backend/.env com suas configurações

# Executar em modo desenvolvimento (backend + frontend)
npm run dev
```

### Scripts Disponíveis

```bash
npm run dev              # Executar backend + frontend
npm run dev:backend      # Executar apenas backend
npm run dev:frontend     # Executar apenas frontend
npm run build            # Build de produção
npm run test             # Executar todos os testes
npm run lint             # Verificar código
```

![Logo Rafaela](LogoRafa.png)

[![Versão](https://img.shields.io/badge/versão-2.0.0-orange.svg)](https://github.com/ItaloCabral1995RN/RAFAELA-FINAL/releases)
[![Testes](https://img.shields.io/badge/testes-91%25-brightgreen.svg)](#testes)
[![Responsivo](https://img.shields.io/badge/responsivo-✓-blue.svg)](#responsividade)
[![Produção](https://img.shields.io/badge/produção-ready-success.svg)](#deploy)

## 🌟 Sobre a Rafaela

Sistema inspirado na **Vereadora Rafaela de Parnamirim** - fonoaudióloga, cristã e defensora da inclusão. O sistema incorpora sua personalidade maternal e acolhedora para cuidar das gestantes com o carinho que elas merecem.

### **Características da Personalidade Implementadas:**
- 🧡 **Tom maternal**: "minha querida", "nossa gente"
- 💪 **Linguagem de força**: "seguimos firmes juntas"
- 🙏 **Elementos de fé**: "Deus abençoa", "com esperança"
- 👥 **Inclusão**: "juntas somos mais fortes"
- 📝 **Respostas concisas**: 15-60 palavras

## 🌟 Funcionalidades Principais

### 📊 **Dashboard Inteligente**
- Métricas em tempo real com dados reais do banco
- Gráficos responsivos de gestantes por gênero do bebê
- Estatísticas de mensagens e conversas ativas
- Monitoramento de saúde do sistema

### 📱 **WhatsApp + IA Personalizada**
- Integração oficial com WppConnect
- IA Gemini com personalidade da Rafaela
- Respostas automáticas contextuais
- Follow-up proativo a cada 15 dias
- Análise de sentimento das mensagens

### 👥 **Gestão Simplificada**
- CRUD de gestantes (Nome, Telefone, Gênero do Bebê)
- Soft delete com reativação automática
- Busca e filtros avançados
- Importação CSV

### 🔧 **Recursos Avançados**
- Sistema de Webhooks completo
- Monitoramento de saúde em tempo real
- Testes de performance para envio em massa
- Rate limiting e segurança robusta
- Logs de auditoria completos

## 📱 Responsividade Completa

✓ **Mobile First** - Otimizado para dispositivos móveis  
✓ **Breakpoints**: 375px, 768px, 1024px, 1600px+  
✓ **Sidebar responsiva** com overlay em mobile  
✓ **Cards adaptativos** (1→2→4 colunas)  
✓ **Botões touch-friendly** (44px+)  
✓ **Gráficos com scroll horizontal**  

## 🧪 Testes Robustos

### **91% de Cobertura (142/156 testes)**
- ✓ **Health Monitor** (25/25) - 100%
- ✓ **Webhooks** (19/19) - 100%
- ✓ **WhatsApp Integration** (14/14) - 100%
- ✓ **Auth** (18/18) - 100%
- ✓ **Analytics** (9/9) - 100%
- ✓ **Messages** (14/14) - 100%
- ✓ **Contacts** (16/16) - 100%
- ✓ **Performance** (9/9) - 100%

### **Testes E2E**
- Responsividade em múltiplos dispositivos
- Fluxos completos de usuário
- Testes de integração
- Validação de formulários

## 🛠️ Tecnologias

### **Backend Robusto**
- **Node.js + TypeScript** - Base sólida e tipada
- **Express.js** - Framework web rápido
- **Supabase (PostgreSQL)** - Banco moderno e escalável
- **JWT** - Autenticação segura
- **WppConnect** - Integração WhatsApp oficial
- **Google Gemini AI** - IA avançada personalizada
- **Jest** - Testes unitários e integração

### **Frontend Responsivo**
- **React + TypeScript** - Interface moderna
- **Vite** - Build tool ultrarrápido
- **Tailwind CSS** - Estilização utilitária
- **React Router** - Navegação SPA
- **Recharts** - Gráficos interativos
- **Playwright** - Testes E2E

## 🚀 Instalação Rápida

### **Pré-requisitos**
- Node.js 18+
- Conta Supabase
- API Key do Google Gemini

### **1. Clone o Repositório**
```bash
git clone https://github.com/ItaloCabral1995RN/RAFAELA-FINAL.git
cd RAFAELA-FINAL
```

### **2. Backend**
```bash
cd backend
npm install
cp .env.example .env
# Configure as variáveis de ambiente
npm run dev
```

### **3. Frontend**
```bash
npm install
npm run dev
```

### **4. Acesse o Sistema**
- Frontend: `http://localhost:5173`
- Backend: `http://localhost:3334`

## 🔧 Configuração

### **Backend (.env)**
```env
# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# IA Gemini
GEMINI_API_KEY=your_gemini_api_key

# JWT
JWT_SECRET=your-jwt-secret-super-seguro-aqui

# Servidor
PORT=3334
NODE_ENV=development
```

### **Frontend (.env)**
```env
VITE_API_URL=http://localhost:3334
```

## 🧪 Executar Testes

### **Backend (91% cobertura)**
```bash
cd backend
npm test                           # Todos os testes
npm test -- --coverage           # Com cobertura
npm test auth                     # Testes específicos
```

### **Frontend**
```bash
npm test                          # Testes unitários
npm run test:e2e                 # Testes E2E
npm run test:responsive         # Testes de responsividade
```

## 🚀 Deploy em Produção

### **Backend**
```bash
npm run build
npm start
```

### **Frontend**
```bash
npm run build
npm run preview
```

## 📊 Monitoramento

### **Health Checks**
- `GET /api/health` - Status básico
- `GET /api/health/detailed` - Status detalhado
- `GET /api/health/live` - Monitoramento em tempo real

### **Métricas Disponíveis**
- Uso de memória e CPU
- Status da conexão WhatsApp
- Fila de webhooks
- Tempo de resposta das APIs

## 🔒 Segurança

- ✓ **Autenticação JWT** com refresh tokens
- ✓ **Rate limiting** contra ataques
- ✓ **Validação rigorosa** de entrada
- ✓ **Logs de auditoria** completos
- ✓ **CORS** configurado
- ✓ **Helmet** para headers de segurança

## 🗺️ Roadmap Futuro

- [ ] **Notificações Push** para alertas críticos
- [ ] **Dashboard visual** para métricas de saúde
- [ ] **Backup automático** de configurações
- [ ] **Interface web** para gerenciar webhooks
- [ ] **Logs estruturados** com ELK Stack
- [ ] **Integração com calendário** para consultas

## 🤝 Contribuição

1. **Fork** o projeto
2. **Crie** uma branch: `git checkout -b feature/nova-funcionalidade`
3. **Commit** suas mudanças: `git commit -m 'Adiciona nova funcionalidade'`
4. **Push** para a branch: `git push origin feature/nova-funcionalidade`
5. **Abra** um Pull Request

## 📄 Licença

Este projeto está sob a licença **MIT**. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🙏 Agradecimentos

Inspirado na dedicação da **Vereadora Rafaela de Parnamirim** em cuidar da nossa gente com amor, fé e inclusão.

> *"Seguimos firmes juntas, cuidando de cada gestante com o carinho que elas merecem!"* 🧡💪🙏

---

**Desenvolvido com 🧡 para cuidar das gestantes de Parnamirim e além!**
#   g e s t - o - m a t e r n a - i n t e g r a d a 
 
 