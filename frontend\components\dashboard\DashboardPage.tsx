
import React from 'react';
import PageTitle from '../shared/PageTitle';
import MetricCard from './MetricCard';
import PregnantWomenChart from './PregnantWomenChart';
import MessageStatsChart from './MessageStatsChart';
import AudioTranscriptionsPanel from './AudioTranscriptionsPanel';
import Spinner from '../shared/Spinner';
import { useDashboard, useDashboardFreshness } from '../../hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const DashboardPage: React.FC = () => {
  const { data, loading, error, refetch, lastUpdated } = useDashboard();
  const isStale = useDashboardFreshness();
  const { metrics, newRegistrations, ageDistribution, recentActivities, topContacts } = data;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error || !metrics) {
    return (
      <div className="text-center">
        <div className="text-red-500 mb-4">
          {error || "Erro ao carregar os dados do dashboard."}
        </div>
        <button
          onClick={refetch}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark"
        >
          Tentar Novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
        <PageTitle title="Dashboard Visão Geral" subtitle="Métricas e estatísticas chave do sistema." />

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
          {lastUpdated && (
            <span className="text-xs sm:text-sm text-gray-500 order-2 sm:order-1">
              Atualizado {formatDistanceToNow(lastUpdated, {
                addSuffix: true,
                locale: ptBR
              })}
            </span>
          )}

          <div className="flex items-center gap-2 order-1 sm:order-2">
            {isStale && (
              <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded whitespace-nowrap">
                Dados desatualizados
              </span>
            )}

            <button
              onClick={refetch}
              className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 whitespace-nowrap"
              disabled={loading}
            >
              {loading ? 'Atualizando...' : 'Atualizar'}
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6">
        <div data-testid="metric-card">
          <MetricCard title="Total de Gestantes" value={metrics.totalPregnantWomen.toString()} />
        </div>
        <div data-testid="metric-card">
          <MetricCard title="Mensagens Totais" value={metrics.messagesSentThisMonth.toString()} />
        </div>
        <div data-testid="metric-card">
          <MetricCard title="Conversas Ativas" value={metrics.activeConversations.toString()} />
        </div>
        <div data-testid="metric-card">
          <MetricCard title="Sistema Ativo" value="✅ Online" />
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6 mb-6">
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow">
          <h3 className="text-base lg:text-lg font-semibold text-neutral-dark mb-4">
            Novas Gestantes Cadastradas (Últimos 6 Meses)
          </h3>
          <div className="overflow-x-auto">
            {newRegistrations.length > 0 ? <PregnantWomenChart data={newRegistrations} /> : <p className="text-gray-500 text-center py-8">Sem dados para exibir.</p>}
          </div>
        </div>
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow">
          <h3 className="text-base lg:text-lg font-semibold text-neutral-dark mb-4">
            Distribuição por Gênero do Bebê
          </h3>
          <div className="overflow-x-auto">
            {ageDistribution.length > 0 ? <MessageStatsChart data={ageDistribution} /> : <p className="text-gray-500 text-center py-8">Sem dados para exibir.</p>}
          </div>
        </div>
      </div>
      

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 lg:gap-6">
        <div className="bg-white p-4 lg:p-6 rounded-lg shadow">
          <h3 className="text-base lg:text-lg font-semibold text-neutral-dark mb-4">Contatos Mais Ativos</h3>
          {topContacts && topContacts.length > 0 ? (
            <ul className="space-y-3">
              {topContacts.map((contact: any, index: number) => (
                <li key={index} className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 bg-gray-50 rounded gap-2">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 text-sm lg:text-base">{contact.name}</div>
                    <div className="text-xs lg:text-sm text-gray-500">{contact.phone}</div>
                    <div className="text-xs text-blue-600 capitalize">{contact.pregnancyStage?.replace('_', ' ')}</div>
                  </div>
                  <div className="text-left sm:text-right">
                    <div className="text-lg font-bold text-primary">{contact.messageCount}</div>
                    <div className="text-xs text-gray-500">mensagens</div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500 text-center py-8">Nenhuma atividade recente encontrada.</p>
          )}
        </div>

        <div className="bg-white p-4 lg:p-6 rounded-lg shadow">
          <h3 className="text-base lg:text-lg font-semibold text-neutral-dark mb-4">Resumo do Sistema</h3>
          <ul className="space-y-3 text-sm">
            <li className="flex justify-between items-center p-3 bg-green-50 rounded">
              <span className="text-green-700 font-medium">Status WhatsApp:</span>
              <span className="font-bold text-green-800">✅ Conectado</span>
            </li>
            <li className="flex justify-between items-center p-3 bg-blue-50 rounded">
              <span className="text-blue-700 font-medium">Total de Gestantes:</span>
              <span className="font-bold text-blue-800">{metrics?.totalPregnantWomen || 0}</span>
            </li>
            <li className="flex justify-between items-center p-3 bg-purple-50 rounded">
              <span className="text-purple-700 font-medium">Mensagens do Mês:</span>
              <span className="font-bold text-purple-800">{metrics?.messagesSentThisMonth || 0}</span>
            </li>
            <li className="flex justify-between items-center p-3 bg-orange-50 rounded">
              <span className="text-orange-700 font-medium">Conversas Ativas:</span>
              <span className="font-bold text-orange-800">{metrics?.activeConversations || 0}</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Painel de Transcrições de Áudio */}
      <div className="grid grid-cols-1 gap-4 lg:gap-6 mt-6">
        <AudioTranscriptionsPanel />
      </div>
    </div>
  );
};

export default DashboardPage;
    