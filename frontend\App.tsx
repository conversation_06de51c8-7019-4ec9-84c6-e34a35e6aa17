import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import Login from './pages/Login';
import { authService } from './services/authService';
import { ErrorBoundary } from './components/ErrorBoundary';
import Sidebar from '../components/shared/Sidebar';
import Navbar from '../components/shared/Navbar';
import DashboardPage from '../components/dashboard/DashboardPage';
import PregnantListPage from '../components/pregnant/PregnantListPage';
import WhatsAppPage from '../components/whatsapp/WhatsAppPage';
import WhatsAppAutoPage from '../components/whatsapp/WhatsAppAutoPage';

import DatabaseViewer from '../components/debug/DatabaseViewer';
import { DEFAULT_API_KEY_NOTICE } from './constants';


const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const isAuthenticated = authService.isAuthenticated();
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};


const MainLayout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [apiKeyStatus, setApiKeyStatus] = useState(DEFAULT_API_KEY_NOTICE);

  useEffect(() => {

    const checkApiKey = () => {
      const apiKey = import.meta.env['VITE_GEMINI_API_KEY'];
      if (apiKey && apiKey !== 'your-gemini-api-key-here') {
        setApiKeyStatus('API Key configurada');
      } else {
        setApiKeyStatus(DEFAULT_API_KEY_NOTICE);
      }
    };

    checkApiKey();
  }, []);

  return (
    <div className="flex h-screen bg-neutral-light relative">

      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      <Sidebar isOpen={isSidebarOpen} setIsOpen={setIsSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        <Navbar toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} apiKeyStatus={apiKeyStatus} />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-neutral-light p-4 lg:p-6">
          <Routes>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/gestantes" element={<PregnantListPage />} />
            <Route path="/whatsapp" element={<WhatsAppPage />} />
            <Route path="/whatsapp-auto" element={<WhatsAppAutoPage />} />
            <Route path="/debug/database" element={<DatabaseViewer />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <Router>
        <Routes>

        <Route
          path="/logout"
          element={<Navigate to="/login" replace />}
        />


        <Route
          path="/login"
          element={<Login />}
        />


        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        />


        <Route
          path="/"
          element={<Navigate to="/login" replace />}
        />
        </Routes>

        <ToastContainer position="top-right" autoClose={3000} />
      </Router>
    </ErrorBoundary>
  );
};

export default App; 