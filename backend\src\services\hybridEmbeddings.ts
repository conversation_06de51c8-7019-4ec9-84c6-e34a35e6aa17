/**
 * Sistema de Embeddings Híbridos
 * Combina múltiplos modelos para melhor representação semântica
 */

import { embeddingService } from './embeddingService';
import { geminiService } from './gemini';

export interface HybridEmbeddingResult {
  primary: number[];
  secondary?: number[];
  combined: number[];
  weights: number[];
  model: string;
  dimensions: number;
  processingTime: number;
}

export interface EmbeddingModel {
  name: string;
  dimensions: number;
  weight: number;
  enabled: boolean;
  generateEmbedding: (text: string) => Promise<number[]>;
}

export class HybridEmbeddingService {
  private models: Map<string, EmbeddingModel> = new Map();
  private defaultWeights: number[] = [0.7, 0.3]; // Gemini primary, secondary model

  constructor() {
    this.initializeModels();
  }

  /**
   * Inicializar modelos de embedding
   */
  private initializeModels(): void {
    // Modelo primário: Gemini text-embedding-004
    this.models.set('gemini-primary', {
      name: 'text-embedding-004',
      dimensions: 768,
      weight: 0.7,
      enabled: true,
      generateEmbedding: async (text: string) => {
        const result = await embeddingService.generateEmbedding(text);
        return result.embedding;
      }
    });

    // Modelo secundário: Gemini text-embedding-004 com prompt diferente
    this.models.set('gemini-contextual', {
      name: 'text-embedding-004-contextual',
      dimensions: 768,
      weight: 0.3,
      enabled: true,
      generateEmbedding: async (text: string) => {
        // Adicionar contexto específico do gabinete
        const contextualText = `Contexto: Gabinete de vereadora, serviços públicos, legislação municipal.
Texto: ${text}`;
        const result = await embeddingService.generateEmbedding(contextualText);
        return result.embedding;
      }
    });
  }

  /**
   * Gerar embedding híbrido
   */
  async generateHybridEmbedding(text: string): Promise<HybridEmbeddingResult> {
    const startTime = Date.now();

    try {
      console.log(`🔄 Gerando embedding híbrido para: "${text.substring(0, 50)}..."`);

      const enabledModels = Array.from(this.models.values()).filter(m => m.enabled);
      
      if (enabledModels.length === 0) {
        throw new Error('Nenhum modelo de embedding habilitado');
      }

      // Gerar embeddings em paralelo
      const embeddingPromises = enabledModels.map(async (model) => {
        try {
          const embedding = await model.generateEmbedding(text);
          return { model: model.name, embedding, weight: model.weight };
        } catch (error) {
          console.warn(`⚠️ Erro no modelo ${model.name}:`, error);
          return null;
        }
      });

      const results = await Promise.all(embeddingPromises);
      const validResults = results.filter(r => r !== null);

      if (validResults.length === 0) {
        throw new Error('Todos os modelos de embedding falharam');
      }

      // Combinar embeddings com pesos
      const combinedEmbedding = this.combineEmbeddings(validResults);
      
      const processingTime = Date.now() - startTime;

      console.log(`✅ Embedding híbrido gerado em ${processingTime}ms`);

      return {
        primary: validResults[0]?.embedding || [],
        secondary: validResults[1]?.embedding,
        combined: combinedEmbedding.embedding,
        weights: combinedEmbedding.weights,
        model: 'hybrid',
        dimensions: combinedEmbedding.embedding.length,
        processingTime
      };

    } catch (error) {
      console.error('❌ Erro ao gerar embedding híbrido:', error);
      
      // Fallback para modelo primário
      const fallback = await embeddingService.generateEmbedding(text);
      return {
        primary: fallback.embedding,
        combined: fallback.embedding,
        weights: [1.0],
        model: 'fallback',
        dimensions: fallback.embedding.length,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Combinar múltiplos embeddings com pesos
   */
  private combineEmbeddings(results: Array<{ model: string; embedding: number[]; weight: number }>): {
    embedding: number[];
    weights: number[];
  } {
    if (results.length === 1) {
      return {
        embedding: results[0].embedding,
        weights: [1.0]
      };
    }

    // Normalizar pesos
    const totalWeight = results.reduce((sum, r) => sum + r.weight, 0);
    const normalizedWeights = results.map(r => r.weight / totalWeight);

    // Verificar se todas as dimensões são iguais
    const dimensions = results[0].embedding.length;
    const allSameDimensions = results.every(r => r.embedding.length === dimensions);

    if (!allSameDimensions) {
      console.warn('⚠️ Dimensões diferentes entre embeddings, usando apenas o primeiro');
      return {
        embedding: results[0].embedding,
        weights: [1.0]
      };
    }

    // Combinar embeddings
    const combinedEmbedding = new Array(dimensions).fill(0);

    for (let i = 0; i < dimensions; i++) {
      for (let j = 0; j < results.length; j++) {
        combinedEmbedding[i] += results[j].embedding[i] * normalizedWeights[j];
      }
    }

    // Normalizar o embedding resultante
    const magnitude = Math.sqrt(combinedEmbedding.reduce((sum, val) => sum + val * val, 0));
    if (magnitude > 0) {
      for (let i = 0; i < combinedEmbedding.length; i++) {
        combinedEmbedding[i] /= magnitude;
      }
    }

    return {
      embedding: combinedEmbedding,
      weights: normalizedWeights
    };
  }

  /**
   * Gerar embeddings especializados por domínio
   */
  async generateDomainSpecificEmbedding(text: string, domain: string): Promise<HybridEmbeddingResult> {
    const domainPrompts = {
      legislation: 'Contexto: Legislação municipal, projetos de lei, normas jurídicas.',
      services: 'Contexto: Serviços públicos municipais, atendimento ao cidadão.',
      projects: 'Contexto: Projetos e iniciativas da vereadora, ações políticas.',
      faq: 'Contexto: Perguntas frequentes, dúvidas comuns dos cidadãos.',
      agenda: 'Contexto: Agenda política, eventos, reuniões, cronograma.'
    };

    const domainContext = domainPrompts[domain as keyof typeof domainPrompts] || '';
    const contextualText = domainContext ? `${domainContext}\nTexto: ${text}` : text;

    return this.generateHybridEmbedding(contextualText);
  }

  /**
   * Calcular similaridade híbrida
   */
  calculateHybridSimilarity(
    embedding1: HybridEmbeddingResult,
    embedding2: HybridEmbeddingResult
  ): number {
    // Usar embedding combinado para cálculo de similaridade
    return embeddingService.calculateCosineSimilarity(
      embedding1.combined,
      embedding2.combined
    );
  }

  /**
   * Otimizar pesos baseado em feedback
   */
  async optimizeWeights(feedbackData: Array<{
    query: string;
    relevantDocs: string[];
    irrelevantDocs: string[];
  }>): Promise<void> {
    console.log('🎯 Otimizando pesos dos embeddings baseado em feedback...');

    // Implementação simplificada - em produção usaria algoritmos mais sofisticados
    let totalRelevanceScore = 0;
    let totalSamples = 0;

    for (const feedback of feedbackData) {
      const queryEmbedding = await this.generateHybridEmbedding(feedback.query);
      
      // Calcular scores para documentos relevantes vs irrelevantes
      for (const relevantDoc of feedback.relevantDocs) {
        const docEmbedding = await this.generateHybridEmbedding(relevantDoc);
        const similarity = this.calculateHybridSimilarity(queryEmbedding, docEmbedding);
        totalRelevanceScore += similarity;
        totalSamples++;
      }
    }

    const avgRelevanceScore = totalSamples > 0 ? totalRelevanceScore / totalSamples : 0;
    
    // Ajustar pesos baseado na performance
    if (avgRelevanceScore < 0.7) {
      // Aumentar peso do modelo contextual se a relevância está baixa
      this.adjustModelWeight('gemini-contextual', 0.1);
      console.log('📈 Peso do modelo contextual aumentado');
    }

    console.log(`✅ Otimização concluída. Score médio de relevância: ${(avgRelevanceScore * 100).toFixed(1)}%`);
  }

  /**
   * Ajustar peso de um modelo
   */
  private adjustModelWeight(modelName: string, adjustment: number): void {
    const model = this.models.get(modelName);
    if (model) {
      model.weight = Math.max(0.1, Math.min(0.9, model.weight + adjustment));
      
      // Renormalizar pesos
      const totalWeight = Array.from(this.models.values())
        .filter(m => m.enabled)
        .reduce((sum, m) => sum + m.weight, 0);
      
      for (const m of this.models.values()) {
        if (m.enabled) {
          m.weight = m.weight / totalWeight;
        }
      }
    }
  }

  /**
   * Configurar modelo
   */
  configureModel(modelName: string, config: Partial<EmbeddingModel>): void {
    const model = this.models.get(modelName);
    if (model) {
      Object.assign(model, config);
      console.log(`⚙️ Modelo ${modelName} configurado`);
    }
  }

  /**
   * Obter estatísticas dos modelos
   */
  getModelStats(): Array<{
    name: string;
    dimensions: number;
    weight: number;
    enabled: boolean;
  }> {
    return Array.from(this.models.values()).map(model => ({
      name: model.name,
      dimensions: model.dimensions,
      weight: model.weight,
      enabled: model.enabled
    }));
  }

  /**
   * Benchmark de modelos
   */
  async benchmarkModels(testQueries: string[]): Promise<{
    modelPerformance: Record<string, number>;
    averageTime: number;
    recommendedWeights: number[];
  }> {
    console.log('🏃‍♂️ Executando benchmark dos modelos...');

    const results: Record<string, number[]> = {};
    const times: number[] = [];

    for (const query of testQueries) {
      const start = Date.now();
      const embedding = await this.generateHybridEmbedding(query);
      const time = Date.now() - start;
      
      times.push(time);
      
      // Simular score de qualidade (em produção seria baseado em métricas reais)
      const qualityScore = Math.random() * 0.3 + 0.7; // 0.7-1.0
      
      if (!results['hybrid']) results['hybrid'] = [];
      results['hybrid'].push(qualityScore);
    }

    const averageTime = times.reduce((sum, t) => sum + t, 0) / times.length;
    const modelPerformance: Record<string, number> = {};

    for (const [model, scores] of Object.entries(results)) {
      modelPerformance[model] = scores.reduce((sum, s) => sum + s, 0) / scores.length;
    }

    console.log(`✅ Benchmark concluído. Tempo médio: ${averageTime.toFixed(0)}ms`);

    return {
      modelPerformance,
      averageTime,
      recommendedWeights: this.defaultWeights
    };
  }
}

// Instância singleton
export const hybridEmbeddingService = new HybridEmbeddingService();
